import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../component/Button_component.dart';
import '../models/header_model.dart';
import '../services/server_service.dart';

/// 页头右侧组件常量配置
class HeaderRightConstants {
  static const double buttonSpacing = 4.0; // 减小按钮间距
  static const double groupSpacing = 8.0; // 减小按钮组间距
  static const double rightIconAreaHeight = 48.0;
  static const double iconRowSpacing = 6.0;
  static const double iconColumnSpacing = 4.0;
  static const double iconSize = 16.0; // 减小图标尺寸以适应更小空间
  
  // 响应式断点
  static const int extraSmallBreakpoint = 360;
  static const int smallBreakpoint = 480;
  static const int mediumBreakpoint = 768;
  
  // 布局模式
  static const int maxButtonsPerRow = 3; // 每排最多3个按钮
  
  // 最小宽度要求
  static const double minWidthForFourButtons = 160.0; // 4个small按钮的最小宽度
  static const double minWidthForThreeButtons = 120.0; // 3个small按钮的最小宽度
  static const double minWidthForTwoButtons = 80.0;    // 2个small按钮的最小宽度
}

/// 页头右侧组件
class HeaderRightComponent extends StatelessWidget {
  final bool showStatusInfo;
  final VoidCallback onRefresh;
  final VoidCallback onReconnect; // 重连回调
  final VoidCallback onLogout;
  final VoidCallback? onVersionUpdate; // 版本更新回调

  const HeaderRightComponent({
    super.key,
    required this.showStatusInfo,
    required this.onRefresh,
    required this.onReconnect, // 必需的重连回调
    required this.onLogout,
    this.onVersionUpdate, // 可选的版本更新回调
  });
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final screenWidth = MediaQuery.of(context).size.width;
        
        // 根据可用宽度和屏幕尺寸选择布局模式
        return _buildAdaptiveLayout(context, availableWidth, screenWidth);
      },
    );
  }
  
  /// 构建自适应布局
  Widget _buildAdaptiveLayout(BuildContext context, double availableWidth, double screenWidth) {
    // 极小屏幕：只显示最重要的按钮
    if (screenWidth < HeaderRightConstants.extraSmallBreakpoint) {
      return _buildExtraSmallLayout(context);
    }
    
    // 小屏幕：紧凑布局
    if (screenWidth < HeaderRightConstants.smallBreakpoint) {
      return _buildCompactLayout(context);
    }
    
    // 中等屏幕：根据可用宽度决定
    if (screenWidth < HeaderRightConstants.mediumBreakpoint) {
      if (availableWidth < HeaderRightConstants.minWidthForTwoButtons) {
        return _buildCompactLayout(context);
      } else {
        return _buildMediumLayout(context);
      }
    }
    
    // 大屏幕：根据可用宽度智能选择
    if (availableWidth >= HeaderRightConstants.minWidthForThreeButtons) {
      return _buildLargeLayout(context);
    } else if (availableWidth >= HeaderRightConstants.minWidthForTwoButtons) {
      return _buildMediumLayout(context);
    } else {
      return _buildCompactLayout(context);
    }
  }
  
  /// 构建大屏幕布局（桌面端）- 水平排列所有按钮
  Widget _buildLargeLayout(BuildContext context) {
    return Container(
      height: HeaderRightConstants.rightIconAreaHeight,
      padding: const EdgeInsets.symmetric(horizontal: 2.0), // 减小内边距
      alignment: Alignment.centerRight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 状态控制按钮组（仅显示/隐藏切换）
          _buildStatusControlGroup(),
          
          SizedBox(width: HeaderRightConstants.groupSpacing),
          
          // 系统操作按钮组
          _buildSystemActionGroup(),
        ],
      ),
    );
  }
  
  /// 构建中等屏幕布局（平板端）- 两排布局或紧凑水平布局
  Widget _buildMediumLayout(BuildContext context) {
    return Container(
      height: HeaderRightConstants.rightIconAreaHeight,
      padding: const EdgeInsets.symmetric(horizontal: 2.0),
      alignment: Alignment.centerRight,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // 第一排：状态控制按钮
          SizedBox(
            height: (HeaderRightConstants.rightIconAreaHeight - HeaderRightConstants.iconRowSpacing) / 2,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatusControlGroup(),
              ],
            ),
          ),
          
          // 第二排：系统操作按钮
          SizedBox(
            height: (HeaderRightConstants.rightIconAreaHeight - HeaderRightConstants.iconRowSpacing) / 2,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildSystemActionGroup(),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建紧凑布局（小屏幕/手机端）
  Widget _buildCompactLayout(BuildContext context) {
    return Container(
      alignment: Alignment.centerRight,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 紧凑操作按钮
          _CompactActionButtons(
            onRefresh: onRefresh,
            onReconnect: onReconnect, // 添加重连回调
            onLogout: onLogout,
            showStatusInfo: showStatusInfo,
            onVersionUpdate: onVersionUpdate,
          ),
        ],
      ),
    );
  }
  
  /// 构建极小屏幕布局（手机小屏）- 只显示最重要的按钮
  Widget _buildExtraSmallLayout(BuildContext context) {
    return Container(
      alignment: Alignment.centerRight,
      child: Consumer<ServerService>(
        builder: (context, serverService, child) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 只显示连接/断开和退出按钮
              Button.create(ButtonConfig.icon(
                _getReconnectIcon(serverService),
                onPressed: serverService.isConnecting ? null : onReconnect, // 连接中时禁用
                type: _getReconnectButtonType(serverService),
                size: ButtonSize.small,
                shape: ButtonShape.circle,
              )),
              const SizedBox(width: 3.0), // 减小间距
              Button.create(ButtonConfig.icon(
                Icons.logout,
                onPressed: onLogout,
                type: ButtonType.danger,
                size: ButtonSize.small,
                shape: ButtonShape.circle,
              )),
            ],
          );
        },
      ),
    );
  }
  
  /// 构建状态控制按钮组 - 仅版本更新按钮
  Widget _buildStatusControlGroup() {
    return Consumer<HeaderModel>(
      builder: (context, headerModel, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 版本更新按钮（仅在有新版本时显示）
            if (headerModel.hasNewVersion && onVersionUpdate != null) ...[
              Button.create(ButtonConfig.icon(
                Icons.system_update,
                onPressed: onVersionUpdate!,
                type: ButtonType.success,
                size: ButtonSize.small,
                shape: ButtonShape.circle,
              )),
            ],
          ],
        );
      },
    );
  }
  
  /// 构建系统操作按钮组 - 透明背景
  Widget _buildSystemActionGroup() {
    return Consumer<ServerService>(
      builder: (context, serverService, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 连接/断开按钮 - 根据连接状态显示不同图标和颜色
            Button.create(ButtonConfig.icon(
              _getReconnectIcon(serverService),
              onPressed: serverService.isConnecting ? null : onReconnect, // 连接中时禁用
              type: _getReconnectButtonType(serverService),
              size: ButtonSize.small,
              shape: ButtonShape.circle,
            )),
            SizedBox(width: HeaderRightConstants.buttonSpacing),
            Button.create(ButtonConfig.icon(
              Icons.logout,
              onPressed: onLogout,
              type: ButtonType.danger,
              size: ButtonSize.small,
              shape: ButtonShape.circle,
            )),
          ],
        );
      },
    );
  }

  /// 获取连接/断开按钮图标
  IconData _getReconnectIcon(ServerService serverService) {
    if (serverService.isConnecting) {
      return Icons.sync; // 连接中显示同步图标
    } else if (serverService.isConnected) {
      return Icons.link_off; // 已连接显示断开链接图标
    } else {
      return Icons.link; // 未连接显示连接图标
    }
  }

  /// 获取连接/断开按钮类型（颜色）
  ButtonType _getReconnectButtonType(ServerService serverService) {
    if (serverService.isConnecting) {
      return ButtonType.primary; // 连接中显示蓝色
    } else if (serverService.isConnected) {
      return ButtonType.warning; // 已连接显示橙色（表示可以断开）
    } else {
      return ButtonType.success; // 未连接显示绿色（表示可以连接）
    }
  }
}

/// 紧凑型操作按钮组件（用于极小屏幕）
class _CompactActionButtons extends StatelessWidget {
  final VoidCallback onRefresh;
  final VoidCallback onReconnect; // 重连回调
  final VoidCallback onLogout;
  final VoidCallback? onVersionUpdate; // 版本更新回调
  final bool showStatusInfo;

  const _CompactActionButtons({
    required this.onRefresh,
    required this.onReconnect, // 必需的重连回调
    required this.onLogout,
    required this.showStatusInfo,
    this.onVersionUpdate, // 可选的版本更新回调
  });
  
  @override
  Widget build(BuildContext context) {
    return Button.create(ButtonConfig.icon(
      Icons.more_vert,
      onPressed: () => _showCompactMenu(context),
      type: ButtonType.primary,
      size: ButtonSize.small,
      shape: ButtonShape.circle,
    ));
  }
  
  /// 显示紧凑菜单
  void _showCompactMenu(BuildContext context) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset.zero, ancestor: overlay),
        button.localToGlobal(button.size.bottomRight(Offset.zero), ancestor: overlay),
      ),
      Offset.zero & overlay.size,
    );

    // 获取HeaderModel来检查是否有新版本
    final headerModel = Provider.of<HeaderModel>(context, listen: false);
    // 获取ServerService来检查连接状态
    final serverService = Provider.of<ServerService>(context, listen: false);
    
    showMenu(
      context: context,
      position: position,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)), // 圆角菜单
      elevation: 8.0, // 增加菜单阴影
      items: [
        // 版本更新选项（仅在有新版本且回调存在时显示）
        if (headerModel.hasNewVersion && onVersionUpdate != null)
          PopupMenuItem(
            value: 'versionUpdate',
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6.0),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Icon(Icons.system_update, size: 18, color: Colors.green),
                ),
                const SizedBox(width: 12),
                const Text('更新版本', style: TextStyle(fontSize: 14)),
              ],
            ),
          ),

        PopupMenuItem(
          value: 'reconnect',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6.0),
                decoration: BoxDecoration(
                  color: _getReconnectMenuColor(serverService).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Icon(
                  _getReconnectMenuIcon(serverService),
                  size: 18,
                  color: _getReconnectMenuColor(serverService)
                ),
              ),
              const SizedBox(width: 12),
              Text(
                _getReconnectMenuText(serverService),
                style: const TextStyle(fontSize: 14)
              ),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'logout',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6.0),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Icon(Icons.logout, size: 18, color: Colors.red),
              ),
              const SizedBox(width: 12),
              const Text('退出登录', style: TextStyle(fontSize: 14)),
            ],
          ),
        ),
      ],
    ).then((value) {
      if (value != null) {
        switch (value) {
          case 'versionUpdate':
            onVersionUpdate?.call();
            break;

          case 'reconnect': // 修改为重连
            onReconnect();
            break;
          case 'refresh':
            onRefresh();
            break;
          case 'logout':
            onLogout();
            break;
        }
      }
    });
  }

  /// 获取连接/断开菜单图标
  IconData _getReconnectMenuIcon(ServerService serverService) {
    if (serverService.isConnecting) {
      return Icons.sync; // 连接中显示同步图标
    } else if (serverService.isConnected) {
      return Icons.link_off; // 已连接显示断开链接图标
    } else {
      return Icons.link; // 未连接显示连接图标
    }
  }

  /// 获取连接/断开菜单颜色
  Color _getReconnectMenuColor(ServerService serverService) {
    if (serverService.isConnecting) {
      return Colors.blue; // 连接中显示蓝色
    } else if (serverService.isConnected) {
      return Colors.orange; // 已连接显示橙色（表示可以断开）
    } else {
      return Colors.green; // 未连接显示绿色（表示可以连接）
    }
  }

  /// 获取连接/断开菜单文本
  String _getReconnectMenuText(ServerService serverService) {
    if (serverService.isConnecting) {
      return '连接中...'; // 连接中状态
    } else if (serverService.isConnected) {
      return '断开连接'; // 已连接状态
    } else {
      return '连接服务器'; // 未连接状态
    }
  }
}