// ignore_for_file: use_super_parameters, library_private_types_in_public_api, unused_import, unused_element

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../component/Button_component.dart';
import '../component/input_component.dart';
import '../component/background_component.dart';
import '../controllers/register_controller.dart';
import '../models/login_model.dart';
import '../models/game_model.dart';
import '../models/auth_model.dart';
import '../services/server_service.dart';

/// 注册页面
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  static const double _cardWidth = 420.0;
  static const double _cardPadding = 24.0;
  static const double _titleFontSize = 28.0;
  static const double _spacing = 16.0;
  static const double _inputSpacing = 24.0;
  static const double _buttonSpacing = 10.0;
  static const double _checkboxSpacing = 8.0;
  
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late RegisterController _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _initController());
  }

  void _initController() {
    if (!mounted) return;
    
    _controller = RegisterController(
      serverService: Provider.of<ServerService>(context, listen: false),
      loginModel: Provider.of<LoginModel>(context, listen: false),
      gameModel: Provider.of<GameModel>(context, listen: false),
      authModel: Provider.of<AuthModel>(context, listen: false),
    );
    
    _controller.addListener(() {
      if (mounted) setState(() {});
    });
    
    setState(() => _isInitialized = true);
  }

  @override
  void dispose() {
    if (_isInitialized) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return BackgroundComponent.createGradientBackground(
      gradientColors: [Colors.green.shade100, Colors.lightGreen.shade50],
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: SingleChildScrollView(
            child: _buildRegisterCard(),
          ),
        ),
      ),
    );
  }

  Widget _buildRegisterCard() {
    return Container(
      width: _cardWidth,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.all(_cardPadding),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildTitle(),
              const SizedBox(height: _inputSpacing),
              _buildInputFields(),
              const SizedBox(height: 12),
              _buildAgreementCheckbox(),
              const SizedBox(height: _inputSpacing),
              _buildRegisterButtons(),
              const SizedBox(height: _spacing),
              _buildBackToLoginButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return const Center(
      child: Text(
        '注册新账号',
        style: TextStyle(
          fontSize: _titleFontSize,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildInputFields() {
    return InputComponent.createMultiInputs(
      inputs: _controller.getInputFields(),
      controllers: _controller.controllers,
      spacing: _inputSpacing,
      passwordVisibleList: _controller.passwordVisibleList,
      onTogglePasswordVisibility: _controller.updatePasswordVisibility,
    );
  }

  Widget _buildAgreementCheckbox() {
    return Row(
      children: [
        Transform.scale(
          scale: 0.8,
          child: Checkbox(
            value: _controller.agreeToTerms,
            onChanged: (value) => _controller.updateAgreeToTerms(value == true),
            activeColor: Theme.of(context).primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        const SizedBox(width: _checkboxSpacing),
        Expanded(child: _buildAgreementText()),
      ],
    );
  }

  Widget _buildAgreementText() {
    return RichText(
      text: const TextSpan(
        style: TextStyle(color: Colors.black87, fontSize: 14),
        children: [
          TextSpan(text: '我已阅读并同意 '),
          TextSpan(text: '用户协议', style: TextStyle(color: Colors.blue)),
          TextSpan(text: ' 和 '),
          TextSpan(text: '隐私政策', style: TextStyle(color: Colors.blue)),
        ],
      ),
    );
  }

  Widget _buildRegisterButtons() {
    return Row(
      children: [
        Expanded(child: _buildRegisterButton(false)),
        const SizedBox(width: _buttonSpacing),
        Expanded(child: _buildRegisterButton(true)),
      ],
    );
  }

  Widget _buildRegisterButton(bool isPro) {
    return Button.create(ButtonConfig.textWithIcon(
      _getRegisterButtonText(isPro),
      _getRegisterButtonIconData(isPro),
      onPressed: _getRegisterButtonOnPressed(isPro),
      type: isPro ? ButtonType.warning : ButtonType.primary,
      size: ButtonSize.large,
      fullWidth: true,
    ));
  }

  VoidCallback? _getRegisterButtonOnPressed(bool isPro) {
    if (_controller.isLoading) return null;
    
    return () {
      if (_formKey.currentState?.validate() == true) {
        isPro ? _controller.handleProRegister(context) : _controller.handleRegister(context);
      }
    };
  }

  String _getRegisterButtonText(bool isPro) {
    if (_controller.isLoading) return '注册中...';
    return isPro ? 'Pro注册' : '普通注册';
  }

  IconData _getRegisterButtonIconData(bool isPro) {
    return isPro ? Icons.star : Icons.person_add;
  }

  Widget _buildBackToLoginButton() {
    return Center(
      child: Button.create(ButtonConfig.text(
        '返回登录',
        onPressed: () => _controller.navigateToLogin(context),
        type: ButtonType.outline,
        size: ButtonSize.small,
      )),
    );
  }
}
