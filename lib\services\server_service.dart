// ignore_for_file: unused_import, unnecessary_import, unnecessary_null_comparison, use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/resource_model.dart';
import '../models/status_bar_model.dart';
import '../models/header_model.dart';
import '../component/message_component.dart';
import '../utils/logger.dart';

/// 消息监听器类型定义 - 接收WebSocket消息的回调函数
typedef MessageListener = void Function(dynamic message);

/// 状态更新回调函数类型定义
typedef StatusUpdateCallback = void Function(Map<String, dynamic> statusData);

/// 服务器连接状态枚举
enum ConnectionState {
  disconnected,  // 断开连接
  connecting,    // 连接中
  connected,     // 已连接
  error,         // 连接错误
}

/// 服务器服务 - 处理与服务器的WebSocket通信
class ServerService extends ChangeNotifier {
  static const String _logTag = 'ServerService';
  static const Duration _defaultHeartbeatInterval = Duration(seconds: 15);
  static const Duration _fastHeartbeatInterval = Duration(milliseconds: 300);
  static const Duration _reconnectDelay = Duration(seconds: 5);
  static const Duration _connectionTimeout = Duration(seconds: 5);
  static const Duration _fastModeTimeout = Duration(seconds: 10);
  
  final Logger _logger = Logger();
  final StatusBarModel _statusBarModel = StatusBarModel();
  final Set<MessageListener> _messageListeners = {};
  final Set<StatusUpdateCallback> _statusUpdateCallbacks = {};
  
  WebSocketChannel? _channel;
  ConnectionState _connectionState = ConnectionState.disconnected;
  String _errorMessage = '';
  String _serverAddress = '';
  String _serverPort = '';
  String _token = '';
  DateTime? _lastHeartbeat;
  Timer? _heartbeatTimer;
  Timer? _fastHeartbeatTimer;
  Timer? _reconnectTimer;
  bool _fastHeartbeatMode = false;
  String? _lastStatusHashCode;
  bool _userDisconnected = false; // 用户主动断开标志

  // 可配置的心跳频率
  Duration _heartbeatInterval = _defaultHeartbeatInterval;
  
  // Getters
  ConnectionState get connectionState => _connectionState;
  String get errorMessage => _errorMessage;
  String get serverAddress => _serverAddress;
  String get serverPort => _serverPort;
  String get token => _token;
  DateTime? get lastHeartbeat => _lastHeartbeat;
  StatusBarModel get statusBarModel => _statusBarModel;
  bool get isConnected => _connectionState == ConnectionState.connected;
  bool get isConnecting => _connectionState == ConnectionState.connecting;
  bool get hasError => _errorMessage.isNotEmpty;
  String get errorText => _errorMessage;
  bool get isFastHeartbeatMode => _fastHeartbeatMode;
  Duration get heartbeatInterval => _heartbeatInterval;
  bool get isUserDisconnected => _userDisconnected;
  
  ServerService() {
    _logger.i(_logTag, '初始化服务器服务');
  }
  
  /// 添加消息监听器
  MessageListener addMessageListener(MessageListener listener) {
    if (!_messageListeners.contains(listener)) {
      _messageListeners.add(listener);
      _logger.i(_logTag, '添加消息监听器，当前数量: ${_messageListeners.length}');
    }
    return listener;
  }
  
  /// 添加状态更新回调
  void addStatusUpdateCallback(StatusUpdateCallback callback) {
    _statusUpdateCallbacks.add(callback);
    _logger.i(_logTag, '添加状态更新回调，当前数量: ${_statusUpdateCallbacks.length}');
  }
  
  /// 移除消息监听器
  void removeMessageListener(MessageListener listener) {
    if (_messageListeners.contains(listener)) {
      _messageListeners.remove(listener);
      _logger.i(_logTag, '移除消息监听器，当前数量: ${_messageListeners.length}');
    }
  }
  
  /// 移除状态更新回调
  void removeStatusUpdateCallback(StatusUpdateCallback callback) {
    _statusUpdateCallbacks.remove(callback);
    _logger.i(_logTag, '移除状态更新回调，当前数量: ${_statusUpdateCallbacks.length}');
  }
  
  /// 清除所有消息监听器
  void clearMessageListeners() {
    _messageListeners.clear();
    _logger.i(_logTag, '已清除所有消息监听器');
  }
  
  /// 连接到服务器
  Future<bool> connect({
    required String serverAddress,
    required String serverPort,
    required String token,
  }) async {
    if (_connectionState == ConnectionState.connected) {
      disconnect();
    }

    _userDisconnected = false; // 重置用户断开标志
    _serverAddress = serverAddress;
    _serverPort = serverPort;
    _token = token;

    _setConnectionState(ConnectionState.connecting);
    
    try {
      final wsUri = Uri.parse('ws://$serverAddress:$serverPort/ws?token=$token');
      _channel = WebSocketChannel.connect(wsUri);
      
      _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnect,
      );
      
      _setConnectionState(ConnectionState.connected);
      
      // 延迟发送初始消息，确保连接完全建立
      Future.delayed(const Duration(milliseconds: 100), () {
        if (_connectionState == ConnectionState.connected) {
          _sendInitialMessage(token);
        }
      });
      
      _startHeartbeat();
      
      _logger.i(_logTag, '成功连接到服务器', {'address': serverAddress, 'port': serverPort});
      return true;
    } catch (e) {
      _setError('连接服务器失败: $e');
      _setConnectionState(ConnectionState.error);
      _startReconnectTimer();
      _logger.e(_logTag, '连接失败', e.toString());
      return false;
    }
  }
  
  /// 面向UI的连接方法
  Future<void> handleConnectService(BuildContext context, String serverAddress, String port, String token) async {
    print('🔗 开始连接服务器: $serverAddress:$port (实例: ${this.hashCode})'); // 强制输出
    _clearError();

    _serverAddress = serverAddress;
    _serverPort = port;
    if (token.isNotEmpty) {
      _token = token;
    }

    _setConnectionState(ConnectionState.connecting);
    
    try {
      if (!_validateConnectionParams(serverAddress, port)) {
        _showConnectionError(context, '服务器地址和端口不能为空');
        return;
      }
      
      if (!_validatePort(port)) {
        _showConnectionError(context, '无效的端口号，必须是1-65535之间的数字');
        return;
      }

      final success = await _connectWithTimeout(serverAddress, port, token);
      _handleConnectionResult(context, success);
    } catch (e) {
      _setError('连接失败: $e');
      _logger.e(_logTag, '服务器连接失败', e.toString());
      
      if (context.mounted) {
        MessageComponent.showIconToast(
          context: context,
          message: '连接失败: $e',
          type: MessageType.error,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }
  
  /// 断开连接
  Future<void> handleDisconnectService(BuildContext context) async {
    _clearError();
    
    try {
      _logger.i(_logTag, '正在断开WebSocket连接');
      disconnect();
      _logger.i(_logTag, '服务器已断开连接');
      
      if (context.mounted) {
        MessageComponent.showIconToast(
          context: context,
          message: '已断开服务器连接',
          type: MessageType.success,
          duration: const Duration(seconds: 2),
        );
      }
    } catch (e) {
      _setError('断开连接失败: $e');
      _logger.e(_logTag, '断开服务器连接失败', e.toString());
      
      if (context.mounted) {
        MessageComponent.showIconToast(
          context: context,
          message: '断开连接失败: $e',
          type: MessageType.error,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }
  
  /// 获取服务器状态描述
  String getServerStatusDescription() {
    if (isConnecting) {
      return isConnected ? '正在断开连接...' : '正在连接...';
    } else if (isConnected) {
      return '已连接到 $_serverAddress:$_serverPort';
    } else if (hasError) {
      return '连接错误: $_errorMessage';
    } else {
      return '未连接';
    }
  }
  
  /// 断开连接
  void disconnect() {
    _userDisconnected = true; // 标记为用户主动断开
    _stopAllTimers();

    if (_channel != null) {
      _channel!.sink.close();
      _channel = null;
    }

    clearMessageListeners();
    _setConnectionState(ConnectionState.disconnected);
    _logger.i(_logTag, '用户主动断开连接并清除所有消息监听器');
  }
  
  /// 发送消息
  void sendMessage(dynamic message) {
    if (_connectionState != ConnectionState.connected || _channel == null) {
      if (_userDisconnected) {
        _setError('发送消息失败: 用户已主动断开连接');
        _logger.w(_logTag, '无法发送消息，用户已主动断开连接', {'state': _connectionState.toString()});
      } else {
        _setError('发送消息失败: 未连接到服务器');
        _logger.w(_logTag, '无法发送消息，WebSocket未连接', {'state': _connectionState.toString()});
      }
      return;
    }
    
    try {
      if (message is Map<String, dynamic>) {
        final jsonStr = jsonEncode(message);
        _channel!.sink.add(jsonStr);
        _logger.i(_logTag, '发送JSON消息', {'action': message['action'], 'length': jsonStr.length});
      } else if (message is String) {
        _channel!.sink.add(message);
        _logger.i(_logTag, '发送字符串消息', {'length': message.length});
      } else {
        _setError('发送消息失败: 不支持的消息类型');
        _logger.e(_logTag, '不支持的消息类型', message.runtimeType.toString());
      }
    } catch (e) {
      _setError('发送消息失败: $e');
      _logger.e(_logTag, '发送消息异常', e.toString());
    }
  }
  
  /// 获取系统资源信息
  void requestSystemResources() {
    sendMessage({
      'action': 'get_system_resources',
      'token': _token,
    });
  }
  
  /// 启用快速心跳模式
  void enableFastHeartbeat() {
    if (_fastHeartbeatMode) return;
    
    _fastHeartbeatMode = true;
    _logger.i(_logTag, '启用快速心跳模式，间隔300ms');
    
    _stopHeartbeat();
    _startFastHeartbeat();
    
    Timer(_fastModeTimeout, () {
      if (_fastHeartbeatMode) {
        disableFastHeartbeat();
      }
    });
  }
  
  /// 停用快速心跳模式
  void disableFastHeartbeat() {
    if (!_fastHeartbeatMode) return;
    
    _fastHeartbeatMode = false;
    _logger.i(_logTag, '停用快速心跳模式，恢复普通心跳');
    
    _stopFastHeartbeat();
    _startHeartbeat();
  }
  
  /// 发送初始连接消息
  void _sendInitialMessage(String token) {
    // 根据API文档，服务器不支持connect操作
    // 直接发送status_bar_query来建立连接和获取状态
    final message = {
      'action': 'status_bar_query',
      'token': token,
    };
    _channel!.sink.add(jsonEncode(message));
    _logger.i(_logTag, '发送初始连接消息');
  }
  
  /// 验证连接参数
  bool _validateConnectionParams(String serverAddress, String port) {
    if (serverAddress.isEmpty || port.isEmpty) {
      _setError('地址和端口不能为空');
      _logger.w(_logTag, '服务器地址和端口不能为空');
      return false;
    }
    return true;
  }
  
  /// 验证端口号
  bool _validatePort(String port) {
    final portInt = int.tryParse(port);
    if (portInt == null || portInt <= 0 || portInt > 65535) {
      _setError('无效的端口号');
      _logger.w(_logTag, '无效的端口号');
      return false;
    }
    return true;
  }
  
  /// 显示连接错误
  void _showConnectionError(BuildContext context, String message) {
    if (context.mounted) {
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.warning,
        duration: const Duration(seconds: 3),
      );
    }
  }
  
  /// 带超时的连接
  Future<bool> _connectWithTimeout(String serverAddress, String port, String token) async {
    bool connectionEstablished = false;
    
    final connectionTimer = Timer(_connectionTimeout, () {
      if (!connectionEstablished) {
        _logger.w(_logTag, 'WebSocket连接超时');
        _setError('连接超时');
        _setConnectionState(ConnectionState.error);
      }
    });
    
    try {
      final success = await connect(
        serverAddress: serverAddress,
        serverPort: port,
        token: token,
      );
      
      connectionTimer.cancel();
      connectionEstablished = true;
      return success;
    } catch (e) {
      connectionTimer.cancel();
      rethrow;
    }
  }
  
  /// 处理连接结果
  void _handleConnectionResult(BuildContext context, bool success) {
    if (!context.mounted) return;
    
    if (success) {
      MessageComponent.showIconToast(
        context: context,
        message: '已成功连接到服务器',
        type: MessageType.success,
        duration: const Duration(seconds: 2),
      );
    } else {
      MessageComponent.showIconToast(
        context: context,
        message: '连接服务器失败: $_errorMessage',
        type: MessageType.error,
        duration: const Duration(seconds: 3),
      );
    }
  }
  
  /// 通知所有消息监听器
  void _notifyMessageListeners(dynamic message) {
    final listenersCopy = Set<MessageListener>.from(_messageListeners);
    
    for (var listener in listenersCopy) {
      try {
        listener(message);
      } catch (e) {
        _logger.e(_logTag, '消息监听器处理失败', e.toString());
      }
    }
  }
  
  /// 处理接收到的消息
  void _handleMessage(dynamic message) {
    try {
      _lastHeartbeat = DateTime.now();
      _notifyMessageListeners(message);
      
      if (message is String) {
        _logger.i(_logTag, '收到消息', {'length': message.length});
        _processJsonMessage(message);
      } else {
        _logger.w(_logTag, '收到非字符串消息', {'type': message.runtimeType});
      }
      
      notifyListeners();
    } catch (e) {
      _logger.e(_logTag, '处理消息错误', e.toString());
    }
  }
  
  /// 处理JSON消息
  void _processJsonMessage(String message) {
    try {
      // 检查空消息或无效消息
      if (message.trim().isEmpty) {
        _logger.d(_logTag, '收到空消息，可能是心跳或连接保持消息');
        return;
      }

      // 尝试解析JSON
      final data = jsonDecode(message) as Map<String, dynamic>;
      final action = data['action'] ?? '';
      final type = data['type'] ?? '';

      if (data.containsKey('status') && data['status'] == 'error') {
        final errorMessage = data['message'] ?? '未知错误';
        _logger.w(_logTag, '收到错误消息', {'error': errorMessage});
        return;
      }

      if (action.isNotEmpty) {
        _handleActionMessage(action, data);
      } else if (type.isNotEmpty) {
        _handleTypeMessage(type, data);
      } else {
        _logger.w(_logTag, '接收到未知类型消息，缺少action或type字段', {'data': data});
      }
    } catch (e) {
      // 更详细的错误处理
      if (e is FormatException) {
        _logger.w(_logTag, 'JSON格式错误，可能是非JSON消息', {
          'message': message.length > 100 ? '${message.substring(0, 100)}...' : message,
          'error': e.toString()
        });
      } else {
        _logger.e(_logTag, 'JSON解析错误', {
          'message': message.length > 100 ? '${message.substring(0, 100)}...' : message,
          'error': e.toString()
        });
      }
    }
  }
  
  /// 处理action类型消息
  void _handleActionMessage(String action, Map<String, dynamic> data) {
    _logger.i(_logTag, '接收到action类型消息', {'action': action});

    if (action == 'status_bar_response' ||
        action == 'status_bar_query' ||
        action.contains('status_bar')) {
      _handleStatusBarQuery(data);
      return;
    }

    if (action == 'heartbeat_response' || action == 'heartbeat') {
      _handleHeartbeat(data);
      return;
    }

    // 其他action消息已经在_handleMessage中通知过监听器了，这里不需要重复通知
    _logger.i(_logTag, 'action消息已转发到监听器', {'action': action});
  }
  
  /// 处理type类型消息
  void _handleTypeMessage(String type, Map<String, dynamic> data) {
    _logger.i(_logTag, '接收到type类型消息', {'type': type});
    
    switch (type) {
      case 'heartbeat':
        _handleHeartbeat(data);
        break;
      case 'system_resources':
        _handleSystemResources(data);
        break;
      case 'status':
      case 'status_update':
      case 'status_query_response':
        _handleStatusBarQuery(data);
        break;
      case 'error':
        _logger.w(_logTag, '处理错误消息', {'message': data['message']});
        _setError(data['message'] ?? '服务器错误');
        break;
      default:
        _logger.i(_logTag, '转发type消息到监听器', {'type': type});
        break;
    }
  }
  
  /// 处理状态栏查询响应
  void _handleStatusBarQuery(Map<String, dynamic> data) {
    try {
      final dataJson = jsonEncode(data);
      final currentHash = dataJson.hashCode.toString();
      
      if (!_fastHeartbeatMode && _lastStatusHashCode == currentHash) {
        _logger.i(_logTag, '跳过重复的状态栏消息（普通模式）');
        return;
      }
      
      if (_fastHeartbeatMode) {
        _logger.i(_logTag, '快速心跳模式，强制处理状态栏消息');
      }
      
      _lastStatusHashCode = currentHash;
      _logger.i(_logTag, '处理状态栏信息');
      
      final responseData = _extractStatusData(data);
      if (responseData != null) {
        _updateStatusBarModel(responseData);
      }
    } catch (e) {
      _logger.e(_logTag, '处理状态栏查询失败', e.toString());
    }
  }
  
  /// 提取状态数据
  Map<String, dynamic>? _extractStatusData(Map<String, dynamic> data) {
    if (data['action'] == 'status_bar_response' && data.containsKey('data')) {
      _logger.i(_logTag, '使用标准响应格式处理状态栏信息');
      return data['data'] as Map<String, dynamic>?;
    } 
    
    if (data['action'] == 'status_bar_query' && data.containsKey('data')) {
      _logger.i(_logTag, '使用查询响应格式处理状态栏信息');
      return data['data'] as Map<String, dynamic>?;
    }
    
    if (data.containsKey('content')) {
      _logger.i(_logTag, '使用旧版content格式处理状态栏信息');
      return data['content'] as Map<String, dynamic>?;
    }
    
    if (data.containsKey('status') && data['status'] == 'success' && data.containsKey('data')) {
      _logger.i(_logTag, '使用带status字段的格式处理状态栏信息');
      return data['data'] as Map<String, dynamic>?;
    }
    
    _logger.i(_logTag, '直接使用整个data对象处理状态栏信息');
    return Map<String, dynamic>.from(data);
  }
  
  /// 更新状态栏模型
  void _updateStatusBarModel(Map<String, dynamic> responseData) {
    _logger.i(_logTag, '开始更新状态栏模型，当前回调数量: ${_statusUpdateCallbacks.length}');
    
    _logger.i(_logTag, '状态栏信息详情', {
      'dbStatus': responseData['dbStatus'],
      'inferenceStatus': responseData['inferenceStatus'],
      'cardKeyStatus': responseData['cardKeyStatus'],
      'keyMouseStatus': responseData['keyMouseStatus'],
      'frameRate': responseData['frameRate'],
    });
    
    // 处理可能的frameRate值类型
    int frameRate = 0;
    if (responseData.containsKey('frameRate')) {
      final dynamic rawFrameRate = responseData['frameRate'];
      if (rawFrameRate is int) {
        frameRate = rawFrameRate;
      } else if (rawFrameRate is double) {
        frameRate = rawFrameRate.toInt();
      } else if (rawFrameRate is String) {
        frameRate = int.tryParse(rawFrameRate) ?? 0;
      }
    }
    
    // 处理可能的width值类型
    int width = 0;
    if (responseData.containsKey('width')) {
      final dynamic rawWidth = responseData['width'];
      if (rawWidth is int) {
        width = rawWidth;
      } else if (rawWidth is double) {
        width = rawWidth.toInt();
      } else if (rawWidth is String) {
        width = int.tryParse(rawWidth) ?? 0;
      }
    }
    
    // 处理可能的height值类型
    int height = 0;
    if (responseData.containsKey('height')) {
      final dynamic rawHeight = responseData['height'];
      if (rawHeight is int) {
        height = rawHeight;
      } else if (rawHeight is double) {
        height = rawHeight.toInt();
      } else if (rawHeight is String) {
        height = int.tryParse(rawHeight) ?? 0;
      }
    }
    
    // 处理版本信息
    String currentVersion = '';
    if (responseData.containsKey('currentVersion')) {
      currentVersion = responseData['currentVersion']?.toString() ?? '';
    }
    
    String latestVersion = '';
    if (responseData.containsKey('latestVersion')) {
      latestVersion = responseData['latestVersion']?.toString() ?? '';
    }
    
    // 构建标准化的状态数据
    final normalizedData = {
      'dbStatus': _parseBooleanStatus(responseData['dbStatus']),
      'inferenceStatus': _parseBooleanStatus(responseData['inferenceStatus']),
      'cardKeyStatus': _parseBooleanStatus(responseData['cardKeyStatus']),
      'keyMouseStatus': _parseBooleanStatus(responseData['keyMouseStatus']),
      'frameRate': frameRate,
      'width': width,
      'height': height,
      'currentVersion': currentVersion,
      'latestVersion': latestVersion,
      'updatedAt': responseData['updatedAt'] ?? DateTime.now().toIso8601String(),
      'createdAt': DateTime.now().toIso8601String(),
    };
    
    _statusBarModel.updateFromJson(normalizedData);
    _lastHeartbeat = DateTime.now();
    _logger.i(_logTag, '状态栏模型已更新');
    
    // 通知所有状态更新回调
    _logger.i(_logTag, '开始通知状态更新回调，回调数量: ${_statusUpdateCallbacks.length}');
    for (final callback in _statusUpdateCallbacks) {
      try {
        _logger.i(_logTag, '调用状态更新回调，数据: $normalizedData');
        callback(normalizedData);
        _logger.i(_logTag, '状态更新回调执行成功');
      } catch (e) {
        _logger.e(_logTag, '状态更新回调执行失败', e.toString());
      }
    }
    _logger.i(_logTag, '所有状态更新回调通知完成');
  }
  
  /// 解析布尔状态值
  bool _parseBooleanStatus(dynamic value) {
    if (value is bool) return value;
    
    if (value is String) {
      final lowerValue = value.toLowerCase();
      return lowerValue == 'true' || 
             lowerValue == 'connected' || 
             lowerValue == 'running' || 
             lowerValue == 'valid' || 
             lowerValue == 'ready' || 
             lowerValue == 'healthy' || 
             lowerValue == 'online' || 
             lowerValue == 'active' || 
             lowerValue == '1' || 
             lowerValue == 'yes' || 
             lowerValue == 'ok';
    }
    
    if (value is num) return value > 0;
    
    _logger.w(_logTag, '无法识别的状态值类型，默认为false', {'type': value.runtimeType});
    return false;
  }
  
  /// 处理心跳消息
  void _handleHeartbeat(Map<String, dynamic> data) {
    _lastHeartbeat = DateTime.now();
    _logger.i(_logTag, '收到心跳响应');
  }
  
  /// 处理系统资源信息
  void _handleSystemResources(Map<String, dynamic> data) {
    _logger.i(_logTag, '收到系统资源信息');
  }
  
  /// 处理连接错误
  void _handleError(error) {
    _logger.e(_logTag, '连接错误', error.toString());
    
    // 延迟处理错误状态更新，避免在构建期间调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setError('连接错误: $error');
      _setConnectionState(ConnectionState.error);
      _startReconnectTimer();
    });
  }
  
  /// 处理连接断开
  void _handleDisconnect() {
    if (_connectionState != ConnectionState.disconnected) {
      _logger.w(_logTag, '连接已断开', {
        'previousState': _connectionState.toString(),
        'serverAddress': _serverAddress,
        'serverPort': _serverPort,
        'lastHeartbeat': _lastHeartbeat?.toIso8601String() ?? 'null'
      });
      
      // 延迟处理断开状态更新，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _setConnectionState(ConnectionState.disconnected);
        _setError('连接已断开');
        
        // 只有在非用户主动断开的情况下才自动重连
        if (_serverAddress.isNotEmpty && _serverPort.isNotEmpty) {
          _logger.i(_logTag, '准备自动重连...');
          _startReconnectTimer();
        } else {
          _logger.w(_logTag, '缺少连接信息，跳过自动重连');
        }
      });
    }
  }
  
  /// 启动心跳检测
  void _startHeartbeat() {
    _stopHeartbeat();
    _logger.i(_logTag, '启动状态栏查询定时器，间隔${_heartbeatInterval.inSeconds}秒');
    
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (_connectionState == ConnectionState.connected && _channel != null) {
        _logger.i(_logTag, '发送状态栏查询请求 (获取页头状态信息)');
        sendMessage({
          'action': 'status_bar_query',
          'token': _token,
        });
      } else {
        _logger.w(_logTag, '状态栏查询请求未发送', {'state': _connectionState.toString()});
      }
    });
  }
  
  /// 停止心跳检测
  void _stopHeartbeat() {
    if (_heartbeatTimer != null) {
      _logger.i(_logTag, '停止状态栏查询定时器');
      _heartbeatTimer?.cancel();
      _heartbeatTimer = null;
    }
  }
  
  /// 启动快速心跳定时器
  void _startFastHeartbeat() {
    _stopFastHeartbeat();
    _logger.i(_logTag, '启动快速状态栏查询定时器，间隔300ms (快速模式)');
    
    _fastHeartbeatTimer = Timer.periodic(_fastHeartbeatInterval, (timer) {
      if (_connectionState == ConnectionState.connected && _channel != null) {
        sendMessage({
          'action': 'status_bar_query',
          'token': _token,
        });
      }
    });
  }
  
  /// 停止快速心跳定时器
  void _stopFastHeartbeat() {
    if (_fastHeartbeatTimer != null) {
      _logger.i(_logTag, '停止快速状态栏查询定时器');
      _fastHeartbeatTimer?.cancel();
      _fastHeartbeatTimer = null;
    }
  }
  
  /// 启动自动重连
  void _startReconnectTimer() {
    _stopReconnectTimer();

    _reconnectTimer = Timer(_reconnectDelay, () {
      if (_connectionState != ConnectionState.connected && !_userDisconnected) {
        _logger.i(_logTag, '尝试重新连接...');
        connect(
          serverAddress: _serverAddress,
          serverPort: _serverPort,
          token: _token,
        );
      } else if (_userDisconnected) {
        _logger.i(_logTag, '用户主动断开，跳过自动重连');
      }
    });
  }
  
  /// 停止自动重连
  void _stopReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }
  
  /// 停止所有定时器
  void _stopAllTimers() {
    _stopHeartbeat();
    _stopFastHeartbeat();
    _stopReconnectTimer();
  }
  
  /// 设置连接状态
  void _setConnectionState(ConnectionState state) {
    final oldState = _connectionState;
    _connectionState = state;
    _logger.i(_logTag, '🔄 连接状态变化', {
      'from': oldState.toString(),
      'to': state.toString(),
      'isConnected': isConnected,
      'isConnecting': isConnecting,
      'serverAddress': _serverAddress,
      'serverPort': _serverPort,
    });
    print('🔄 ServerService状态变化: ${oldState.toString()} -> ${state.toString()}, isConnected: $isConnected'); // 强制输出
    notifyListeners();
  }
  
  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  /// 清除错误信息
  void _clearError() {
    _errorMessage = '';
    notifyListeners();
  }
  
  /// 设置心跳频率
  void setHeartbeatInterval(int seconds) {
    // 验证心跳频率范围（1-300秒）
    if (seconds < 1 || seconds > 300) {
      _logger.w(_logTag, '状态栏查询频率超出有效范围(1-300秒)', {'seconds': seconds});
      return;
    }
    
    final newInterval = Duration(seconds: seconds);
    if (_heartbeatInterval != newInterval) {
      final oldSeconds = _heartbeatInterval.inSeconds;
      _heartbeatInterval = newInterval;
      
      _logger.i(_logTag, '状态栏查询频率已更新', {
        'oldInterval': '${oldSeconds}s',
        'newInterval': '${seconds}s',
        'description': '控制页头状态信息更新频率'
      });
      
      // 如果当前连接且不在快速心跳模式，重启心跳定时器
      if (isConnected && !_fastHeartbeatMode) {
        _logger.i(_logTag, '重启状态栏查询定时器以应用新频率');
        _startHeartbeat();
      }
      
      notifyListeners();
    }
  }
  
  @override
  void dispose() {
    disconnect();
    _stopAllTimers();
    _messageListeners.clear();
    super.dispose();
  }
} 