// ignore_for_file: unused_import, avoid_print, unused_field

import 'package:flutter/material.dart';
import 'dart:convert';
import 'dart:async';
import '../component/dropdown_component.dart';
import '../utils/logger.dart';
import '../services/server_service.dart';
import '../models/auth_model.dart';
import '../models/game_model.dart';
import '../models/ui_config_model.dart';
import '../models/login_model.dart';
import '../models/header_model.dart';
import 'side_controller.dart';

/// 页头控制器 - 负责处理页头页面的业务逻辑
class HeaderController extends ChangeNotifier {
  // 常量配置
  static const String _logTag = 'HeaderController';
  static const Duration _requestDelay = Duration(milliseconds: 50);
  static const Duration _refreshDelay = Duration(milliseconds: 100);
  static const Duration _aimDelay = Duration(milliseconds: 150);
  static const Duration _fireDelay = Duration(milliseconds: 200);
  static const String _defaultGameId = 'csgo2';
  
  // 请求动作映射
  static const Map<String, String> _moduleActions = {
    'fov': 'fov_read',
    'pid': 'pid_read',
    'aim': 'aim_read',
    'fire': 'fire_read',
    'dataCollection': 'data_collection_read',
  };
  
  late GameLabelItem _selectedGame;
  bool _statusCallbackRegistered = false; // 状态回调注册标志
  final ServerService _serverService;
  final AuthModel _authModel;
  final GameModel _gameModel;
  final LoginModel _loginModel;
  final HeaderModel _headerModel;
  final Logger _logger = Logger();
  
  SideController? _currentRefreshController;
  Timer? _disposeTimer;
  
  GameLabelItem get selectedGame => _selectedGame;
  
  /// 构造函数
  HeaderController({
    required ServerService serverService,
    required AuthModel authModel,
    required GameModel gameModel,
    required LoginModel loginModel,
    required HeaderModel headerModel,
  }) : _serverService = serverService,
       _authModel = authModel,
       _gameModel = gameModel,
       _loginModel = loginModel,
       _headerModel = headerModel {
    _initializeController();
  }
  
  /// 初始化控制器
  void _initializeController() {
    _initFromGameModel();
    _registerStatusUpdateCallback();
    _logger.i(_logTag, '页头控制器初始化完成');
  }
  
  /// 注册状态更新回调
  void _registerStatusUpdateCallback() {
    _serverService.addStatusUpdateCallback(_onStatusUpdate);
    _statusCallbackRegistered = true;
    _logger.i(_logTag, '已注册状态更新回调');
  }
  
  /// 重新注册状态更新回调（用于Provider复用实例时）
  void reregisterStatusUpdateCallback() {
    // 检查是否已经注册，避免重复注册
    if (_statusCallbackRegistered) {
      _logger.i(_logTag, '状态更新回调已注册，跳过重复注册');
      return;
    }

    _serverService.removeStatusUpdateCallback(_onStatusUpdate);
    _serverService.addStatusUpdateCallback(_onStatusUpdate);
    _statusCallbackRegistered = true;
    _logger.i(_logTag, '已重新注册状态更新回调');
  }
  
  /// 状态更新回调处理
  void _onStatusUpdate(Map<String, dynamic> statusData) {
    _logger.i(_logTag, '([_onStatusUpdate]) 收到状态更新回调，数据: $statusData');
    _headerModel.updateFromJson(statusData);
    _logger.i(_logTag, '([_onStatusUpdate]) HeaderModel状态已同步更新');
  }
  
  /// 从游戏模型初始化数据
  void _initFromGameModel() {
    final currentGameName = _gameModel.currentGame;
    final allGames = _gameModel.getAllGameLabels();
    
    final matchedGame = _findMatchingGame(allGames, currentGameName);
    if (matchedGame != null) {
      _selectedGame = matchedGame;
      _logger.i(_logTag, '匹配到游戏', {'id': matchedGame.id, 'label': matchedGame.label});
    } else {
      _selectedGame = _gameModel.getDefaultGameLabel();
      _gameModel.updateCurrentGame(_selectedGame.id);
      _logger.i(_logTag, '使用默认游戏', {'id': _selectedGame.id, 'label': _selectedGame.label});
    }
  }
  
  /// 查找匹配的游戏
  GameLabelItem? _findMatchingGame(List<GameLabelItem> games, String gameName) {
    for (final game in games) {
      if (_isGameMatch(game, gameName)) {
        return game;
      }
    }
    return null;
  }
  
  /// 检查游戏是否匹配
  bool _isGameMatch(GameLabelItem game, String gameName) {
    return game.id.toLowerCase() == gameName.toLowerCase() || 
           game.label.toLowerCase() == gameName.toLowerCase();
  }
  
  /// 处理游戏选择
  void handleGameSelected(String? gameId) {
    if (gameId == null || gameId == _selectedGame.id) return;
    
    final newGame = _gameModel.findGameLabelById(gameId);
    if (newGame == null) return;
    
    final oldGame = _selectedGame.id;
    _updateSelectedGame(newGame);
    
    _logger.i(_logTag, '页头游戏选择已更新', {
      'oldGame': oldGame,
      'newGame': gameId,
      'username': _authModel.username,
      'selectedGameId': _selectedGame.id,
      'gameModelGame': _gameModel.currentGame,
    });
    
    // 立即发送游戏更改请求（类似首页的保存操作）
    _notifyServerGameChanged();
    _refreshAllModules();
      
    notifyListeners();
  }
  
  /// 更新选中的游戏
  void _updateSelectedGame(GameLabelItem game) {
    _selectedGame = game;
    _gameModel.updateCurrentGame(game.id);
  }
  
  /// 获取全部游戏标签项列表（委托给GameModel）
  List<GameLabelItem> getAllGameLabels() {
    return _gameModel.getAllGameLabels();
  }
  
  /// 刷新所有模块
  void _refreshAllModules() {
      _refreshGameModules();
      _refreshSidebar();
  }
  
  /// 刷新游戏相关功能模块数据
  void _refreshGameModules() {
    if (!_serverService.isConnected) {
      _logger.w(_logTag, 'WebSocket未连接，无法发送功能模块刷新请求');
      return;
    }
    
    try {
      _logger.i(_logTag, '开始刷新游戏相关功能模块数据');
      _sendModuleRequests();
    } catch (e) {
      _logger.e(_logTag, '刷新游戏功能模块数据失败', e.toString());
    }
  }
  
  /// 发送模块请求
  void _sendModuleRequests() {
    final requests = _buildModuleRequests();
    
    _sendRequestWithDelay(requests['fov']!, Duration.zero);
    _sendRequestWithDelay(requests['pid']!, _requestDelay);
    _sendRequestWithDelay(requests['aim']!, _refreshDelay);
    _sendRequestWithDelay(requests['fire']!, _aimDelay);
    _sendRequestWithDelay(requests['dataCollection']!, _fireDelay);
  }
  
  /// 构建模块请求
  Map<String, Map<String, dynamic>> _buildModuleRequests() {
    final baseContent = _buildBaseRequestContent();
    
    return {
      'fov': _buildRequest(_moduleActions['fov']!, baseContent),
      'pid': _buildRequest(_moduleActions['pid']!, baseContent),
      'aim': _buildRequest(_moduleActions['aim']!, baseContent),
      'fire': _buildRequest(_moduleActions['fire']!, baseContent),
      'dataCollection': _buildRequest(_moduleActions['dataCollection']!, baseContent),
    };
  }
  
  /// 构建请求
  Map<String, dynamic> _buildRequest(String action, Map<String, dynamic> content) {
    return {
      'action': action,
      'content': content,
    };
  }
  
  /// 构建基础请求内容
  Map<String, dynamic> _buildBaseRequestContent() {
    // 使用当前选中的游戏，如果没有选中则使用GameModel中的游戏
    final currentGameName = _selectedGame.id;
    
    return {
      'username': _authModel.username,
      'gameName': currentGameName,
      'cardKey': _gameModel.cardKey,
      'updatedAt': DateTime.now().toIso8601String(),
    };
  }
  
  /// 延迟发送请求
  void _sendRequestWithDelay(Map<String, dynamic> request, Duration delay) {
    Future.delayed(delay, () {
      if (_serverService.isConnected) {
        _serverService.sendMessage(jsonEncode(request));
        _logger.i(_logTag, '已发送${request['action']}请求');
      }
    });
  }
  
  /// 刷新侧边栏数据
  void _refreshSidebar() {
    _cancelCurrentRefreshTask();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sendSidebarRefreshRequests();
    });
  }
  
  /// 发送侧边栏刷新请求
  void _sendSidebarRefreshRequests() {
    if (!_serverService.isConnected) {
      _logger.w(_logTag, 'WebSocket未连接，无法发送侧边栏刷新请求');
      return;
    }
    
    try {
      _disposeTimer?.cancel();
      _disposeTimer = null;
      
      // 根据sidebar_api.md，发送所有页面的读取请求
      final pageActions = [
        'home_read',
        'function_read', 
        'pid_read',
        'fov_read',
        'aim_read',
        'fire_read',
        'data_collection_read'
      ];
      
      // 以100毫秒间隔依次发送请求
      for (int i = 0; i < pageActions.length; i++) {
        Future.delayed(Duration(milliseconds: i * 100), () {
          if (_serverService.isConnected) {
            final request = _buildPageRefreshRequest(pageActions[i]);
            _serverService.sendMessage(jsonEncode(request));
            _logger.i(_logTag, '已发送${pageActions[i]}请求');
          }
        });
      }
      
      _logger.i(_logTag, '已发送侧边栏页面刷新请求', {
        'gameName': _selectedGame.id, 
        'username': _authModel.username,
        'pageCount': pageActions.length
      });
    } catch (e) {
      _logger.e(_logTag, '刷新侧边栏数据失败', e.toString());
    }
  }
  
  /// 构建页面刷新请求
  Map<String, dynamic> _buildPageRefreshRequest(String action) {
    // 使用当前选中的游戏，如果没有选中则使用GameModel中的游戏
    final currentGameName = _selectedGame.id;
    
    return {
      'action': action,
      'content': {
        'username': _authModel.username,
        'gameName': currentGameName,
        'cardKey': _gameModel.cardKey,
        'updatedAt': DateTime.now().toIso8601String(),
      }
    };
  }
  
  /// 取消当前刷新任务
  void _cancelCurrentRefreshTask() {
    _disposeTimer?.cancel();
    _disposeTimer = null;
    
    if (_currentRefreshController != null) {
      _logger.i(_logTag, '清理临时SideController');
      _currentRefreshController!.dispose();
      _currentRefreshController = null;
    }
  }
  
  /// 通知服务器游戏选择已更改
  void _notifyServerGameChanged() {
    if (!_serverService.isConnected) {
      _logger.w(_logTag, '无法发送游戏更改消息，服务器未连接');
      return;
    }
    
    try {
      final requestData = _buildGameChangeRequest();
      _serverService.sendMessage(jsonEncode(requestData));
      
      _logger.i(_logTag, '已发送游戏更改消息', {
        'gameName': _selectedGame.id,
        'username': _authModel.username,
      });
    } catch (e) {
      _logger.e(_logTag, '发送游戏更改消息失败', e.toString());
    }
  }
  
  /// 构建游戏更改请求
  Map<String, dynamic> _buildGameChangeRequest() {
    // 使用当前选中的游戏，如果没有选中则使用GameModel中的游戏
    final currentGameName = _selectedGame.id;
    
    return {
      'action': 'home_modify',
      'content': {
        'username': _authModel.username,
        'gameName': currentGameName,
        'cardKey': _gameModel.cardKey,
        'isPro': _getProStatus(),
        'updatedAt': DateTime.now().toIso8601String(),
      }
    };
  }
  
  /// 获取Pro状态
  bool _getProStatus() {
    try {
      return _loginModel.isPro;
    } catch (e) {
      _logger.w(_logTag, '获取Pro状态失败', e.toString());
      return false;
    }
  }
  
  /// 处理退出登录
  void handleLogout(BuildContext context) {
    final currentUsername = _authModel.username;
    final rememberPwd = _authModel.rememberPassword;

    _logger.i(_logTag, '用户退出登录', {
      'username': currentUsername,
      'rememberPassword': rememberPwd
    });

    // 退出登录，Pro状态将在下次登录时由服务器重新设置
    _authModel.logout(onClearLoginInfo: () {
      _loginModel.clearLoginInfo();
    });

    _logger.i(_logTag, '用户已退出登录，保留的用户名: ${_authModel.username}');

    // 安全的页面跳转，检查context是否有效
    if (context.mounted) {
      try {
        Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
      } catch (e) {
        _logger.e(_logTag, '页面跳转失败', e.toString());
        // 如果Navigator跳转失败，尝试使用延迟跳转
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (context.mounted) {
            try {
              Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
            } catch (e) {
              _logger.e(_logTag, '延迟页面跳转也失败', e.toString());
            }
          }
        });
      }
    } else {
      _logger.w(_logTag, 'Context已销毁，无法进行页面跳转');
    }
  }
  
  /// 处理刷新系统
  void handleRefreshSystem() {
    _logger.i(_logTag, '系统刷新开始');

    // 只有在未连接时才尝试重连，避免不必要的连接中断
    if (!_serverService.isConnected) {
      _logger.i(_logTag, 'WebSocket未连接，尝试重连');
      _reconnectWebSocket();
    } else {
      _logger.i(_logTag, 'WebSocket已连接，跳过重连');
    }

    _sendSystemRefreshRequests();
    _refreshAllModules();
    _initFromGameModel();

    notifyListeners();
  }
  
  /// 发送系统刷新请求
  void _sendSystemRefreshRequests() {
    if (!_serverService.isConnected) {
      _logger.w(_logTag, '无法发送系统刷新请求，WebSocket未连接');
      return;
    }
    
    try {
      _sendSystemRefreshRequest();
      _sendStatusBarQueryRequest();
    } catch (e) {
      _logger.e(_logTag, '发送系统刷新请求失败', e.toString());
    }
  }
  
  /// 发送系统刷新请求
  void _sendSystemRefreshRequest() {
    // 使用当前选中的游戏，如果没有选中则使用GameModel中的游戏
    final currentGameName = _selectedGame.id;
    
    final refreshRequest = {
      'action': 'system_refresh',
      'content': {
        'username': _authModel.username,
        'gameName': currentGameName,
        'refreshTime': DateTime.now().toIso8601String(),
      }
    };
        
    _serverService.sendMessage(jsonEncode(refreshRequest));
    _logger.i(_logTag, '系统刷新请求已发送');
  }
  
  /// 发送状态栏查询请求
  void _sendStatusBarQueryRequest() {
    _serverService.sendMessage({
      'action': 'status_bar_query',
      'token': _serverService.token,
    });
    _logger.i(_logTag, '状态栏查询请求已发送');
  }
  
  /// 尝试连接或重新连接WebSocket
  void _reconnectWebSocket() {
    if (_serverService.isConnected) {
      _logger.i(_logTag, 'WebSocket已连接，将重新连接');
      _serverService.disconnect();
    }
    
    final connectionInfo = _getConnectionInfo();
    if (_isValidConnectionInfo(connectionInfo)) {
      _attemptConnection(connectionInfo);
    } else {
      _logger.w(_logTag, '无法连接WebSocket，缺少连接信息');
    }
  }
  
  /// 获取连接信息
  Map<String, String> _getConnectionInfo() {
    String serverAddress = _serverService.serverAddress;
    String serverPort = _serverService.serverPort;
    String token = _serverService.token;
    
    if (serverAddress.isEmpty || serverPort.isEmpty) {
      _logger.i(_logTag, 'ServerService中无连接信息，从LoginModel获取');
      
      serverAddress = _loginModel.serverAddress;
      serverPort = _loginModel.serverPort;
      
      if (token.isEmpty) {
        token = _loginModel.token.isNotEmpty ? _loginModel.token : _authModel.token;
      }
    }
    
    return {
        'address': serverAddress,
        'port': serverPort,
      'token': token,
    };
  }
  
  /// 检查连接信息是否有效
  bool _isValidConnectionInfo(Map<String, String> info) {
    return info['address']!.isNotEmpty && info['port']!.isNotEmpty;
  }
  
  /// 尝试连接
  void _attemptConnection(Map<String, String> connectionInfo) {
    final address = connectionInfo['address']!;
    final port = connectionInfo['port']!;
    final token = connectionInfo['token']!;
    
    _logger.i(_logTag, '尝试连接WebSocket服务器', {
      'address': address,
      'port': port,
        'hasToken': token.isNotEmpty,
      });
      
      Future.microtask(() async {
        try {
          final success = await _serverService.connect(
          serverAddress: address,
          serverPort: port,
            token: token,
          );
          
          if (success) {
          _handleConnectionSuccess(address, port, token);
        } else {
          _logger.e(_logTag, 'WebSocket连接失败');
        }
      } catch (e) {
        _logger.e(_logTag, 'WebSocket连接异常', e.toString());
      }
    });
  }
  
  /// 处理连接成功
  void _handleConnectionSuccess(String address, String port, String token) {
    _logger.i(_logTag, 'WebSocket连接成功');
    
    _updateLoginModelConnection(address, port, token);
    _sendInitialRequests(token);
    
    notifyListeners();
  }
  
  /// 更新LoginModel连接信息
  void _updateLoginModelConnection(String address, String port, String token) {
    _loginModel.updateServerAddress(address);
    _loginModel.updateServerPort(port);
            if (token.isNotEmpty) {
              _loginModel.updateToken(token);
    }
            }
            
  /// 发送初始请求
  void _sendInitialRequests(String token) {
    _serverService.sendMessage({
      'action': 'status_bar_query',
      'token': token,
    });
    
    _logger.i(_logTag, '初始请求已发送');
  }
  
  /// 从路径中提取游戏信息
  (String id, String displayName) extractInfoFromPath(String path) {
    final filename = path.split('/').last;
    final id = filename.split('.').first;
    return (id, id);
  }
  
  /// 将GameLabelItem转换为IconDropdownItem
  List<IconDropdownItem> convertToIconDropdownItems(List<GameLabelItem> games) {
    return games.map((game) => IconDropdownItem(
      value: game.id,
      text: game.label,
      image: Image.asset(
        game.iconPath,
        width: 24,
        height: 24,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 24,
            height: 24,
            color: Colors.grey.shade100,
            child: const Icon(
              Icons.games,
              size: 20,
              color: Colors.grey,
            ),
          );
        },
      ),
    )).toList();
  }
  
  @override
  void dispose() {
    _cancelCurrentRefreshTask();
    _serverService.removeStatusUpdateCallback(_onStatusUpdate);
    _logger.i(_logTag, 'HeaderController资源已释放');
    super.dispose();
  }
} 