// ignore_for_file: unused_import, sized_box_for_whitespace

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/auth_model.dart';
import '../models/login_model.dart';
import '../models/header_model.dart';
import '../controllers/header_controller.dart';
import '../component/dropdown_component.dart';

/// 页头左侧组件常量配置
class HeaderLeftConstants {
  static const Color primaryColor = Color.fromARGB(255, 52, 100, 172);

  // 下拉菜单配置
  static const double menuWidth = 200.0;
  static const double menuMaxHeight = 360.0; // 增加菜单最大高度（280 → 360）
  static const EdgeInsets menuPadding = EdgeInsets.all(12.0);
  static const double itemHeight = 40.0;
  static const double sectionSpacing = 8.0;
  static const double itemSpacing = 4.0;
  static const double borderRadius = 8.0;

  // 游戏列表配置
  static const double gameListMaxHeight = 180.0; // 增加游戏列表最大高度（120 → 180），约5个游戏项目
}

/// 用户头像配置
class AvatarConfig {
  static const double containerSize = 36.0; // 减小容器尺寸，更紧凑
  static const double avatarSize = 32.0; // 减小头像核心尺寸
  static const double fontSize = 14.0; // 减小字体
  static const double proBorderWidth = 2.0;
  static const Color proGoldColor = Color(0xFFFFD700);
  static const Color proBrownColor = Color(0xFF8B4513);
  static const double crownSize = 10.0; // 减小皇冠图标
  static const Color normalBackgroundColor = HeaderLeftConstants.primaryColor;
}

/// 页头左侧组件 - 包含用户头像和下拉菜单
class HeaderLeftComponent extends StatefulWidget {
  const HeaderLeftComponent({super.key});
  
  @override
  State<HeaderLeftComponent> createState() => _HeaderLeftComponentState();
}

class _HeaderLeftComponentState extends State<HeaderLeftComponent> {
  final GlobalKey _avatarKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  bool _isMenuOpen = false;

  @override
  void dispose() {
    _closeMenu();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: UserAvatarSection(
        key: _avatarKey,
        onTap: _toggleMenu,
      ),
    );
  }

  /// 切换菜单显示/隐藏
  void _toggleMenu() {
    if (_isMenuOpen) {
      _closeMenu();
    } else {
      _showMenu();
    }
  }

  /// 显示下拉菜单
  void _showMenu() {
    if (_isMenuOpen) return;

    final RenderBox renderBox = _avatarKey.currentContext?.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => UserDropdownMenu(
        position: offset,
        avatarSize: size,
        onClose: _closeMenu,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isMenuOpen = true;
    });
  }

  /// 关闭下拉菜单
  void _closeMenu() {
    if (!_isMenuOpen) return;

    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _isMenuOpen = false;
    });
  }
}

/// 用户头像区域组件
class UserAvatarSection extends StatelessWidget {
  final VoidCallback onTap;
  
  const UserAvatarSection({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthModel, LoginModel>(
      builder: (context, authModel, loginModel, child) {
        return Consumer<HeaderModel>(
          builder: (context, headerModel, child) {
            return Stack(
              children: [
                UserAvatarWidget(
                  username: authModel.username,
                  isPro: loginModel.isPro,
                  onTap: onTap,
                ),
                
                // 版本更新提示小红点
                if (headerModel.hasNewVersion)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white,
                          width: 1,
                        ),
                      ),
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}

/// 用户下拉菜单组件
class UserDropdownMenu extends StatelessWidget {
  final Offset position;
  final Size avatarSize;
  final VoidCallback onClose;
  
  const UserDropdownMenu({
    super.key,
    required this.position,
    required this.avatarSize,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onClose,
      child: Material(
        color: Colors.transparent,
        child: Stack(
          children: [
            // 透明背景，点击关闭菜单
            Positioned.fill(
              child: Container(color: Colors.transparent),
            ),
            // 下拉菜单
            Positioned(
              left: position.dx,
              top: position.dy + avatarSize.height + 4,
              child: GestureDetector(
                onTap: () {}, // 阻止事件冒泡
                child: Container(
                  width: HeaderLeftConstants.menuWidth,
                  constraints: const BoxConstraints(
                    maxHeight: HeaderLeftConstants.menuMaxHeight,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(HeaderLeftConstants.borderRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.15),
                        blurRadius: 10.0,
                        offset: const Offset(0, 4),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.2),
                      width: 1.0,
                    ),
                  ),
                  child: UserDropdownContent(onClose: onClose),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 用户下拉菜单内容组件
class UserDropdownContent extends StatelessWidget {
  final VoidCallback onClose;
  
  const UserDropdownContent({
    super.key,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(HeaderLeftConstants.borderRadius),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 固定显示的用户信息区域
          Consumer2<AuthModel, LoginModel>(
            builder: (context, authModel, loginModel, child) {
              return _buildUserInfoSection(authModel, loginModel);
            },
          ),

          // 分隔线
          Divider(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.3),
          ),

          // 固定显示的版本信息区域
          Consumer<HeaderModel>(
            builder: (context, headerModel, child) {
              return _buildVersionInfoSection(headerModel);
            },
          ),

          // 分隔线
          Divider(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.3),
          ),

          // 固定显示的游戏选择标题
          _buildGameSectionTitle(),

          // 可滚动的游戏列表区域（仅此部分可滚动）
          Consumer<HeaderController>(
            builder: (context, headerController, child) {
              return _buildScrollableGameList(headerController);
            },
          ),
        ],
      ),
    );
  }

  /// 构建用户信息区域
  Widget _buildUserInfoSection(AuthModel authModel, LoginModel loginModel) {
    return Container(
      width: double.infinity,
      padding: HeaderLeftConstants.menuPadding,
      child: Row(
        children: [
          // 用户头像（小版本）
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: loginModel.isPro 
                  ? AvatarConfig.proBrownColor 
                  : AvatarConfig.normalBackgroundColor,
              border: loginModel.isPro 
                  ? Border.all(color: AvatarConfig.proGoldColor, width: 2)
                  : null,
            ),
            child: Center(
              child: Text(
                authModel.username.isNotEmpty 
                    ? authModel.username[0].toUpperCase() 
                    : 'U',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: loginModel.isPro 
                      ? AvatarConfig.proGoldColor 
                      : Colors.white,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // 用户信息文字
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  authModel.username,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    if (loginModel.isPro) ...[
                      Icon(
                        Icons.star,
                        size: 12,
                        color: AvatarConfig.proGoldColor,
                      ),
                      const SizedBox(width: 4),
                    ],
                    Text(
                      loginModel.isPro ? 'PRO' : '普通',
                      style: TextStyle(
                        fontSize: 12,
                        color: loginModel.isPro 
                            ? AvatarConfig.proGoldColor 
                            : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建版本信息区域
  Widget _buildVersionInfoSection(HeaderModel headerModel) {
    final currentVersion = headerModel.currentVersion;
    final latestVersion = headerModel.latestVersion;
    final hasNewVersion = headerModel.hasNewVersion;
    
    // 如果没有版本信息，不显示此区域
    if (currentVersion.isEmpty && latestVersion.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 当前版本
          if (currentVersion.isNotEmpty)
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 12,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    '当前版本: $currentVersion',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          
          // 最新版本（如果有新版本）
          if (hasNewVersion && latestVersion.isNotEmpty) ...[
            const SizedBox(height: 3),
            Row(
              children: [
                Icon(
                  Icons.new_releases,
                  size: 12,
                  color: Colors.orange.shade600,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    '最新版本: $latestVersion',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.orange.shade600,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
          
          // 版本状态提示
          if (hasNewVersion) ...[
            const SizedBox(height: 3),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: Colors.orange.withValues(alpha: 0.3),
                  width: 0.5,
                ),
              ),
              child: Text(
                '有新版本可用',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ] else if (currentVersion.isNotEmpty && latestVersion.isNotEmpty) ...[
            const SizedBox(height: 3),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: Colors.green.withValues(alpha: 0.3),
                  width: 0.5,
                ),
              ),
              child: Text(
                '已是最新版本',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.green.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建游戏选择标题
  Widget _buildGameSectionTitle() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.games,
            size: 14,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 6),
          Text(
            '选择游戏',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建可滚动的游戏列表（仅游戏列表部分可滚动）
  Widget _buildScrollableGameList(HeaderController headerController) {
    final games = headerController.getAllGameLabels();
    final selectedGameId = headerController.selectedGame.id;

    return Container(
      constraints: const BoxConstraints(
        maxHeight: HeaderLeftConstants.gameListMaxHeight, // 限制游戏列表的最大高度
      ),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(), // 使用弹性滚动效果
        child: Column(
          children: games.map((game) {
            final isSelected = game.id == selectedGameId;
            
            return Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  headerController.handleGameSelected(game.id);
                  onClose();
                },
                borderRadius: BorderRadius.circular(6.0),
                child: Container(
                  width: double.infinity,
                  height: 38, // 稍微增加高度，利用更大的空间
                  margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2), // 增加垂直间距
                  padding: const EdgeInsets.symmetric(horizontal: 12), // 增加水平内边距
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Colors.blue.withValues(alpha: 0.12)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(6.0),
                    border: isSelected
                        ? Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 1)
                        : null,
                  ),
                  child: Row(
                    children: [
                      // 游戏图标
                      Container(
                        width: 20, // 减小图标尺寸
                        height: 20,
                        child: Image.asset(
                          game.iconPath,
                          width: 20,
                          height: 20,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.games,
                              size: 18,
                              color: Colors.grey.shade400,
                            );
                          },
                        ),
                      ),

                      const SizedBox(width: 8),

                      // 游戏名称
                      Expanded(
                        child: Text(
                          game.label,
                          style: TextStyle(
                            fontSize: 13, // 减小字体
                            color: isSelected
                                ? Colors.blue.shade700
                                : Colors.black87,
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      // 选中标识
                      if (isSelected)
                        Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade600,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            size: 12,
                            color: Colors.white,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

/// 用户头像组件
class UserAvatarWidget extends StatelessWidget {
  final String username;
  final bool isPro;
  final VoidCallback onTap;
  
  const UserAvatarWidget({
    super.key,
    required this.username,
    required this.isPro,
    required this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: '点击查看用户信息和游戏选择',
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(18.0),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(18.0),
          child: SizedBox(
            width: AvatarConfig.containerSize,
            height: AvatarConfig.containerSize,
            child: Center(
              child: isPro 
                  ? ProAvatar(username: username)
                  : NormalAvatar(username: username),
            ),
          ),
        ),
      ),
    );
  }
}

/// Pro版头像
class ProAvatar extends StatelessWidget {
  final String username;
  
  const ProAvatar({super.key, required this.username});
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: AvatarConfig.avatarSize,
      height: AvatarConfig.avatarSize,
      child: Stack(
        children: [
          Container(
            width: AvatarConfig.avatarSize,
            height: AvatarConfig.avatarSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AvatarConfig.proGoldColor,
                width: AvatarConfig.proBorderWidth,
              ),
              boxShadow: [
                BoxShadow(
                  color: AvatarConfig.proGoldColor.withValues(alpha: 0.3),
                  blurRadius: 3.0,
                  spreadRadius: 0.5,
                ),
              ],
            ),
            child: CircleAvatar(
              radius: (AvatarConfig.avatarSize - AvatarConfig.proBorderWidth * 2) / 2,
              backgroundColor: AvatarConfig.proBrownColor,
              child: Text(
                username.isNotEmpty ? username[0].toUpperCase() : 'P',
                style: const TextStyle(
                  fontSize: AvatarConfig.fontSize,
                  color: AvatarConfig.proGoldColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          Positioned(
            right: 0,
            top: 0,
            child: Icon(
              Icons.star,
              size: AvatarConfig.crownSize,
              color: AvatarConfig.proGoldColor,
            ),
          ),
        ],
      ),
    );
  }
}

/// 普通版头像
class NormalAvatar extends StatelessWidget {
  final String username;
  
  const NormalAvatar({super.key, required this.username});
  
  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: AvatarConfig.avatarSize / 2,
      backgroundColor: AvatarConfig.normalBackgroundColor,
      child: Text(
        username.isNotEmpty ? username[0].toUpperCase() : 'A',
        style: const TextStyle(
          fontSize: AvatarConfig.fontSize,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
} 