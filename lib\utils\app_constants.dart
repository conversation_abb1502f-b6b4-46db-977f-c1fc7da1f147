// ignore_for_file: constant_identifier_names

/// 应用常量管理 - 统一管理所有常量定义
/// 
/// 这个类集中管理BlWeb项目中的所有常量，包括网络配置、UI尺寸、
/// 游戏配置、存储键名等，避免在多个文件中重复定义相同的常量。
class AppConstants {
  // 私有构造函数，防止实例化
  AppConstants._();

  /// ========== 网络相关常量 ==========
  static const String DEFAULT_WEBSOCKET_URL = 'ws://localhost:8080';
  static const Duration REQUEST_TIMEOUT = Duration(seconds: 30);
  static const Duration HEARTBEAT_INTERVAL = Duration(seconds: 30);
  static const Duration CONNECTION_RETRY_DELAY = Duration(seconds: 5);
  static const int MAX_RETRY_ATTEMPTS = 3;
  static const Duration REQUEST_DELAY = Duration(milliseconds: 50);

  /// ========== 游戏相关常量 ==========
  static const String DEFAULT_GAME_ID = 'csgo2';
  static const List<String> SUPPORTED_GAMES = [
    'apex',
    'cf',
    'cfhd',
    'csgo2',
    'default',
    'hpjy',
    'pubg',
    'sszj',
    'sszj2',
    'valorant'
  ];

  /// 游戏显示名称映射
  static const Map<String, String> GAME_DISPLAY_NAMES = {
    'apex': 'Apex Legends',
    'cf': 'CrossFire',
    'cfhd': 'CrossFire HD',
    'csgo2': 'CS:GO 2',
    'default': 'Default',
    'hpjy': '和平精英',
    'pubg': 'PUBG',
    'sszj': '生死狙击',
    'sszj2': '生死狙击2',
    'valorant': '无畏契约',
  };

  /// ========== UI相关常量 ==========
  static const Duration ANIMATION_DURATION = Duration(milliseconds: 300);
  static const Duration TOAST_DURATION = Duration(seconds: 3);
  static const Duration LOADING_DELAY = Duration(milliseconds: 500);
  
  // 响应式断点
  static const double MOBILE_BREAKPOINT = 600.0;
  static const double TABLET_BREAKPOINT = 900.0;
  static const double EXTRA_SMALL_BREAKPOINT = 360.0;
  static const double SMALL_BREAKPOINT = 480.0;
  static const double MEDIUM_BREAKPOINT = 600.0;
  static const double LARGE_BREAKPOINT = 900.0;
  static const double EXTRA_LARGE_BREAKPOINT = 1200.0;

  /// ========== 布局相关常量 ==========
  static const double SIDEBAR_WIDTH = 250.0;
  static const double MOBILE_SIDEBAR_WIDTH = 60.0;
  static const double DESKTOP_SIDEBAR_WIDTH = 250.0;
  static const double HEADER_HEIGHT = 60.0;
  static const double CARD_BORDER_RADIUS = 8.0;
  static const double BUTTON_BORDER_RADIUS = 4.0;
  static const double INPUT_BORDER_RADIUS = 4.0;

  /// ========== 间距常量 ==========
  static const double SPACING_XS = 4.0;
  static const double SPACING_SM = 8.0;
  static const double SPACING_MD = 16.0;
  static const double SPACING_LG = 24.0;
  static const double SPACING_XL = 32.0;

  /// ========== 字体大小常量 ==========
  static const double FONT_SIZE_XS = 10.0;
  static const double FONT_SIZE_SM = 12.0;
  static const double FONT_SIZE_MD = 14.0;
  static const double FONT_SIZE_LG = 16.0;
  static const double FONT_SIZE_XL = 18.0;
  static const double FONT_SIZE_XXL = 20.0;

  /// ========== 滑块组件相关常量 ==========
  static const double SLIDER_HEIGHT = 28.0;
  static const double SLIDER_BUTTON_WIDTH = 28.0;
  static const double SLIDER_INPUT_WIDTH_DESKTOP = 80.0;
  static const double SLIDER_INPUT_WIDTH_MOBILE = 60.0;
  static const double SLIDER_INPUT_WIDTH_SMALL = 50.0;
  static const double SLIDER_TRACK_HEIGHT = 4.0;

  /// ========== 数据验证常量 ==========
  static const int MIN_USERNAME_LENGTH = 3;
  static const int MAX_USERNAME_LENGTH = 20;
  static const int MIN_PASSWORD_LENGTH = 6;
  static const int MAX_PASSWORD_LENGTH = 50;
  static const double MIN_SLIDER_VALUE = 0.0;
  static const double MAX_SLIDER_VALUE = 100.0;

  /// ========== 本地存储键名 ==========
  static const String STORAGE_PREFIX = 'blweb_';
  
  // 用户相关
  static const String KEY_USERNAME = '${STORAGE_PREFIX}username';
  static const String KEY_PASSWORD = '${STORAGE_PREFIX}password';
  static const String KEY_TOKEN = '${STORAGE_PREFIX}token';
  static const String KEY_IS_PRO = '${STORAGE_PREFIX}is_pro';
  static const String KEY_REMEMBER_PASSWORD = '${STORAGE_PREFIX}remember_password';
  static const String KEY_SERVER_ADDRESS = '${STORAGE_PREFIX}server_address';
  static const String KEY_SERVER_PORT = '${STORAGE_PREFIX}server_port';

  // 游戏相关
  static const String KEY_CURRENT_GAME = '${STORAGE_PREFIX}current_game';
  static const String KEY_GAME_LIST = '${STORAGE_PREFIX}game_list';
  static const String KEY_CARD_KEY = '${STORAGE_PREFIX}card_key';

  // 配置相关
  static const String KEY_AIM_CONFIG = '${STORAGE_PREFIX}aim_config';
  static const String KEY_FIRE_CONFIG = '${STORAGE_PREFIX}fire_config';
  static const String KEY_FOV_CONFIG = '${STORAGE_PREFIX}fov_config';
  static const String KEY_PID_CONFIG = '${STORAGE_PREFIX}pid_config';
  static const String KEY_FUNCTION_CONFIG = '${STORAGE_PREFIX}function_config';
  static const String KEY_DATA_COLLECTION_CONFIG = '${STORAGE_PREFIX}data_collection_config';

  // UI配置
  static const String KEY_SIDEBAR_COLLAPSED = '${STORAGE_PREFIX}sidebar_collapsed';
  static const String KEY_THEME_MODE = '${STORAGE_PREFIX}theme_mode';
  static const String KEY_FONT_FAMILY = '${STORAGE_PREFIX}font_family';
  static const String KEY_IS_DARK_MODE = '${STORAGE_PREFIX}is_dark_mode';
  static const String KEY_LANGUAGE = '${STORAGE_PREFIX}language';

  /// ========== WebSocket动作常量 ==========
  static const String ACTION_LOGIN = 'login_read';
  static const String ACTION_REGISTER = 'register_read';
  static const String ACTION_STATUS_BAR_QUERY = 'status_bar_query';
  static const String ACTION_GAME_LIST_QUERY = 'game_list_query';
  
  // 配置动作
  static const String ACTION_AIM_READ = 'aim_read';
  static const String ACTION_AIM_MODIFY = 'aim_modify';
  static const String ACTION_FIRE_READ = 'fire_read';
  static const String ACTION_FIRE_MODIFY = 'fire_modify';
  static const String ACTION_FOV_READ = 'fov_read';
  static const String ACTION_FOV_MODIFY = 'fov_modify';
  static const String ACTION_PID_READ = 'pid_read';
  static const String ACTION_PID_MODIFY = 'pid_modify';
  static const String ACTION_FUNCTION_READ = 'function_read';
  static const String ACTION_FUNCTION_MODIFY = 'function_modify';
  static const String ACTION_DATA_COLLECTION_READ = 'data_collection_read';
  static const String ACTION_DATA_COLLECTION_MODIFY = 'data_collection_modify';

  /// ========== 错误消息常量 ==========
  static const String ERROR_NETWORK = '网络连接失败，请检查网络设置';
  static const String ERROR_SERVER = '服务器连接异常，请重试';
  static const String ERROR_PARSE = '服务器响应格式错误，请稍后重试';
  static const String ERROR_VALIDATION = '输入数据格式错误';
  static const String ERROR_UNAUTHORIZED = '用户认证失败，请重新登录';
  static const String ERROR_TIMEOUT = '请求超时，请重试';
  static const String ERROR_CONNECTION_FAILED = '连接服务器失败';

  /// ========== 成功消息常量 ==========
  static const String SUCCESS_LOGIN = '登录成功';
  static const String SUCCESS_REGISTER = '注册成功';
  static const String SUCCESS_SAVE = '保存成功';
  static const String SUCCESS_UPDATE = '更新成功';
  static const String SUCCESS_DELETE = '删除成功';
  static const String SUCCESS_CONNECT = '连接成功';

  /// ========== 日志标签常量 ==========
  static const String LOG_TAG_APP = 'BlWebApp';
  static const String LOG_TAG_AUTH = 'AuthController';
  static const String LOG_TAG_LOGIN = 'LoginController';
  static const String LOG_TAG_REGISTER = 'RegisterController';
  static const String LOG_TAG_HOME = 'HomeController';
  static const String LOG_TAG_HEADER = 'HeaderController';
  static const String LOG_TAG_SIDEBAR = 'SideController';
  static const String LOG_TAG_AIM = 'AimController';
  static const String LOG_TAG_FIRE = 'FireController';
  static const String LOG_TAG_FOV = 'FovController';
  static const String LOG_TAG_PID = 'PidController';
  static const String LOG_TAG_FUNCTION = 'FunctionController';
  static const String LOG_TAG_DATA_COLLECTION = 'DataCollectionController';
  static const String LOG_TAG_SERVER = 'ServerService';
  static const String LOG_TAG_WEBSOCKET = 'BlWebSocket';

  /// ========== 正则表达式常量 ==========
  static const String REGEX_USERNAME = r'^[a-zA-Z0-9_]{3,20}$';
  static const String REGEX_PASSWORD = r'^.{6,50}$';
  static const String REGEX_EMAIL = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String REGEX_NUMBER = r'^[0-9]+$';
  static const String REGEX_DECIMAL = r'^[0-9]+\.?[0-9]*$';

  /// ========== 默认配置值 ==========
  static const Map<String, dynamic> DEFAULT_AIM_CONFIG = {
    'aimSpeed': 50.0,
    'aimSmooth': 30.0,
    'aimFov': 60.0,
    'aimBone': 'head',
  };

  static const Map<String, dynamic> DEFAULT_FIRE_CONFIG = {
    'fireRate': 100.0,
    'recoilControl': 50.0,
    'burstMode': false,
  };

  static const Map<String, dynamic> DEFAULT_FOV_CONFIG = {
    'fovValue': 90.0,
    'fovSmooth': 50.0,
  };

  static const Map<String, dynamic> DEFAULT_PID_CONFIG = {
    'pidP': 1.0,
    'pidI': 0.0,
    'pidD': 0.0,
    'yAxisCoefficient': 1.0,
  };

  /// ========== 工具方法 ==========
  
  /// 获取游戏显示名称
  static String getGameDisplayName(String gameId) {
    return GAME_DISPLAY_NAMES[gameId] ?? gameId;
  }

  /// 检查是否为支持的游戏
  static bool isSupportedGame(String gameId) {
    return SUPPORTED_GAMES.contains(gameId);
  }

  /// 获取存储键名
  static String getStorageKey(String key) {
    return '$STORAGE_PREFIX$key';
  }

  /// 验证用户名格式
  static bool isValidUsername(String username) {
    return RegExp(REGEX_USERNAME).hasMatch(username);
  }

  /// 验证密码格式
  static bool isValidPassword(String password) {
    return RegExp(REGEX_PASSWORD).hasMatch(password);
  }

  /// 验证邮箱格式
  static bool isValidEmail(String email) {
    return RegExp(REGEX_EMAIL).hasMatch(email);
  }
}
