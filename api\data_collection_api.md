# 数据收集系统API

本文档描述数据收集系统的WebSocket API请求和响应格式，基于Prisma数据库schema设计。

## 数据库结构说明

数据收集功能基于`DataCollection`表，该表与`SuperAdmin`用户表建立一对多关系：
- **主键**：`[username, gameName]` - 联合主键，确保每个用户在每个游戏中只有一个数据收集配置
- **关联关系**：与SuperAdmin表建立外键关系，支持级联删除
- **存储位置**：MySQL数据库中的`data_collections`表

## 请求和响应格式

系统使用四种主要操作：
- `data_collection_read`: 读取数据收集设置（从数据库查询）
- `data_collection_modify`: 修改数据收集设置（保存到数据库）
- `data_collection_fetch`: 获取文件系统数据（扫描imgs文件夹）
- `data_collection_upload`: 上传数据到远程服务器

### 读取数据收集设置 (data_collection_read)

**功能说明**：从数据库中查询指定用户和游戏的数据收集配置

**数据库操作**：
```sql
SELECT * FROM data_collections
WHERE username = ? AND game_name = ?
```

**客户端请求**：

```json
{
  "action": "data_collection_read",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称"
  }
}
```

**服务器响应（成功）**：

```json
{
  "action": "data_collection_read_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "isEnabled": true,
    "mapHotkey": "右键",
    "targetHotkey": "前侧",
    "collectionName": "csgo2_collection_jf",
    "teamSide": "警方",
    "createdAt": "2023-06-01T14:30:00.000Z",
    "updatedAt": "2023-06-01T15:00:00.000Z"
  }
}
```

**服务器响应（记录不存在）**：

```json
{
  "action": "data_collection_read_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "isEnabled": false,
    "mapHotkey": "右键",
    "targetHotkey": "前侧",
    "collectionName": "",
    "teamSide": "无",
    "createdAt": null,
    "updatedAt": null
  }
}
```

### 修改数据收集设置 (data_collection_modify)

**功能说明**：保存或更新用户的数据收集配置到数据库

**数据库操作**：使用Prisma的upsert操作，如果记录存在则更新，不存在则创建
```sql
-- 如果记录存在，执行更新
UPDATE data_collections SET
  is_enabled = ?, map_hotkey = ?, target_hotkey = ?,
  team_side = ?, collection_name = ?, updated_at = NOW()
WHERE username = ? AND game_name = ?

-- 如果记录不存在，执行插入
INSERT INTO data_collections
  (username, game_name, is_enabled, map_hotkey, target_hotkey, team_side, collection_name, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
```

**客户端请求**：

```json
{
  "action": "data_collection_modify",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "isEnabled": true,
    "mapHotkey": "右键",
    "targetHotkey": "前侧",
    "collectionName": "csgo2_collection_jf",
    "teamSide": "警方"
  }
}
```

**服务器响应（成功）**：

```json
{
  "action": "data_collection_modify_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "isEnabled": true,
    "mapHotkey": "右键",
    "targetHotkey": "前侧",
    "collectionName": "csgo2_collection_jf",
    "teamSide": "警方",
    "createdAt": "2023-06-01T14:30:00.000Z",
    "updatedAt": "2023-06-02T10:00:00.000Z"
  }
}
```

**服务器响应（失败）**：

```json
{
  "action": "data_collection_modify_response",
  "status": "error",
  "content": {
    "error": {
      "code": "VALIDATION_ERROR",
      "message": "用户不存在或游戏名称无效",
      "field": "username"
    }
  }
}
```

### 获取数据信息 (data_collection_fetch)

**功能说明**：扫描文件系统中的imgs文件夹，获取所有数据收集文件夹的统计信息

**文件系统操作**：
- 扫描 `./imgs/` 目录下的所有子文件夹
- 统计每个文件夹中的图像文件（.jpg, .png等）和标签文件（.txt, .xml等）数量
- 计算文件夹的最后修改时间
- 返回详细的统计信息

**客户端请求**：

```json
{
  "action": "data_collection_fetch",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "includeAllCollections": true,
    "includeDetails": true
  }
}
```

**服务器响应（成功）**：

```json
{
  "action": "data_collection_fetch_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "collections": [
      {
        "name": "csgo2_collection_jf",
        "path": "./imgs/csgo2_collection_jf",
        "folders": [
          {
            "name": "maps",
            "imageCount": 60,
            "labelCount": 60,
            "fileCount": 120,
            "lastUpdated": "2023-06-02T09:15:00.000Z"
          },
          {
            "name": "targets",
            "imageCount": 45,
            "labelCount": 40,
            "fileCount": 85,
            "lastUpdated": "2023-06-02T09:20:00.000Z"
          }
        ],
        "totalFiles": 205,
        "detailedStats": {
          "maps": {
            "images": 60,
            "labels": 60,
            "total": 120
          },
          "targets": {
            "images": 45,
            "labels": 40,
            "total": 85
          }
        },
        "summary": {
          "images": 105,
          "labels": 100,
          "total": 205
        },
        "lastUpdated": "2023-06-02T10:05:00.000Z"
      },
      {
        "name": "csgo2_collection_ff",
        "path": "./imgs/csgo2_collection_ff",
        "folders": [
          {
            "name": "maps",
            "imageCount": 30,
            "labelCount": 30,
            "fileCount": 60,
            "lastUpdated": "2023-06-02T16:15:00.000Z"
          },
          {
            "name": "targets",
            "imageCount": 25,
            "labelCount": 20,
            "fileCount": 45,
            "lastUpdated": "2023-06-02T16:20:00.000Z"
          }
        ],
        "totalFiles": 105,
        "detailedStats": {
          "maps": {
            "images": 30,
            "labels": 30,
            "total": 60
          },
          "targets": {
            "images": 25,
            "labels": 20,
            "total": 45
          }
        },
        "summary": {
          "images": 55,
          "labels": 50,
          "total": 105
        },
        "lastUpdated": "2023-06-02T16:25:00.000Z"
      }
    ],
    "totalCollections": 2,
    "timestamp": "2023-06-02T17:05:00.000Z"
  }
}
```

**服务器响应（无数据）**：

```json
{
  "action": "data_collection_fetch_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "collections": [],
    "totalCollections": 0,
    "timestamp": "2023-06-02T17:05:00.000Z"
  }
}
```

### 上传数据 (data_collection_upload)

**功能说明**：将本地收集的数据文件上传到远程服务器，并可选择性删除本地文件

**操作流程**：
1. 根据用户配置确定要上传的文件夹
2. 压缩文件夹并上传到远程服务器
3. 根据`deleteAll`参数决定是否删除本地文件
4. 返回上传结果和状态信息

**客户端请求**：

```json
{
  "action": "data_collection_upload",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "teamSide": "警方",
    "mapHotkey": "右键",
    "targetHotkey": "前侧",
    "timestamp": "2023-06-02T10:10:00.000Z",
    "deleteAll": false
  }
}
```

**服务器响应（成功）**：

```json
{
  "action": "data_collection_upload_response",
  "status": "ok",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "uploadStatus": "success",
    "uploadComplete": true,
    "filesDeleted": true,
    "message": "数据上传成功并已删除源文件"
  }
}
```

**服务器响应（失败）**：

```json
{
  "action": "data_collection_upload_response",
  "status": "error",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "uploadStatus": "failed",
    "uploadComplete": false,
    "filesDeleted": false,
    "error": {
      "code": "SERVER_ERROR",
      "message": "无法连接到上传服务器"
    }
  }
}
```

**服务器响应（部分成功）**：

```json
{
  "action": "data_collection_upload_response",
  "status": "warning",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "uploadStatus": "partial",
    "uploadComplete": false,
    "filesDeleted": false,
    "message": "部分数据上传成功，但出现错误: 网络连接中断"
  }
}
```

## 数据库字段映射

基于Prisma Schema的`DataCollection`模型，字段映射关系如下：

| API字段名 | 数据库字段名 | 类型 | 默认值 | 约束 | 描述 |
|----------|-------------|------|--------|------|------|
| username | username | String | - | 主键 | 用户名，关联SuperAdmin表 |
| gameName | game_name | String | - | 主键 | 游戏名称 |
| isEnabled | is_enabled | Boolean | false | - | 是否启用数据收集 |
| mapHotkey | map_hotkey | String | "右键" | - | 地图数据收集热键 |
| targetHotkey | target_hotkey | String | "前侧" | - | 目标数据收集热键 |
| teamSide | team_side | String | "无" | - | 阵营选择 |
| collectionName | collection_name | String | "" | - | 数据收集名称 |
| createdAt | created_at | DateTime | NOW() | 自动生成 | 创建时间 |
| updatedAt | updated_at | DateTime | NOW() | 自动更新 | 更新时间 |

## 参数说明

| 参数名称 | 类型 | 描述 | 可选值/格式 | 是否必需 |
|---------|------|------|------------|---------|
| username | string | 用户名，必须在SuperAdmin表中存在 | 任意有效字符串 | 是 |
| gameName | string | 游戏名称 | apex, cf, cfhd, csgo2, pubg, sjz, ssjj2, wwqy | 是 |
| isEnabled | boolean | 是否启用数据收集功能 | true, false | 否（默认false） |
| mapHotkey | string | 地图数据收集热键 | 左键, 右键, 中键, 前侧, 后侧, 无 | 否（默认"右键"） |
| targetHotkey | string | 目标数据收集热键 | 左键, 右键, 中键, 前侧, 后侧, 无 | 否（默认"前侧"） |
| collectionName | string | 数据收集名称，用于标识收集的数据集 | 任意字符串，建议格式：游戏名_描述_阵营简写 | 否（默认""） |
| teamSide | string | 阵营选择 | 警方, 匪方, 无 | 否（默认"无"） |
| createdAt | string | 记录创建时间 | ISO8601格式：YYYY-MM-DDTHH:mm:ss.sssZ | 否（自动生成） |
| updatedAt | string | 记录更新时间 | ISO8601格式：YYYY-MM-DDTHH:mm:ss.sssZ | 否（自动更新） |
| timestamp | string | 操作时间戳 | ISO8601格式：YYYY-MM-DDTHH:mm:ss.sssZ | 否 |
| deleteAll | boolean | 上传后是否删除所有本地数据 | true, false | 否（默认false） |
| includeAllCollections | boolean | 是否包含所有收集文件夹 | true, false | 否（默认true） |
| includeDetails | boolean | 是否包含详细统计信息 | true, false | 否（默认true） |
| totalCollections | number | 数据收集文件夹总数 | 非负整数 | 否（响应字段） |

## 数据结构说明

### collections 数组结构

data_collection_fetch 响应中的 collections 数组包含所有数据采集文件夹的信息，每个采集文件夹包含以下字段：

| 字段 | 类型 | 描述 |
|------|------|------|
| name | string | 采集文件夹名称 |
| path | string | 采集文件夹路径 |
| folders | array | 包含该采集文件夹中的分类子文件夹（如maps、targets）信息的数组 |
| totalFiles | number | 该采集文件夹中的文件总数 |
| detailedStats | object | 详细统计信息，包含各分类的图像、标签及文件总数 |
| summary | object | 汇总信息，包含总的图像、标签及文件总数 |
| lastUpdated | string | 最后更新时间 |

### stats 对象结构

| 字段 | 类型 | 描述 |
|------|------|------|
| totalImages | number | 所有分类中图像文件的总数 |
| totalLabels | number | 所有分类中标签文件的总数 |
| totalFiles | number | 所有文件的总数（图像+标签） |
| categories | object | 包含各分类详细统计的对象 |

### categories 对象结构

每个分类（如maps、targets）都包含以下字段：

| 字段 | 类型 | 描述 |
|------|------|------|
| images | number | 该分类中图像文件的数量 |
| labels | number | 该分类中标签文件的数量 |
| total | number | 该分类中文件总数（图像+标签） |

## 数据库约束说明

### 主键约束
- **联合主键**：`[username, gameName]` - 确保每个用户在每个游戏中只能有一个数据收集配置

### 外键约束
- **username** → `SuperAdmin.username`：级联删除，当用户被删除时，相关的数据收集配置也会被删除

### 默认值约束
- `isEnabled`: false - 新配置默认禁用
- `mapHotkey`: "右键" - 默认地图收集热键
- `targetHotkey`: "前侧" - 默认目标收集热键
- `teamSide`: "无" - 默认无阵营
- `collectionName`: "" - 默认空名称

## 错误码说明

| 错误码 | 描述 | 可能原因 |
|-------|------|---------|
| DATABASE_ERROR | 数据库操作错误 | 数据库连接失败、SQL语法错误 |
| VALIDATION_ERROR | 数据验证失败 | 必需字段缺失、数据格式错误 |
| USER_NOT_FOUND | 用户不存在 | username在SuperAdmin表中不存在 |
| INVALID_GAME | 无效的游戏名称 | gameName不在支持的游戏列表中 |
| DUPLICATE_RECORD | 记录已存在 | 尝试创建已存在的配置记录 |
| SERVER_ERROR | 服务器内部错误 | 未预期的服务器异常 |
| SERVER_UNREACHABLE | 无法连接到上传服务器 | 网络连接问题、服务器宕机 |
| AUTH_FAILED | 认证失败 | 用户权限不足、token无效 |
| INVALID_COLLECTION | 无效的收集名称 | collectionName格式错误或包含非法字符 |
| NO_DATA | 没有可上传的数据 | 指定的数据文件夹为空或不存在 |
| UPLOAD_INCOMPLETE | 上传未完成 | 网络中断、文件损坏 |
| DISK_FULL | 目标服务器存储空间不足 | 远程服务器磁盘空间不够 |
| FILE_SYSTEM_ERROR | 文件系统错误 | 本地文件读取失败、权限不足 |

## 最佳实践

### 数据库操作最佳实践

1. **使用事务处理**：
   ```javascript
   // 推荐：使用事务确保数据一致性
   await prisma.$transaction(async (tx) => {
     const config = await tx.dataCollection.upsert({
       where: { username_gameName: { username, gameName } },
       update: { /* 更新数据 */ },
       create: { /* 创建数据 */ }
     });
   });
   ```

2. **数据验证**：
   ```javascript
   // 验证用户是否存在
   const user = await prisma.superAdmin.findUnique({
     where: { username }
   });
   if (!user) {
     throw new Error('USER_NOT_FOUND');
   }
   ```

3. **错误处理**：
   ```javascript
   try {
     // 数据库操作
   } catch (error) {
     if (error.code === 'P2002') {
       // 唯一约束违反
       return { status: 'error', code: 'DUPLICATE_RECORD' };
     }
     // 其他错误处理
   }
   ```

### API调用最佳实践

1. **请求去重**：客户端应实现请求去重机制，避免重复提交
2. **超时处理**：设置合理的请求超时时间（建议30秒）
3. **重试机制**：网络错误时实现指数退避重试
4. **状态同步**：定期同步本地状态与服务器状态

### 文件系统操作最佳实践

1. **路径安全**：验证文件路径，防止目录遍历攻击
2. **文件锁定**：上传过程中锁定文件，防止并发修改
3. **增量同步**：大文件使用分块上传和断点续传
4. **清理策略**：定期清理过期的临时文件

## 版本历史

### v2.0.0 (2025-07-23)
- 🔄 **重大更新**：基于Prisma Schema重新设计API文档
- ✨ **新增功能**：
  - 完整的数据库字段映射说明
  - 详细的约束和关系说明
  - 扩展的错误码定义
  - 最佳实践指南
- 🐛 **修复问题**：
  - 修正API参数与实际实现的不一致
  - 完善错误处理机制
  - 统一时间戳格式为ISO8601

### v1.5.0 (2025-07-23)
- ✨ 新增随机部位锁定功能：在锁定部位选项中添加"随机部位"选项
- 📝 更新API文档和数据模型说明

### v1.4.3 (2025-01-03)
- 🐛 修复数据收集配置保存问题
- 🔧 优化WebSocket消息处理机制
- 📊 改进文件统计算法

### v1.4.0 (2024-12-15)
- ✨ 新增批量上传功能
- 🔒 增强数据安全性验证
- 🚀 优化大文件处理性能

### v1.3.0 (2024-11-20)
- 📁 支持多种文件格式的数据收集
- 🎯 改进阵营选择逻辑
- 📈 添加详细的统计信息

## 技术规范

### 数据库版本要求
- **MySQL**: 8.0+
- **Prisma**: 5.0+
- **Node.js**: 18.0+

### 性能指标
- **数据库查询响应时间**: < 100ms
- **文件扫描响应时间**: < 2s (1000个文件以内)
- **上传超时时间**: 30s
- **并发连接数**: 最大100个

### 安全要求
- 所有数据库操作必须通过Prisma ORM
- 用户输入必须进行SQL注入防护
- 文件路径必须进行安全验证
- 上传文件必须进行病毒扫描