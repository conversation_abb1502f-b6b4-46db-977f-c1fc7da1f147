// ignore_for_file: use_super_parameters, sized_box_for_whitespace

import 'package:flutter/material.dart';
// GetWidget已移除，使用原生Flutter组件

/// 状态类型枚举
enum StatusDisplayType {
  /// 成功状态 - 绿色
  success,
  
  /// 信息状态 - 蓝色
  info,
  
  /// 警告状态 - 橙色
  warning,
  
  /// 错误状态 - 红色
  error,
  
  /// 中性状态 - 灰色
  neutral,
  
  /// 加载状态 - 蓝色带动画
  loading,
}

/// 通用状态显示组件 - 基于GetWidget封装
/// 
/// 使用示例：
/// ```dart
/// // 基础状态显示
/// StatusDisplayComponent.create(
///   message: '连接成功',
///   type: StatusDisplayType.success,
///   description: '服务器连接正常',
/// )
/// 
/// // 使用GFCard样式
/// StatusDisplayComponent.createCard(
///   message: '系统状态',
///   type: StatusDisplayType.info,
///   description: '所有服务运行正常',
/// )
/// 
/// // 使用GFAlert样式
/// StatusDisplayComponent.createAlert(
///   message: '警告',
///   type: StatusDisplayType.warning,
///   description: '请检查网络连接',
/// )
/// ```
class StatusDisplayComponent extends StatelessWidget {
  final String message;
  final StatusDisplayType type;
  final String? description;
  final Widget? icon;
  final Widget? trailing;
  final double borderRadius;
  final EdgeInsets padding;
  final VoidCallback? onTap;
  final bool showBorder;
  final double? maxWidth;

  const StatusDisplayComponent({
    Key? key,
    required this.message,
    required this.type,
    this.description,
    this.icon,
    this.trailing,
    this.borderRadius = 8.0,
    this.padding = const EdgeInsets.all(16.0),
    this.onTap,
    this.showBorder = true,
    this.maxWidth,
  }) : super(key: key);

  /// 创建状态显示组件的工厂方法
  static Widget create({
    required String message,
    required StatusDisplayType type,
    String? description,
    Widget? icon,
    Widget? trailing,
    double borderRadius = 8.0,
    EdgeInsets padding = const EdgeInsets.all(16.0),
    VoidCallback? onTap,
    bool showBorder = true,
    double? maxWidth,
  }) {
    return StatusDisplayComponent(
      message: message,
      type: type,
      description: description,
      icon: icon,
      trailing: trailing,
      borderRadius: borderRadius,
      padding: padding,
      onTap: onTap,
      showBorder: showBorder,
      maxWidth: maxWidth,
    );
  }

  /// 创建服务器状态显示组件
  static Widget createServerStatus({
    required bool isConnected,
    required bool isConnecting,
    required bool hasError,
    required String errorText,
    required String serverAddress,
    required String serverPort,
  }) {
    StatusDisplayType type;
    String message;
    String? description;
    Widget? icon;

    if (isConnecting) {
      type = StatusDisplayType.loading;
      message = '正在连接服务器...';
      description = '服务器地址: $serverAddress:$serverPort';
    } else if (isConnected) {
      type = StatusDisplayType.success;
      message = '服务器已连接';
      description = '服务器地址: $serverAddress:$serverPort';
      icon = const Icon(
        Icons.check_circle,
        color: Colors.green,
        size: 20,
      );
    } else if (hasError) {
      type = StatusDisplayType.error;
      message = '连接失败';
      description = errorText.isNotEmpty ? errorText : '无法连接到服务器: $serverAddress:$serverPort';
      icon = const Icon(
        Icons.error,
        color: Colors.red,
        size: 20,
      );
    } else {
      type = StatusDisplayType.warning;
      message = '未连接服务器';
      description = '请点击"连接服务器"按钮建立连接';
      icon = const Icon(
        Icons.warning,
        color: Colors.orange,
        size: 20,
      );
    }

    return StatusDisplayComponent.create(
      message: message,
      type: type,
      description: description,
      icon: icon,
      borderRadius: 8.0,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
    );
  }

  /// 创建基于Container的状态显示组件
  static Widget createAlert({
    required String message,
    required StatusDisplayType type,
    String? description,
    Widget? icon,
    VoidCallback? onTap,
  }) {
    final config = _getStatusConfig(type);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: config.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: config.iconColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            message,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: config.textColor,
            ),
          ),
          if (description != null) ...[
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                fontSize: 12,
                height: 1.3,
                color: config.textColor.withValues(alpha: 0.8),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 创建基于GFCard的状态显示组件
  static Widget createCard({
    required String message,
    required StatusDisplayType type,
    String? description,
    Widget? icon,
    Widget? trailing,
    VoidCallback? onTap,
    double elevation = 2.0,
  }) {
    final config = _getStatusConfig(type);
    
    return Card(
      elevation: elevation,
      color: config.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 状态图标
          if (icon != null) ...[
            icon,
            const SizedBox(width: 12),
          ] else ...[
            Icon(
              config.iconData,
              color: config.iconColor,
              size: 20,
            ),
            const SizedBox(width: 12),
          ],
          
          // 主要内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 主要消息
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: config.textColor,
                  ),
                ),
                
                // 描述信息
                if (description != null && description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: config.textColor.withValues(alpha: 0.8),
                      height: 1.3,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // 尾部组件
          if (trailing != null) ...[
            const SizedBox(width: 8),
            trailing,
          ],
        ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final config = _getStatusConfig(type);
    
    // 使用Card作为基础容器
    Widget content = Card(
      elevation: showBorder ? 1.0 : 0.0,
      color: config.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      margin: EdgeInsets.zero,
      child: Padding(
        padding: padding,
        child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 状态图标
          _buildStatusIcon(config),
          const SizedBox(width: 12),
          
          // 主要内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 主要消息
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: config.textColor,
                  ),
                ),
                
                // 描述信息
                if (description != null && description!.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    description!,
                    style: TextStyle(
                      fontSize: 12,
                      color: config.textColor.withValues(alpha: 0.8),
                      height: 1.3,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // 尾部组件
          if (trailing != null) ...[
            const SizedBox(width: 8),
            trailing!,
          ],
        ],
        ),
      ),
    );

    // 如果有点击事件，包装在InkWell中
    if (onTap != null) {
      content = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius),
        child: content,
      );
    }

    // 如果有最大宽度限制，包装在Container中
    if (maxWidth != null) {
      content = Container(
        width: maxWidth,
        child: content,
      );
    }

    return content;
  }

  /// 构建状态图标
  Widget _buildStatusIcon(StatusConfig config) {
    if (icon != null) {
      return icon!;
    }

    if (type == StatusDisplayType.loading) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      );
    }

    return Icon(
      config.iconData,
      color: config.iconColor,
      size: 20,
    );
  }

  /// 获取状态配置
  static StatusConfig _getStatusConfig(StatusDisplayType type) {
    switch (type) {
      case StatusDisplayType.success:
        return StatusConfig(
          backgroundColor: Colors.green.withValues(alpha: 0.1),
          borderColor: Colors.green.withValues(alpha: 0.3),
          iconColor: Colors.green,
          textColor: Colors.green.shade800,
          iconData: Icons.check_circle,
        );
      
      case StatusDisplayType.info:
        return StatusConfig(
          backgroundColor: Colors.blue.withValues(alpha: 0.1),
          borderColor: Colors.blue.withValues(alpha: 0.3),
          iconColor: Colors.blue,
          textColor: Colors.blue.shade800,
          iconData: Icons.info,
        );
      
      case StatusDisplayType.warning:
        return StatusConfig(
          backgroundColor: Colors.orange.withValues(alpha: 0.1),
          borderColor: Colors.orange.withValues(alpha: 0.3),
          iconColor: Colors.orange,
          textColor: Colors.orange.shade800,
          iconData: Icons.warning,
        );
      
      case StatusDisplayType.error:
        return StatusConfig(
          backgroundColor: Colors.red.withValues(alpha: 0.1),
          borderColor: Colors.red.withValues(alpha: 0.3),
          iconColor: Colors.red,
          textColor: Colors.red.shade800,
          iconData: Icons.error,
        );
      
      case StatusDisplayType.neutral:
        return StatusConfig(
          backgroundColor: Colors.grey.withValues(alpha: 0.1),
          borderColor: Colors.grey.withValues(alpha: 0.3),
          iconColor: Colors.grey,
          textColor: Colors.grey.shade800,
          iconData: Icons.info_outline,
        );
      
      case StatusDisplayType.loading:
        return StatusConfig(
          backgroundColor: Colors.blue.withValues(alpha: 0.1),
          borderColor: Colors.blue.withValues(alpha: 0.3),
          iconColor: Colors.blue,
          textColor: Colors.blue.shade800,
          iconData: Icons.hourglass_empty,
        );
    }
  }

  // GFAlert相关方法已移除，使用原生Flutter组件
}

/// 状态配置类
class StatusConfig {
  final Color backgroundColor;
  final Color borderColor;
  final Color iconColor;
  final Color textColor;
  final IconData iconData;

  const StatusConfig({
    required this.backgroundColor,
    required this.borderColor,
    required this.iconColor,
    required this.textColor,
    required this.iconData,
  });
}

/// 为了向后兼容，提供旧的StatusIndicatorComponent别名
class StatusIndicatorComponent {
  static Widget create({
    required String message,
    required StatusType type,
    String? description,
    Widget? icon,
    Widget? trailing,
    double borderRadius = 8.0,
    EdgeInsets padding = const EdgeInsets.all(16.0),
    VoidCallback? onTap,
    bool showBorder = true,
    double? maxWidth,
  }) {
    return StatusDisplayComponent.create(
      message: message,
      type: _convertStatusType(type),
      description: description,
      icon: icon,
      trailing: trailing,
      borderRadius: borderRadius,
      padding: padding,
      onTap: onTap,
      showBorder: showBorder,
      maxWidth: maxWidth,
    );
  }

  /// 转换旧的StatusType到新的StatusDisplayType
  static StatusDisplayType _convertStatusType(StatusType oldType) {
    switch (oldType) {
      case StatusType.success:
        return StatusDisplayType.success;
      case StatusType.info:
        return StatusDisplayType.info;
      case StatusType.warning:
        return StatusDisplayType.warning;
      case StatusType.error:
        return StatusDisplayType.error;
      case StatusType.neutral:
        return StatusDisplayType.neutral;
      case StatusType.loading:
        return StatusDisplayType.loading;
    }
  }
}

/// 为了向后兼容，提供旧的StatusType枚举
enum StatusType {
  success,
  info,
  warning,
  error,
  neutral,
  loading,
} 