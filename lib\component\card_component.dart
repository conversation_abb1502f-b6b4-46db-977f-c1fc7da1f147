// ignore_for_file: use_super_parameters, deprecated_member_use

import 'package:flutter/material.dart';

/// 卡片数据项配置类
class CardItemConfig {
  final String id;           // 卡片唯一标识
  final String title;        // 卡片标题
  final String? imagePath;   // 图片路径（优先使用）
  final IconData? icon;      // 图标（图片为空时使用）
  final Color? backgroundColor; // 背景颜色
  final Color? textColor;    // 文字颜色

  const CardItemConfig({
    required this.id,
    required this.title,
    this.imagePath,
    this.icon,
    this.backgroundColor,
    this.textColor,
  });

  /// 快速创建图片卡片
  CardItemConfig.image(
    String id,
    String title,
    String imagePath, {
    Color? backgroundColor,
    Color? textColor,
  }) : this(
    id: id,
    title: title,
    imagePath: imagePath,
    backgroundColor: backgroundColor,
    textColor: textColor,
  );

  /// 快速创建图标卡片
  CardItemConfig.icon(
    String id,
    String title,
    IconData icon, {
    Color? backgroundColor,
    Color? textColor,
  }) : this(
    id: id,
    title: title,
    icon: icon,
    backgroundColor: backgroundColor,
    textColor: textColor,
  );
}

/// 卡片组件配置类
class CardComponentConfig {
  final List<CardItemConfig> items;        // 卡片项列表
  final Function(CardItemConfig) onSelected; // 选择回调
  final String? initialSelectedId;         // 初始选中ID
  final int? columns;                      // 列数（null为自适应）
  final double spacing;                    // 间距
  final double cardMinSize;                // 卡片最小尺寸
  final double cardMaxSize;                // 卡片最大尺寸
  final bool autoScale;                    // 是否自动缩放
  final EdgeInsets padding;                // 内边距

  const CardComponentConfig({
    required this.items,
    required this.onSelected,
    this.initialSelectedId,
    this.columns,
    this.spacing = 10.0,
    this.cardMinSize = 120.0,
    this.cardMaxSize = 180.0,
    this.autoScale = true,
    this.padding = const EdgeInsets.all(8.0),
  });
}

/// 统一卡片选择组件
class CardSelectComponent extends StatefulWidget {
  final CardComponentConfig config;

  const CardSelectComponent(this.config, {Key? key}) : super(key: key);

  /// 核心API - 创建卡片选择组件
  static Widget create(CardComponentConfig config) {
    return CardSelectComponent(config);
  }

  /// 便捷方法 - 快速创建图片卡片列表
  static Widget createImageCards({
    required List<String> imagePaths,
    required List<String> titles,
    required Function(int index, String imagePath, String title) onSelected,
    String? initialSelectedId,
    int? columns,
    double spacing = 10.0,
    EdgeInsets padding = const EdgeInsets.all(8.0),
  }) {
    assert(imagePaths.length == titles.length, '图片路径和标题数量必须匹配');
    
    final items = List.generate(imagePaths.length, (index) {
      return CardItemConfig.image(
        index.toString(),
        titles[index],
        imagePaths[index],
      );
    });

    return CardSelectComponent.create(CardComponentConfig(
      items: items,
      onSelected: (config) => onSelected(
        int.parse(config.id), 
        config.imagePath!, 
        config.title
      ),
      initialSelectedId: initialSelectedId,
      columns: columns,
      spacing: spacing,
      padding: padding,
    ));
  }

  /// 便捷方法 - 快速创建图标卡片列表
  static Widget createIconCards({
    required List<IconData> icons,
    required List<String> titles,
    required Function(int index, IconData icon, String title) onSelected,
    String? initialSelectedId,
    int? columns,
    double spacing = 10.0,
    EdgeInsets padding = const EdgeInsets.all(8.0),
  }) {
    assert(icons.length == titles.length, '图标和标题数量必须匹配');
    
    final items = List.generate(icons.length, (index) {
      return CardItemConfig.icon(
        index.toString(),
        titles[index],
        icons[index],
      );
    });

    return CardSelectComponent.create(CardComponentConfig(
      items: items,
      onSelected: (config) => onSelected(
        int.parse(config.id), 
        config.icon!, 
        config.title
      ),
      initialSelectedId: initialSelectedId,
      columns: columns,
      spacing: spacing,
      padding: padding,
    ));
  }

  /// 便捷方法 - 创建混合卡片列表（图片+图标）
  static Widget createMixedCards({
    required List<CardItemConfig> items,
    required Function(CardItemConfig) onSelected,
    String? initialSelectedId,
    int? columns,
    double spacing = 10.0,
    EdgeInsets padding = const EdgeInsets.all(8.0),
  }) {
    return CardSelectComponent.create(CardComponentConfig(
      items: items,
      onSelected: onSelected,
      initialSelectedId: initialSelectedId,
      columns: columns,
      spacing: spacing,
      padding: padding,
    ));
  }

  @override
  State<CardSelectComponent> createState() => _CardSelectComponentState();
}

class _CardSelectComponentState extends State<CardSelectComponent> {
  String? _selectedId;
  
  @override
  void initState() {
    super.initState();
    _selectedId = widget.config.initialSelectedId;
  }
  
  @override
  void didUpdateWidget(CardSelectComponent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.config.initialSelectedId != widget.config.initialSelectedId) {
      _selectedId = widget.config.initialSelectedId;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.config.padding,
      child: LayoutBuilder(
      builder: (context, constraints) {
          final availableWidth = constraints.maxWidth - 
              widget.config.padding.horizontal;
          final columns = widget.config.columns ?? 
              _calculateColumns(availableWidth);
          final cardSize = _calculateCardSize(availableWidth, columns);
        
        return Wrap(
            spacing: widget.config.spacing,
            runSpacing: widget.config.spacing,
          alignment: WrapAlignment.start,
            children: widget.config.items.map((item) {
              final isSelected = item.id == _selectedId;
            return SizedBox(
                width: cardSize,
                height: cardSize,
                child: _buildCard(item, isSelected),
            );
          }).toList(),
        );
        },
      ),
    );
  }
  
  /// 计算列数
  int _calculateColumns(double width) {
    if (width < 300) return 1;
    if (width < 600) return 2;
    if (width < 900) return 3;
    if (width < 1200) return 4;
    return 5;
  }
  
  /// 计算卡片尺寸
  double _calculateCardSize(double width, int columns) {
    final totalSpacing = widget.config.spacing * (columns - 1);
    final availableWidth = (width - totalSpacing) / columns;
    
    if (!widget.config.autoScale) {
      return (widget.config.cardMinSize + widget.config.cardMaxSize) / 2;
    }
    
    return availableWidth.clamp(
      widget.config.cardMinSize, 
      widget.config.cardMaxSize
    );
  }
  
  /// 构建单个卡片
  Widget _buildCard(CardItemConfig item, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedId = item.id;
        });
        widget.config.onSelected(item);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: Card(
          elevation: isSelected ? 12 : 4,
          shadowColor: isSelected
              ? Theme.of(context).primaryColor.withOpacity(0.3)
              : Colors.black.withOpacity(0.1),
          color: item.backgroundColor ?? Theme.of(context).cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Colors.grey.withOpacity(0.2),
              width: isSelected ? 2.5 : 1,
            ),
          ),
          child: Column(
          children: [
            // 媒体内容区域（图片或图标）
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8.0),
                child: _buildMediaContent(item),
              ),
            ),
            // 标题区域
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  item.title,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                    fontSize: 13,
                    color: item.textColor ?? 
                        (isSelected 
                            ? Theme.of(context).primaryColor 
                            : null),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
        ),
      ),
    );
  }

  /// 构建媒体内容（图片或图标）
  Widget _buildMediaContent(CardItemConfig item) {
    // 优先显示图片
    if (item.imagePath != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(
          item.imagePath!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey.shade200,
              child: Icon(
                Icons.broken_image,
                size: 40,
                color: Colors.grey.shade400,
              ),
            );
          },
        ),
      );
    }
    
    // 显示图标
    if (item.icon != null) {
      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          item.icon,
          size: 48,
          color: Theme.of(context).primaryColor,
        ),
      );
    }
    
    // 默认占位符
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        Icons.image,
        size: 40,
        color: Colors.grey.shade400,
      ),
    );
  }
}

/*
===========================================
新版统一API使用示例 - New Unified API Examples
===========================================

/// 1. 基础图片卡片列表
CardSelectComponent.createImageCards(
  imagePaths: [
    'assets/images/card1.png',
    'assets/images/card2.png',
    'assets/images/card3.png',
  ],
  titles: ['卡片1', '卡片2', '卡片3'],
  onSelected: (index, imagePath, title) {
    print('选中第$index张: $title ($imagePath)');
  },
  initialSelectedId: '0', // 默认选中第一张
  columns: 3, // 固定3列
);

/// 2. 基础图标卡片列表
CardSelectComponent.createIconCards(
  icons: [Icons.home, Icons.search, Icons.person],
  titles: ['首页', '搜索', '个人'],
  onSelected: (index, icon, title) {
    print('选中第$index个: $title');
  },
  columns: 3,
);

/// 3. 混合卡片列表（图片+图标）
CardSelectComponent.createMixedCards(
  items: [
    CardItemConfig.image('1', '图片卡片', 'assets/images/pic.png'),
    CardItemConfig.icon('2', '图标卡片', Icons.star),
    CardItemConfig('3', '自定义卡片', 
      backgroundColor: Colors.blue.shade100,
      textColor: Colors.blue.shade800,
    ),
  ],
  onSelected: (config) {
    print('选中: ${config.title}');
  },
);

/// 4. 高级配置示例
CardSelectComponent.create(CardComponentConfig(
  items: [
    CardItemConfig.image('img1', '图片1', 'assets/img1.png'),
    CardItemConfig.image('img2', '图片2', 'assets/img2.png'),
    CardItemConfig.icon('icon1', '图标1', Icons.star),
  ],
  onSelected: (config) => print('选中: ${config.title}'),
  initialSelectedId: 'img1',
  columns: 2,
  spacing: 15.0,
  cardMinSize: 100.0,
  cardMaxSize: 200.0,
  autoScale: true,
  padding: EdgeInsets.all(16.0),
));

/// 5. 游戏角色选择示例
CardSelectComponent.createImageCards(
  imagePaths: [
    'assets/characters/warrior.png',
    'assets/characters/mage.png',
    'assets/characters/archer.png',
    'assets/characters/rogue.png',
  ],
  titles: ['战士', '法师', '弓箭手', '盗贼'],
  onSelected: (index, imagePath, title) {
    // 选择角色逻辑
    gameController.selectCharacter(index, title);
  },
  columns: 2,
  spacing: 12.0,
);

/// 6. 工具选择示例
CardSelectComponent.createIconCards(
  icons: [
    Icons.edit,
    Icons.delete,
    Icons.share,
    Icons.download,
  ],
  titles: ['编辑', '删除', '分享', '下载'],
  onSelected: (index, icon, title) {
    // 执行对应工具操作
    toolController.executeTool(index);
  },
  spacing: 8.0,
);

/// 7. 主题选择示例
CardSelectComponent.createMixedCards(
  items: [
    CardItemConfig.image('light', '明亮主题', 'assets/themes/light.png',
      backgroundColor: Colors.white,
      textColor: Colors.black,
    ),
    CardItemConfig.image('dark', '暗黑主题', 'assets/themes/dark.png',
      backgroundColor: Colors.black,
      textColor: Colors.white,
    ),
    CardItemConfig.icon('auto', '自动主题', Icons.brightness_auto,
      backgroundColor: Colors.grey.shade100,
    ),
  ],
  onSelected: (config) {
    themeController.setTheme(config.id);
  },
  initialSelectedId: 'auto',
);
*/