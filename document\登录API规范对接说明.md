# 登录API规范对接说明

## 对接背景

根据最新的`api/login_api.md`文档，后端登录API进行了优化，提供了更精确的错误消息分类。前端需要严格按照新的API规范来处理登录逻辑。

## API规范变更

### 1. 错误消息分类优化

**旧版本API**：
- 统一返回 `"用户名或密码错误"`

**新版本API**：
- `"用户名错误"` - 用户不存在
- `"密码错误"` - 密码不匹配  
- `"令牌无效或已过期"` - token验证失败

### 2. 请求格式保持不变

```json
{
  "action": "login_read",
  "content": {
    "username": "用户名",
    "password": "密码",
    "token": "令牌(可选)",
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

### 3. 成功响应格式

```json
{
  "action": "login_read_response",
  "status": "success",
  "message": "登录成功",
  "data": {
    "userInfo": {
      "username": "用户名",
      "token": "身份验证令牌",
      "lastLoginTime": "2023-06-01T12:00:00Z",
      "isPro": false
    },
    "homeConfig": {
      "gameName": "游戏名称",
      "cardKey": "卡密"
    }
  }
}
```

## 前端对接修改

### 1. 错误消息处理优化

更新`LoginController`中的`_getDetailedErrorMessage()`方法：

```dart
String _getDetailedErrorMessage(String serverMessage) {
  switch (serverMessage) {
    case '用户名错误':
      return '用户名不存在，请检查用户名或注册新账号';
    case '密码错误':
      return '密码错误，请检查密码后重试';
    case '用户名或密码错误':
      // 兼容旧版本API
      return '用户名或密码错误，请检查后重试。如果您还没有账号，请先注册。';
    case '令牌无效或已过期':
      return '登录凭证已过期，请重新输入密码登录';
    default:
      return serverMessage.isNotEmpty ? serverMessage : '登录失败，请检查网络连接';
  }
}
```

### 2. 登录成功数据处理优化

增强`_handleLoginSuccess()`方法，正确处理`homeConfig`数据：

```dart
void _handleLoginSuccess(Map<String, dynamic> response, BuildContext? context, String message) {
  final data = response['data'];
  final userInfo = data?['userInfo'];
  final homeConfig = data?['homeConfig'];
  
  final userIsPro = userInfo?['isPro'] == true;
  final token = userInfo?['token'] ?? '';
  
  // 保存用户认证信息
  _saveAuthInfo(token, userIsPro);
  _updateLoginStatus(userIsPro);
  
  // 处理首页配置数据
  if (homeConfig != null) {
    _processHomeConfig(homeConfig);
  }
  
  // 强制同步Pro状态，确保与服务器一致
  _loginModel.forceUpdateProStatus(userIsPro, reason: '登录成功同步');
}
```

### 3. 新增首页配置处理方法

```dart
void _processHomeConfig(Map<String, dynamic> homeConfig) {
  try {
    final gameName = homeConfig['gameName']?.toString();
    final cardKey = homeConfig['cardKey']?.toString();
    
    if (gameName?.isNotEmpty == true) {
      _gameModel.updateCurrentGame(gameName!);
      log.i(_logTag, '更新游戏名称', gameName);
    }
    
    if (cardKey?.isNotEmpty == true) {
      _gameModel.updateCardKey(cardKey!);
      log.i(_logTag, '更新卡密信息', '***已获取***');
    }
  } catch (e) {
    log.e(_logTag, '处理首页配置数据失败', e.toString());
  }
}
```

## 错误消息映射表

| 后端返回消息 | 前端友好提示 | 用户操作建议 |
|-------------|-------------|-------------|
| 用户名错误 | 用户名不存在，请检查用户名或注册新账号 | 检查用户名拼写或注册新账号 |
| 密码错误 | 密码错误，请检查密码后重试 | 重新输入正确密码 |
| 令牌无效或已过期 | 登录凭证已过期，请重新输入密码登录 | 重新输入密码登录 |
| 用户名或密码错误 | 用户名或密码错误，请检查后重试。如果您还没有账号，请先注册。 | 兼容旧版本API |

## 用户体验改进

### 1. 精确的错误提示
- **用户名不存在**：明确提示用户名不存在，引导注册
- **密码错误**：明确提示密码错误，避免用户怀疑用户名
- **Token过期**：明确提示重新登录

### 2. 完整的数据处理
- **用户信息**：正确保存token、Pro状态等
- **首页配置**：自动更新游戏名称和卡密信息
- **状态同步**：确保前端状态与服务器一致

### 3. 向后兼容
- 保持对旧版本API的兼容性
- 渐进式升级，避免破坏性变更

## 技术要点

### 1. 数据结构解析
```dart
final data = response['data'];
final userInfo = data?['userInfo'];
final homeConfig = data?['homeConfig'];
```

### 2. 空值安全处理
```dart
final userIsPro = userInfo?['isPro'] == true;
final token = userInfo?['token'] ?? '';
```

### 3. 异常处理
```dart
try {
  _processHomeConfig(homeConfig);
} catch (e) {
  log.e(_logTag, '处理首页配置数据失败', e.toString());
}
```

### 4. 日志记录
```dart
log.i(_logTag, '登录成功，处理服务器返回数据', {
  'serverIsPro': userIsPro,
  'hasHomeConfig': homeConfig != null,
});
```

## 修改文件

- `lib/controllers/login_controller.dart` - 主要的登录逻辑修改
- `lib/document/登录API规范对接说明.md` - 本文档

## 测试验证

### 1. 用户名不存在场景
- 输入不存在的用户名
- 验证是否显示"用户名不存在，请检查用户名或注册新账号"

### 2. 密码错误场景  
- 输入存在的用户名但错误的密码
- 验证是否显示"密码错误，请检查密码后重试"

### 3. 登录成功场景
- 正确的用户名和密码
- 验证用户信息和首页配置是否正确保存

### 4. Token登录场景
- 使用过期的token
- 验证是否显示"登录凭证已过期，请重新输入密码登录"

## 总结

通过严格按照最新的`login_api.md`规范对接前端代码，实现了：

1. **精确的错误分类**：用户能够准确了解登录失败的具体原因
2. **完整的数据处理**：正确处理登录成功后的所有数据
3. **向后兼容性**：保持对旧版本API的兼容
4. **用户体验提升**：提供更友好的错误提示和操作指导

这次对接确保了前端登录功能与后端API的完全一致性，提供了更好的用户体验和更准确的错误处理。 