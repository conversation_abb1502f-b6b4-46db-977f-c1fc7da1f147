# 登录系统API

本文档描述登录系统的WebSocket API请求和响应格式。

## 重要说明

**统一登录方式**：系统已统一登录流程，不再区分Pro登录和普通登录。所有用户（包括Pro用户）都使用相同的`login_read`操作进行登录，服务器会根据数据库中的用户信息自动返回正确的Pro状态。

## 请求操作

- `login_read`: 统一用户登录验证（适用于所有用户类型）

## 1. 登录请求格式

**客户端请求**：

```json
{
  "action": "login_read",
  "content": {
    "username": "用户名",
    "password": "密码",
    "token": "令牌(可选)",
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

## 2. 后端处理逻辑

后端按以下顺序验证（统一处理所有用户类型）：

1. **用户名验证**：检查用户是否存在
2. **密码验证**：验证密码是否正确（如果提供token则跳过密码验证）
3. **Token验证**：如果提供token，验证token是否有效
4. **获取用户信息**：从数据库获取用户的Pro状态和其他信息

**重要**：服务器不应该再检查"Pro用户必须使用Pro登录"的逻辑，所有用户都通过统一的`login_read`接口登录。

## 3. 响应格式

### 3.1 登录成功

```json
{
  "action": "login_read_response",
  "status": "success",
  "message": "登录成功",
  "data": {
    "userInfo": {
      "username": "用户名",
      "token": "身份验证令牌",
      "lastLoginTime": "2023-06-01T12:00:00Z",
      "isPro": false
    },
    "homeConfig": {
      "gameName": "游戏名称",
      "cardKey": "卡密"
    }
  }
}
```

### 3.2 用户名错误

```json
{
  "action": "login_read_response",
  "status": "error",
  "message": "用户名错误",
  "data": null
}
```

### 3.3 密码错误

```json
{
  "action": "login_read_response",
  "status": "error",
  "message": "密码错误",
  "data": null
}
```

### 3.4 Token错误

```json
{
  "action": "login_read_response",
  "status": "error",
  "message": "令牌无效或已过期",
  "data": null
}
```

## 4. 参数说明

| 参数名称 | 类型 | 描述 | 必填 |
|---------|------|------|-------|
| username | string | 登录用户名 | 是 |
| password | string | 登录密码 | 是(Token登录时可选) |
| token | string | 身份验证令牌，用于自动登录 | 否 |
| createdAt | string | 请求创建时间(ISO 8601格式) | 是 |
| updatedAt | string | 请求更新时间(ISO 8601格式) | 是 |

## 5. 响应参数说明

| 参数名称 | 类型 | 描述 |
|---------|------|------|
| status | string | 请求状态："success" 或 "error" |
| message | string | 状态描述消息 |
| data.userInfo.username | string | 用户名 |
| data.userInfo.token | string | 身份验证令牌 |
| data.userInfo.lastLoginTime | string | 上次登录时间(ISO 8601格式) |
| data.userInfo.isPro | boolean | 用户的Pro状态（从数据库获取） |
| data.homeConfig.gameName | string | 当前游戏名称 |
| data.homeConfig.cardKey | string | 用户卡密 |

## 6. 错误消息列表

| 错误消息 | 触发条件 |
|---------|---------|
| 用户名错误 | 用户不存在 |
| 密码错误 | 密码不匹配 |
| 令牌无效或已过期 | token验证失败 |

## 7. 使用说明

- 前端只需提供用户名、密码（或token）和时间戳
- 后端验证用户名和密码/token
- 成功后从数据库获取用户的Pro状态返回给前端
- 失败时data字段为null，并返回具体的错误消息 