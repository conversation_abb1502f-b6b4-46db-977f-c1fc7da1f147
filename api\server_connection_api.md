# 服务器连接API

本文档描述客户端与服务器之间的WebSocket连接建立和维护过程。

## WebSocket连接

### 连接建立

客户端通过WebSocket协议连接到服务器。连接URL格式如下：

```
ws://{serverAddress}:{serverPort}/ws
```

其中：
- `serverAddress`: 服务器IP地址
- `serverPort`: 服务器端口号

### 连接保持

建立连接后，客户端和服务器之间应当保持连接状态。客户端可以定期发送心跳包来维持连接：

**客户端心跳请求**：

```json
{
  "action": "heartbeat",
  "content": {
    "timestamp": "2023-06-01T12:00:00Z"
  }
}
```

**服务器心跳响应**：

```json
{
  "action": "heartbeat_response",
  "status": "ok",
  "timestamp": "2023-06-01T12:00:01Z"
}
```

### 连接状态

客户端应当监控连接状态，在连接断开时尝试重新连接。连接状态包括：

- 已连接（Connected）
- 正在连接（Connecting）
- 断开连接（Disconnected）
- 连接错误（Error）

### 认证过程

建立连接后，客户端可以通过以下方式认证身份：

1. 使用用户名和密码登录（参见 [login_api.md](login_api.md)）
2. 使用令牌自动登录（参见 [login_api.md](login_api.md)）

### 错误处理

连接过程中可能出现的错误包括：

- 连接超时
- 服务器拒绝连接
- 认证失败
- 网络中断

客户端应当实现适当的错误处理和重试机制。

## 断开连接

客户端可以主动断开与服务器的连接，也可能由于网络问题被动断开连接。

### 主动断开

客户端主动断开连接时，应当先发送断开连接请求：

```json
{
  "action": "disconnect",
  "content": {
    "username": "用户名",
    "reason": "用户退出",
    "timestamp": "2023-06-01T12:00:00Z"
  }
}
```

然后关闭WebSocket连接。

### 被动断开

当网络中断或服务器主动断开连接时，客户端应当：

1. 检测连接状态
2. 通知用户连接已断开
3. 根据配置，自动尝试重新连接或等待用户指令

## 重连机制

客户端应当实现重连机制，在连接断开后自动尝试重新连接。重连策略包括：

1. 初始重连延迟（如1秒）
2. 重连失败后指数退避（如2秒、4秒、8秒...）
3. 最大重连次数或最大重连时间
4. 重连成功后重置重连计数器

## 安全性考虑

为确保通信安全，建议：

1. 在生产环境中使用WSS（WebSocket Secure）而非WS
2. 实现令牌认证机制
3. 对敏感数据（如密码）进行加密传输
4. 实现连接超时机制 