# 视野设置系统API

本文档描述视野设置系统的WebSocket API请求和响应格式。

## 请求和响应格式

系统使用三种主要操作：
- `fov_read`: 读取视野设置
- `fov_modify`: 修改视野设置
- `fov_measurement_start`: 开始FOV测量

### 读取视野设置 (fov_read)

**客户端请求**：

```json
{
  "action": "fov_read",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

**服务器响应**：

```json
{
  "action": "fov_read_response",
  "status": "ok",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "fov": 0.7,
    "fovTime": 500,
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T13:30:00Z"
  }
}
```

### 修改视野设置 (fov_modify)

**客户端请求**：

```json
{
  "action": "fov_modify",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "fov": 0.7,
    "fovTime": 500,
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

**服务器响应**：

```json
{
  "action": "fov_modify_response",
  "status": "ok",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "fov": 0.7,
    "fovTime": 500,
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

### 开始FOV测量 (fov_measurement_start)

**说明**：启动FOV自动测量功能。系统会自动检测目标并计算FOV值，然后等待测量完成（最多20秒）后返回结果。

**测量原理**：
- 系统通过检测游戏中的目标（如敌人）来计算视野角度
- 测量过程包括：目标检测 → 鼠标移动 → FOV计算 → 精度验证
- 测量结果会自动保存到数据库并更新全局变量

**前置条件**：
- 游戏必须处于运行状态
- 屏幕中需要有可检测的目标
- 用户需要保持准星对准目标区域
- 测量期间不要移动鼠标

**触发时机**：
- 用户在FOV配置页面点击"开始测量"按钮
- 需要自动校准FOV值时
- 更换游戏分辨率或显示器后重新校准

**客户端请求**：

```json
{
  "action": "fov_measurement_start",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "timestamp": "2023-06-01T14:30:00Z"
  }
}
```

**服务器响应 - 测量成功**：

```json
{
  "action": "fov_measurement_start_response",
  "status": "ok",
  "message": "FOV测量完成",
  "data": {
    "username": "testuser",
    "gameName": "CS:GO",
    "fov": 90.0,
    "fovTime": 100,
    "measuring": false,
    "timeout": false,
    "createdAt": "2023-06-01T14:30:00Z",
    "updatedAt": "2023-06-01T14:30:05Z"
  }
}
```

**服务器响应 - 测量超时**：

```json
{
  "action": "fov_measurement_start_response",
  "status": "ok",
  "message": "FOV测量超时，请重试",
  "data": {
    "username": "testuser",
    "gameName": "CS:GO",
    "measuring": false,
    "timeout": true,
    "message": "测量超时，请重试"
  }
}
```

**服务器响应 - 测量失败**：

```json
{
  "action": "fov_measurement_start_response",
  "status": "ok",
  "message": "FOV测量失败，请重试",
  "data": {
    "username": "testuser",
    "gameName": "CS:GO",
    "measuring": false,
    "error": "未检测到有效目标"
  }
}
```

**客户端请求**：

```json
{
  "action": "fov_measurement_start",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

**服务器响应**（测量完成后返回）：

```json
{
  "action": "fov_measurement_start_response",
  "status": "ok",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "fov": 90.0,
    "fovTime": 500,
    "measuring": false,
    "timeout": false
  },
  "message": "FOV测量完成"
}
```

## 测量流程说明

FOV测量是一个同步等待过程（带20秒超时），包含以下详细步骤：

### 🔄 **完整测量流程**

#### **第一阶段：请求处理**
1. **客户端发起测量请求**：发送 `fov_measurement_start` 请求
2. **服务器参数验证**：验证用户名、游戏名、卡密等参数
3. **更新全局变量**：设置用户信息到全局变量
4. **启动测量标志**：设置 `g_start_measure = true`

#### **第二阶段：测量执行**
5. **目标检测**：系统自动检测屏幕中的有效目标
6. **距离计算**：计算准星到目标的像素距离
7. **接近目标**：如果距离过远，自动移动鼠标接近目标
8. **精确测量**：当距离足够小时，开始精确的FOV测量
9. **鼠标移动**：向右移动固定像素（通常100像素）
10. **FOV计算**：根据移动距离和目标位移计算FOV比例
11. **修正移动**：使用计算出的FOV进行反向修正移动
12. **精度验证**：验证测量精度，确保误差在可接受范围内

#### **第三阶段：结果处理**
13. **测量完成**：设置 `g_start_measure = false`
14. **数据更新**：更新全局变量 `g_fov` 和 `g_fov_time`
15. **响应前端**：构建包含测量结果的响应
16. **数据库保存**：将测量结果保存到数据库
17. **发送响应**：向前端发送 `fov_measurement_start_response`

### ⏱️ **超时处理机制**

- **超时时间**：20秒
- **检查频率**：每100毫秒检查一次测量状态
- **超时行为**：
  - 强制停止测量（设置 `g_start_measure = false`）
  - 返回超时响应给前端
  - 不更新数据库（保持原有配置）

### 🎯 **测量精度说明**

- **目标距离要求**：准星到目标距离需小于2像素
- **移动精度**：测量完成后误差通常在1-3像素范围内
- **FOV范围**：测量结果通常在0.1-2.0之间（游戏相关）
- **重复性**：同一环境下多次测量结果应基本一致

### 🚨 **重要注意事项**

**用户操作要求**：
- 测量期间保持准星对准目标
- 不要移动鼠标或键盘
- 确保游戏窗口处于前台
- 屏幕中需要有清晰可见的目标

**技术限制**：
- 需要目标检测算法能够识别的目标类型
- 游戏分辨率和显示设置会影响测量结果
- 某些游戏的反作弊系统可能干扰测量

**错误处理**：
- 如果20秒内测量未完成，会返回超时错误
- 用户可以重新尝试测量
- 建议在不同游戏场景下多次测量取平均值

## 技术实现详解

### 🔧 **后端实现架构**

#### **1. 处理层 (handlers/fov_handlers.cpp)**
```cpp
// 主要职责：
- 接收前端请求并验证参数
- 调用服务层执行测量逻辑
- 处理响应数据并更新全局变量
- 更新数据库配置
- 发送响应给前端

// 关键函数：
std::string handleFovMeasurementStart(const Poco::JSON::Object::Ptr& content)
```

#### **2. 服务层 (blserver/fov_service.cpp)**
```cpp
// 主要职责：
- 启动FOV测量并等待完成
- 实现20秒超时机制
- 构建测量结果响应
- 处理测量异常情况

// 关键函数：
Poco::JSON::Object::Ptr startFovMeasurementWithTimeout(
    const std::string& username,
    const std::string& gameName,
    int timeoutSeconds
)
```

#### **3. 测量执行层 (lkm/keymouse.cpp)**
```cpp
// 主要职责：
- 实际的鼠标移动和FOV计算
- 目标检测和距离计算
- 测量精度控制
- 设置测量完成标志

// 关键函数：
void updateFOVMeasurement()
```

### 🎯 **核心算法说明**

#### **FOV计算公式**
```
FOV比例 = 实际移动距离 / 预期移动距离
最终FOV = 基准FOV * FOV比例
```

#### **测量步骤详解**
1. **目标距离检测**：`distance = sqrt(dx² + dy²)`
2. **接近阶段**：如果距离 > 2像素，移动鼠标接近目标
3. **测量阶段**：向右移动100像素，记录目标位移
4. **计算阶段**：`fov_ratio = target_movement / mouse_movement`
5. **修正阶段**：使用新FOV值进行反向移动验证

### 📊 **全局变量管理**

#### **关键变量 (utils/gmodels.cpp)**
```cpp
namespace webui::fov {
    double g_fov = 90.0;              // 当前FOV值
    int g_fov_time = 100;             // FOV时间
    bool g_start_measure = false;     // 测量状态标志
}
```

#### **变量同步流程**
1. **测量启动**：`g_start_measure = true`
2. **测量进行**：底层算法检测此标志并执行测量
3. **测量完成**：底层设置 `g_start_measure = false` 并更新 `g_fov`
4. **数据同步**：handlers层读取新值并保存到数据库

### 🔄 **数据流转图**

```
前端请求 → handlers层 → 服务层 → 全局变量
    ↓           ↓         ↓         ↓
  响应UI    ← 构建响应  ← 等待完成  ← 测量算法
    ↓           ↓         ↓         ↓
  更新UI    ← 发送响应  ← 更新DB   ← 保存结果
```

## 参数说明

| 参数名称 | 类型 | 描述 | 可选值/范围 | 默认值 |
|---------|------|------|-------|--------|
| fov | double | 视野范围值 | 0.0 - 180.0 | 90.0 |
| fovTime | int | 视野变化过渡时间(毫秒) | 0 - 1000 | 100 |
| measuring | boolean | 测量状态标志 | true/false | false |
| timeout | boolean | 超时状态标志 | true/false | false |

## 使用示例

### 📱 **前端实现示例**

#### **JavaScript/TypeScript 示例**
```javascript
class FovController {
  async startFovMeasurement(username, gameName, cardKey) {
    try {
      // 显示加载状态
      this.showMeasuringState();

      // 发送测量请求
      const response = await this.sendMessage({
        action: "fov_measurement_start",
        content: {
          username: username,
          gameName: gameName,
          cardKey: cardKey,
          timestamp: new Date().toISOString()
        }
      });

      // 处理响应
      if (response.status === "ok") {
        if (response.data.timeout) {
          // 超时处理
          this.showError("FOV测量超时，请重试");
          this.hideMeasuringState();
        } else if (response.data.measuring === false) {
          // 测量成功
          this.updateFovValue(response.data.fov);
          this.showSuccess(`FOV测量完成: ${response.data.fov}`);
          this.hideMeasuringState();
        }
      } else {
        // 错误处理
        this.showError(response.message);
        this.hideMeasuringState();
      }
    } catch (error) {
      console.error("FOV测量请求失败:", error);
      this.showError("网络错误，请重试");
      this.hideMeasuringState();
    }
  }

  showMeasuringState() {
    // 显示加载动画
    // 禁用测量按钮
    // 显示提示信息："请保持准星对准目标，测量进行中..."
  }

  hideMeasuringState() {
    // 隐藏加载动画
    // 启用测量按钮
  }
}
```

#### **Flutter/Dart 示例**
```dart
class FovController {
  Future<void> startFovMeasurement() async {
    try {
      // 更新UI状态
      _isMeasuring.value = true;
      _measurementStatus.value = "FOV测量进行中，请保持准星对准目标...";

      // 发送请求
      final response = await _serverService.sendMessage({
        'action': 'fov_measurement_start',
        'content': {
          'username': _username,
          'gameName': _gameName,
          'cardKey': _cardKey,
          'timestamp': DateTime.now().toIso8601String(),
        }
      });

      // 处理响应
      if (response['status'] == 'ok') {
        final data = response['data'];
        if (data['timeout'] == true) {
          _showError("FOV测量超时，请重试");
        } else {
          _fov.value = data['fov'];
          _fovTime.value = data['fovTime'];
          _showSuccess("FOV测量完成: ${data['fov']}");
        }
      }
    } catch (e) {
      _showError("FOV测量失败: $e");
    } finally {
      _isMeasuring.value = false;
    }
  }
}
```

### 🎯 **最佳实践**

#### **1. 用户体验优化**
- **加载状态**：显示明确的加载动画和进度提示
- **用户指导**：提供清晰的操作指导和注意事项
- **错误处理**：友好的错误提示和重试机制
- **超时处理**：20秒超时后提供重试选项

#### **2. 测量环境要求**
- **目标选择**：选择清晰、对比度高的目标
- **距离控制**：确保准星与目标距离适中
- **环境稳定**：避免在移动或变化的场景中测量
- **多次测量**：建议进行2-3次测量取平均值

#### **3. 错误处理策略**
```javascript
const handleFovMeasurementError = (error) => {
  switch (error.type) {
    case 'timeout':
      return "测量超时，请确保屏幕中有清晰目标后重试";
    case 'no_target':
      return "未检测到有效目标，请调整准星位置";
    case 'network_error':
      return "网络连接异常，请检查网络后重试";
    default:
      return "测量失败，请重试";
  }
};
```

#### **4. 性能优化建议**
- **请求频率**：避免频繁发起测量请求
- **超时设置**：合理设置前端超时时间（建议25秒）
- **状态管理**：正确管理测量状态，避免重复请求
- **资源清理**：测量完成后及时清理相关资源

## 错误处理

### 🚨 **常见错误类型**

| 错误类型 | 错误信息 | 解决方案 |
|---------|----------|----------|
| **参数错误** | "用户名和游戏名不能为空" | 检查请求参数完整性 |
| **超时错误** | "FOV测量超时" | 重新尝试，确保有有效目标 |
| **目标检测失败** | "未检测到有效目标" | 调整准星位置，选择清晰目标 |
| **网络错误** | "请求失败" | 检查网络连接 |
| **权限错误** | "卡密验证失败" | 检查卡密有效性 |

### 📋 **标准错误响应格式**

```json
{
  "action": "fov_measurement_start_response",
  "status": "ok",
  "message": "错误描述信息",
  "data": {
    "username": "用户名",
    "gameName": "游戏名",
    "measuring": false,
    "timeout": true,
    "error": "具体错误原因"
  }
}
```