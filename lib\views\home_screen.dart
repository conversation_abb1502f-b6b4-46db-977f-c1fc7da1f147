// ignore_for_file: use_super_parameters, library_private_types_in_public_api, unused_import, sized_box_for_whitespace

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:getwidget/getwidget.dart';
import '../component/card_component.dart';
import '../component/input_component.dart';
import '../controllers/home_controller.dart';
import '../controllers/header_controller.dart';
import '../services/server_service.dart'; // 导入服务器服务
import '../models/auth_model.dart'; // 导入认证模型
import '../models/game_model.dart'; // 导入游戏模型
import '../models/login_model.dart'; // 导入登录模型
import '../component/message_component.dart'; // 导入消息组件

/// 首页配置页面
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) {
        final homeController = HomeController(
          serverService: Provider.of<ServerService>(context, listen: false),
          authModel: Provider.of<AuthModel>(context, listen: false),
          gameModel: Provider.of<GameModel>(context, listen: false),
          loginModel: Provider.of<LoginModel>(context, listen: false),
        );
        homeController.setHeaderController(
          Provider.of<HeaderController>(context, listen: false)
        );
        return homeController;
      },
      child: const _HomeScreenView(),
    );
  }
}

/// 首页视图组件
class _HomeScreenView extends StatelessWidget {
  // 常量配置
  static const double _smallScreenPadding = 8.0;
  static const double _normalPadding = 16.0;
  static const double _headerPadding = 16.0;
  static const double _titleFontSize = 24.0;
  static const double _buttonSpacing = 12.0;
  static const double _cardSpacing = 2.0;
  static const double _cardSpacingLarge = 4.0;
  static const double _bottomBarPadding = 12.0;
  static const double _gameIconSize = 24.0;
  static const double _selectedBarFontSize = 16.0;
  static const int _smallScreenBreakpoint = 600;
  static const int _tabletBreakpoint = 900;
  static const int _mobileColumns = 2;
  static const int _tabletColumns = 3;
  static const int _desktopColumns = 4;

  const _HomeScreenView();

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < _smallScreenBreakpoint;
    final homeController = Provider.of<HomeController>(context);
    
    return Scaffold(
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context, homeController),
            _buildCardKeyExpireTime(context, isSmallScreen, homeController),
            _buildCardKeyInput(context, isSmallScreen, homeController),
            _buildHeartbeatIntervalInput(context, isSmallScreen, homeController),
            Expanded(
              child: _buildGameSelection(context, isSmallScreen, homeController),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildSelectedGameBar(context, isSmallScreen),
    );
  }
  
  /// 构建顶部标题和操作按钮
  Widget _buildHeader(BuildContext context, HomeController controller) {
    return Container(
      padding: const EdgeInsets.all(_headerPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: _buildTitle(),
          ),
          Flexible(
            child: _buildActionButtons(context),
          ),
        ],
      ),
    );
  }

  /// 构建页面标题
  Widget _buildTitle() {
    return const Text(
      '首页配置',
      style: TextStyle(
        fontSize: _titleFontSize,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  /// 构建操作按钮组
  Widget _buildActionButtons(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isVerySmallScreen = screenSize.width < 350; // 极小屏幕

    return Row(
      mainAxisSize: MainAxisSize.min, // 使用最小尺寸
      children: [
        _buildRefreshButton(context),
        SizedBox(width: isVerySmallScreen ? 4 : 8), // 极小屏幕进一步减小间距
        Flexible( // 使保存按钮可以适应空间
          child: _buildSaveButton(context),
        ),
      ],
    );
  }

  /// 构建刷新按钮
  Widget _buildRefreshButton(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isVerySmallScreen = screenSize.width < 350;
    final buttonSize = isVerySmallScreen ? 28.0 : 32.0; // 极小屏幕使用更小尺寸

    return Tooltip(
      message: '刷新参数',
      child: Container(
        height: buttonSize,
        width: buttonSize,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [
              Color(0xFF4CAF50), // 浅绿色
              Color(0xFF2E7D32), // 深绿色
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(buttonSize / 2), // 调整圆角以适应新尺寸
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF2E7D32).withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(buttonSize / 2),
            onTap: () => _handleRefreshConfig(context),
            child: Container(
              alignment: Alignment.center,
              child: Icon(
                Icons.refresh,
                color: Colors.white,
                size: isVerySmallScreen ? 16 : 18, // 根据屏幕尺寸调整图标大小
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建保存按钮
  Widget _buildSaveButton(BuildContext context) {
    return Consumer<HomeController>(
      builder: (context, controller, child) {
        final isSaving = controller.isSaving;
        
        return Tooltip(
          message: isSaving ? '正在保存...' : '保存当前设置',
          child: Container(
            height: 32,
            constraints: const BoxConstraints(
              minWidth: 60, // 最小宽度
              maxWidth: 80, // 最大宽度
            ),
            decoration: BoxDecoration(
              color: isSaving ? Colors.grey : Colors.blue,
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                  color: (isSaving ? Colors.grey : Colors.blue).withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(6),
                onTap: isSaving ? null : () => _handleSaveConfig(context),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center, // 居中对齐
                  children: [
                    if (isSaving) ...[
                      const SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                    ] else ...[
                      const Icon(
                        Icons.save,
                        color: Colors.white,
                        size: 18,
                      ),
                    ],
                    const SizedBox(width: 6),
                    Text(
                      isSaving ? '保存中' : '保存',  // 移除省略号，保持长度一致
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建卡密输入框
  Widget _buildCardKeyInput(BuildContext context, bool isSmallScreen, HomeController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? _smallScreenPadding : _normalPadding, 
        vertical: _smallScreenPadding
      ),
      child: InputComponent.createInput(
        label: '卡密',
        hint: '请输入您的卡密',
        icon: Icons.vpn_key,
        controller: controller.cardKeyController,
        onChanged: controller.onCardKeyChanged,
      ),
    );
  }

  /// 构建卡密到期时间显示
  Widget _buildCardKeyExpireTime(BuildContext context, bool isSmallScreen, HomeController controller) {
    return Consumer<GameModel>(
      builder: (context, gameModel, child) {
        final expireTime = gameModel.cardKeyExpireTime;

        // 默认显示信息
        String displayText = '卡密到期时间: 未获取';
        Color textColor = Colors.grey.shade600;
        IconData iconData = Icons.schedule;

        // 如果有到期时间数据，解析并计算剩余天数
        if (expireTime.isNotEmpty) {
          final expireDateTime = DateTime.tryParse(expireTime);

          if (expireDateTime != null) {
            final now = DateTime.now();
            final difference = expireDateTime.difference(now);
            final remainingDays = difference.inDays;
            final remainingHours = difference.inHours;

            // 格式化显示日期 (YYYY-MM-DD HH:mm)
            final formattedDate = '${expireDateTime.year}-${expireDateTime.month.toString().padLeft(2, '0')}-${expireDateTime.day.toString().padLeft(2, '0')} ${expireDateTime.hour.toString().padLeft(2, '0')}:${expireDateTime.minute.toString().padLeft(2, '0')}';

            // 修复逻辑：只有当到期时间真的过了才算到期
            if (expireDateTime.isAfter(now)) {
              // 还没到期
              if (remainingDays > 30) {
                displayText = '卡密到期: $formattedDate (剩余$remainingDays天)';
                textColor = Colors.green.shade600;
                iconData = Icons.check_circle;
              } else if (remainingDays > 7) {
                displayText = '卡密到期: $formattedDate (剩余$remainingDays天)';
                textColor = Colors.orange.shade600;
                iconData = Icons.warning;
              } else if (remainingDays > 0) {
                displayText = '卡密到期: $formattedDate (剩余$remainingDays天，即将到期)';
                textColor = Colors.red.shade600;
                iconData = Icons.error;
              } else if (remainingHours > 0) {
                // 不到1天但还有几小时
                displayText = '卡密到期: $formattedDate (剩余$remainingHours小时)';
                textColor = Colors.red.shade600;
                iconData = Icons.error;
              } else {
                // 不到1小时但还没到期
                final remainingMinutes = difference.inMinutes;
                displayText = '卡密到期: $formattedDate (剩余$remainingMinutes分钟)';
                textColor = Colors.red.shade700;
                iconData = Icons.error;
              }
            } else {
              // 真的已经到期了
              displayText = '卡密已到期: $formattedDate';
              textColor = Colors.red.shade700;
              iconData = Icons.error_outline;
            }
          } else {
            // 有数据但解析失败，直接显示原始数据
            displayText = '卡密到期时间: $expireTime';
            textColor = Colors.grey.shade600;
            iconData = Icons.schedule;
          }
        }

        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? _smallScreenPadding : _normalPadding,
            vertical: _smallScreenPadding / 2
          ),
          child: Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                Icon(
                  iconData,
                  color: textColor,
                  size: 20.0,
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: Text(
                    displayText,
                    style: TextStyle(
                      fontSize: 14.0,
                      color: textColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建心跳频率输入框
  Widget _buildHeartbeatIntervalInput(BuildContext context, bool isSmallScreen, HomeController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? _smallScreenPadding : _normalPadding, 
        vertical: _smallScreenPadding
      ),
      child: InputComponent.createInput(
        label: '状态栏查询频率 (秒)',
        hint: '设置页头状态信息更新频率 (1-300秒)',
        icon: Icons.refresh,
        controller: controller.heartbeatIntervalController,
        onChanged: controller.onHeartbeatIntervalChanged,
        keyboardType: TextInputType.number,
      ),
    );
  }
  
  /// 构建游戏选择区域
  Widget _buildGameSelection(BuildContext context, bool isSmallScreen, HomeController controller) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 确保约束有效
        if (constraints.maxHeight <= 0 || constraints.maxWidth <= 0) {
          return const SizedBox.shrink();
        }
        
        // Android平台优化：使用更简单的布局结构
        return Container(
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Container(
              width: constraints.maxWidth,
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              padding: EdgeInsets.all(isSmallScreen ? _smallScreenPadding : _normalPadding),
              child: _buildGameCards(
                context, 
                constraints, 
                isSmallScreen, 
                controller
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建游戏卡片网格
  Widget _buildGameCards(
    BuildContext context, 
    BoxConstraints constraints,
    bool isSmallScreen, 
    HomeController controller
  ) {
    final columnsToUse = _calculateColumns(constraints.maxWidth, isSmallScreen);
    final spacing = isSmallScreen ? _cardSpacing : _cardSpacingLarge;
    
    return CardSelectComponent.createMixedCards(
      items: controller.getGameCards(),
      onSelected: controller.selectGame,
      initialSelectedId: controller.getInitialSelectedId(),
      columns: columnsToUse,
      spacing: spacing,
    );
  }

  /// 计算网格列数
  int _calculateColumns(double maxWidth, bool isSmallScreen) {
    if (isSmallScreen) return _mobileColumns;
    return maxWidth < _tabletBreakpoint ? _tabletColumns : _desktopColumns;
  }
  
  /// 构建已选择游戏显示条
  Widget _buildSelectedGameBar(BuildContext context, bool isSmallScreen) {
    return Consumer<HomeController>(
      builder: (context, controller, child) {
        final selectedGame = controller.selectedGame;
        if (selectedGame == null) return const SizedBox.shrink();
        
        return Container(
          color: Colors.blue.shade50,
          padding: EdgeInsets.symmetric(
            vertical: _bottomBarPadding, 
            horizontal: isSmallScreen ? _normalPadding : _headerPadding * 1.5
          ),
          child: _buildSelectedGameContent(selectedGame),
        );
      },
    );
  }

  /// 构建已选择游戏内容
  Widget _buildSelectedGameContent(CardItemConfig selectedGame) {
    return Row(
      children: [
        if (selectedGame.imagePath != null) ...[
          Image.asset(
            selectedGame.imagePath!,
            width: _gameIconSize,
            height: _gameIconSize,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: _gameIconSize,
                height: _gameIconSize,
                color: Colors.grey.shade100,
                child: Icon(
                  Icons.games,
                  size: _gameIconSize - 4,
                  color: Colors.grey,
                ),
              );
            },
          ),
          const SizedBox(width: _smallScreenPadding),
        ],
        Expanded( // 使文本能够适应剩余空间
          child: Text(
            '已选择: ${selectedGame.title}',
            style: const TextStyle(
              fontSize: _selectedBarFontSize,
              fontWeight: FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis, // 文本溢出时显示省略号
          ),
        ),
      ],
    );
  }

  /// 处理刷新配置
  void _handleRefreshConfig(BuildContext context) {
    final controller = Provider.of<HomeController>(context, listen: false);
    controller.refreshHomeConfig();
    
    MessageComponent.showIconToast(
      context: context,
      message: '正在刷新首页配置...',
      type: MessageType.info,
      duration: const Duration(seconds: 1),
    );
  }
  
  /// 处理保存配置
  void _handleSaveConfig(BuildContext context) {
    final controller = Provider.of<HomeController>(context, listen: false);
    controller.saveHomeConfig(context);
  }
} 