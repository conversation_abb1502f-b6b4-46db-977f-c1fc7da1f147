// ignore_for_file: unnecessary_import, prefer_final_fields

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/web_font_config.dart';

/// UI配置模型 - 管理UI相关配置
class UIConfigModel extends ChangeNotifier {
  // 页头下拉框配置
  Map<String, dynamic> _headerDropdownConfig = {
    //宽度
    'width': 100.0,
    //高度
    'height': 100.0,
    //背景色
    'backgroundColor': Colors.white,
    //字体颜色
    'textColor': Colors.black,
    //字体大小
    'fontSize': 16.0,
    //字体粗细
    'fontWeight': FontWeight.normal,
  };
  
  // 字体配置
  String _fontFamily = 'Roboto';
  bool _useGoogleFonts = !kIsWeb; // Web环境下默认禁用Google Fonts
  List<String> _availableFonts = [
    'Roboto',
    'Lato',
    'Open Sans',
    'Montserrat',
    'Oswald',
    'Noto Sans',
    'Source Sans Pro',
    'Poppins',
  ];
  
  // Getters
  Map<String, dynamic> get headerDropdownConfig => _headerDropdownConfig;
  
  // 获取下拉框宽度
  double get dropdownWidth => _headerDropdownConfig['width'] as double;
  
  // 获取下拉框高度 
  double get dropdownHeight => _headerDropdownConfig['height'] as double;
  
  // 获取背景色
  Color get backgroundColor => _headerDropdownConfig['backgroundColor'] as Color;
  
  // 获取文字颜色
  Color get textColor => _headerDropdownConfig['textColor'] as Color;
  
  // 获取字体大小
  double get fontSize => _headerDropdownConfig['fontSize'] as double;
  
  // 获取字体粗细
  FontWeight get fontWeight => _headerDropdownConfig['fontWeight'] as FontWeight;
  
  // 字体相关 getters
  String get fontFamily => _fontFamily;
  bool get useGoogleFonts => _useGoogleFonts && WebFontConfig.shouldUseGoogleFonts();
  List<String> get availableFonts => _availableFonts;
  
  // 获取当前文本主题样式
  TextStyle getTextStyle({TextStyle? baseStyle}) {
    if (_useGoogleFonts && !kIsWeb) {
      // 只在非Web环境下使用Google Fonts
      try {
        return GoogleFonts.getFont(
          _fontFamily,
          textStyle: baseStyle,
          fontSize: baseStyle?.fontSize ?? _headerDropdownConfig['fontSize'] as double,
          fontWeight: baseStyle?.fontWeight ?? _headerDropdownConfig['fontWeight'] as FontWeight,
          color: baseStyle?.color ?? _headerDropdownConfig['textColor'] as Color,
        );
      } catch (e) {
        // Google Fonts加载失败时的降级方案
        debugPrint('Google Fonts加载失败，使用系统字体: $e');
      }
    }

    // Web环境或Google Fonts失败时使用系统字体
    return TextStyle(
      fontFamily: _getSystemFontFamily(),
      fontSize: baseStyle?.fontSize ?? _headerDropdownConfig['fontSize'] as double,
      fontWeight: baseStyle?.fontWeight ?? _headerDropdownConfig['fontWeight'] as FontWeight,
      color: baseStyle?.color ?? _headerDropdownConfig['textColor'] as Color,
    );
  }
  
  // 获取系统字体名称
  String _getSystemFontFamily() {
    return WebFontConfig.getWebSafeFont(_fontFamily);
  }

  // 获取文本主题
  TextTheme getTextTheme(TextTheme baseTheme) {
    if (_useGoogleFonts && !kIsWeb) {
      // 只在非Web环境下使用Google Fonts
      try {
        return GoogleFonts.getTextTheme(_fontFamily, baseTheme);
      } catch (e) {
        debugPrint('Google Fonts文本主题加载失败，使用系统字体: $e');
      }
    }

    // Web环境或Google Fonts失败时使用系统字体
    return baseTheme.copyWith(
      bodyLarge: baseTheme.bodyLarge?.copyWith(fontFamily: _getSystemFontFamily()),
      bodyMedium: baseTheme.bodyMedium?.copyWith(fontFamily: _getSystemFontFamily()),
      bodySmall: baseTheme.bodySmall?.copyWith(fontFamily: _getSystemFontFamily()),
      headlineLarge: baseTheme.headlineLarge?.copyWith(fontFamily: _getSystemFontFamily()),
      headlineMedium: baseTheme.headlineMedium?.copyWith(fontFamily: _getSystemFontFamily()),
      headlineSmall: baseTheme.headlineSmall?.copyWith(fontFamily: _getSystemFontFamily()),
      titleLarge: baseTheme.titleLarge?.copyWith(fontFamily: _getSystemFontFamily()),
      titleMedium: baseTheme.titleMedium?.copyWith(fontFamily: _getSystemFontFamily()),
      titleSmall: baseTheme.titleSmall?.copyWith(fontFamily: _getSystemFontFamily()),
      labelLarge: baseTheme.labelLarge?.copyWith(fontFamily: _getSystemFontFamily()),
      labelMedium: baseTheme.labelMedium?.copyWith(fontFamily: _getSystemFontFamily()),
      labelSmall: baseTheme.labelSmall?.copyWith(fontFamily: _getSystemFontFamily()),
    );
  }
  
  // 初始化 - 从持久化存储加载设置
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 这里可以从持久化存储加载自定义的UI配置
    // 由于UI配置包含复杂对象如Color、FontWeight等，需要特殊处理
    
    // 示例：只加载简单类型
    final width = prefs.getDouble('ui_dropdown_width');
    if (width != null) {
      _headerDropdownConfig['width'] = width;
    }
    
    final height = prefs.getDouble('ui_dropdown_height');
    if (height != null) {
      _headerDropdownConfig['height'] = height;
    }
    
    final fontSize = prefs.getDouble('ui_dropdown_font_size');
    if (fontSize != null) {
      _headerDropdownConfig['fontSize'] = fontSize;
    }
    
    // 加载字体配置
    final fontFamily = prefs.getString('ui_font_family');
    if (fontFamily != null && _availableFonts.contains(fontFamily)) {
      _fontFamily = fontFamily;
    }
    
    final useGoogleFonts = prefs.getBool('ui_use_google_fonts');
    if (useGoogleFonts != null && !kIsWeb) {
      // 只在非Web环境下加载Google Fonts设置
      _useGoogleFonts = useGoogleFonts;
    } else if (kIsWeb) {
      // Web环境下强制禁用
      _useGoogleFonts = false;
    }
    
    notifyListeners();
  }
  
  // 更新下拉框宽度
  Future<void> updateDropdownWidth(double width) async {
    _headerDropdownConfig['width'] = width;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新下拉框高度
  Future<void> updateDropdownHeight(double height) async {
    _headerDropdownConfig['height'] = height;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新背景色
  Future<void> updateBackgroundColor(Color color) async {
    _headerDropdownConfig['backgroundColor'] = color;
    notifyListeners();
    // 注意：颜色对象无法直接保存到SharedPreferences，需要特殊处理
  }
  
  // 更新文本颜色
  Future<void> updateTextColor(Color color) async {
    _headerDropdownConfig['textColor'] = color;
    notifyListeners();
    // 注意：颜色对象无法直接保存到SharedPreferences，需要特殊处理
  }
  
  // 更新字体大小
  Future<void> updateFontSize(double size) async {
    _headerDropdownConfig['fontSize'] = size;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新字体粗细
  Future<void> updateFontWeight(FontWeight weight) async {
    _headerDropdownConfig['fontWeight'] = weight;
    notifyListeners();
    // 注意：FontWeight对象无法直接保存到SharedPreferences，需要特殊处理
  }
  
  // 更新字体
  Future<void> updateFontFamily(String fontFamily) async {
    if (_availableFonts.contains(fontFamily)) {
      _fontFamily = fontFamily;
      await _saveSettings();
      notifyListeners();
    }
  }
  
  // 切换是否使用Google字体
  Future<void> toggleGoogleFonts(bool useGoogleFonts) async {
    // Web环境下强制禁用Google Fonts
    if (kIsWeb) {
      _useGoogleFonts = false;
      debugPrint('Web环境下禁用Google Fonts以避免资源加载错误');
    } else {
      _useGoogleFonts = useGoogleFonts;
    }
    await _saveSettings();
    notifyListeners();
  }
  
  // 保存设置到持久化存储
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 保存简单类型
    await prefs.setDouble('ui_dropdown_width', _headerDropdownConfig['width'] as double);
    await prefs.setDouble('ui_dropdown_height', _headerDropdownConfig['height'] as double);
    await prefs.setDouble('ui_dropdown_font_size', _headerDropdownConfig['fontSize'] as double);
    
    // 保存字体配置
    await prefs.setString('ui_font_family', _fontFamily);
    await prefs.setBool('ui_use_google_fonts', _useGoogleFonts);
    
    // 复杂类型如Color和FontWeight需要转换成可保存的格式
  }
} 