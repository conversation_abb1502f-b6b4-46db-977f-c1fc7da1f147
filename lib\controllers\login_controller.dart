// ignore_for_file: use_build_context_synchronously, unnecessary_string_interpolations, prefer_function_declarations_over_variables, unused_field, unused_import, unnecessary_null_comparison

import 'package:flutter/material.dart';
import '../component/message_component.dart';
import '../utils/logger.dart';
import '../services/server_service.dart'; // 导入服务器服务
import '../services/auth_service.dart'; // 导入认证服务
import '../models/auth_model.dart'; // 导入认证模型
import '../models/login_model.dart'; // 导入登录模型
import '../models/game_model.dart'; // 导入游戏模型
import 'dart:async';
import 'dart:convert'; // 导入JSON转换库
import 'package:provider/provider.dart';
import '../controllers/side_controller.dart';

/// 登录控制器，处理登录页面的所有业务逻辑
class LoginController extends ChangeNotifier {
  static const Duration _loginTimeout = Duration(seconds: 10);
  static const String _logTag = 'LoginController';
  
  final log = Logger();
  final ServerService _serverService;
  final AuthService _authService;
  final AuthModel _authModel;
  final LoginModel _loginModel;
  final GameModel _gameModel;
  
  final TextEditingController usernameController;
  final TextEditingController passwordController;
  final TextEditingController serverAddressController;
  final TextEditingController portController;
  
  bool isLoading = false;
  List<bool> passwordVisibleList = [false, false, false, false];
  
  Timer? _loginTimer;
  VoidCallback? _serverStatusListener;
  bool _isDisposed = false;
  
  // 错误消息
  String? _errorMessage;
  String? get errorMessage => _errorMessage;
  
  // 控制器列表
  List<TextEditingController> get controllers => [
    serverAddressController,
    portController,
    usernameController,
    passwordController
  ];
  
  // 登录超时计时器
  Timer? _loginRequestTimer;
  
  // 构造函数，初始化控制器
  LoginController({
    required ServerService serverService,
    required AuthService authService,
    required AuthModel authModel,
    required LoginModel loginModel,
    required GameModel gameModel,
  })  : _serverService = serverService,
        _authService = authService,
        _authModel = authModel,
        _loginModel = loginModel,
        _gameModel = gameModel,
        usernameController = TextEditingController(
          text: authModel.username.isNotEmpty ? authModel.username : loginModel.username,
        ),
        passwordController = TextEditingController(
          text: (authModel.rememberPassword || loginModel.password.isNotEmpty) 
              ? loginModel.password : '',
        ),
        serverAddressController = TextEditingController(text: loginModel.serverAddress),
        portController = TextEditingController(text: loginModel.serverPort) {
    _initController();
  }
  
  void _initController() {
    log.i(_logTag, '登录控制器初始化');
    
    _syncUsernames();
    _setupListeners();
  }
  
  void _syncUsernames() {
    if (_authModel.username.isNotEmpty && _loginModel.username != _authModel.username) {
      _loginModel.updateUsername(_authModel.username);
    }
  }
  
  void _setupListeners() {
    _serverStatusListener = () => notifyListeners();
    _serverService.addListener(_serverStatusListener!);
    _serverService.addMessageListener(_handleServerMessage);
  }
  
  // 更新全局变量并记录日志
  void updateServerAddress(String value) {
    _loginModel.updateServerAddress(value);
    log.i(_logTag, '服务器地址已更新', value);
  }
  
  void updatePort(String value) {
    _loginModel.updateServerPort(value);
    log.i(_logTag, '端口已更新', value);
  }
  
  void updateUsername(String value) {
    _loginModel.updateUsername(value);
    log.i(_logTag, '用户名已更新', value);
  }
  
  void updatePassword(String value) {
    _loginModel.updatePassword(value);
    log.i(_logTag, '密码已更新', '******');
  }
  
  // 更新密码可见性
  void updatePasswordVisibility(int index, bool isVisible) {
    passwordVisibleList[index] = isVisible;
    notifyListeners();
  }

  // 处理服务器消息
  void _handleServerMessage(dynamic message) {
    // 检查控制器是否已被销毁
    if (_isDisposed) return;
    
    try {
      Map<String, dynamic> responseData = jsonDecode(message.toString());
      final String action = responseData['action'] ?? '';
      
      // 只处理登录相关消息
      if (action != 'login_read' && action != 'login_read_response' && action != 'home_read') return;
      
      log.i(_logTag, '收到服务器消息', {
        'action': action,
        'status': responseData['status'],
        'hasData': responseData['data'] != null,
      });
      
      switch (action) {
        case 'login_read':
        case 'login_read_response':
          _handleLoginResponse(responseData);
          break;
        case 'home_read':
          _handleHomeConfigResponse(responseData);
          break;
      }
    } catch (e) {
      log.e(_logTag, '解析服务器消息失败', {
        'error': e.toString(),
        'message': message.toString(),
        'isLoading': isLoading,
      });
      
      // 如果当前正在登录过程中，显示解析错误提示
      if (isLoading) {
        final context = WidgetsBinding.instance.focusManager.primaryFocus?.context;
        if (context != null) {
          _showToast(context, '服务器响应格式错误，请稍后重试', MessageType.error);
        }
        
        isLoading = false;
        notifyListeners();
      }
    }
  }
  
  // 显示Toast消息
  void _showToast(BuildContext context, String message, MessageType type, {Duration duration = const Duration(seconds: 2)}) {
    if (context.mounted) {
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: type,
        duration: duration,
      );
    }
  }

  // 处理登录按钮点击
  Future<void> handleLogin(BuildContext context) async {
    if (!_validateInput(context) || !await _ensureConnection(context)) return;
    
    _startLogin();
    
    try {
      await _updateLoginData();
      await _sendLoginRequest();
      _showToast(context, '登录请求已发送，等待响应...', MessageType.info);
    } catch (e) {
      _handleLoginError(context, e);
    }
  }

  bool _validateInput(BuildContext context) {
    if (usernameController.text.trim().isEmpty || passwordController.text.trim().isEmpty) {
      _showToast(context, '用户名和密码不能为空', MessageType.warning);
      return false;
    }
    return true;
  }

  Future<bool> _ensureConnection(BuildContext context) async {
    if (_serverService.isConnected) return true;
    
    _showToast(context, '正在连接到服务器...', MessageType.info);
    
    await _serverService.handleConnectService(
      context,
      serverAddressController.text,
      portController.text,
      _loginModel.token,
    );
    
    if (!_serverService.isConnected) {
      _showToast(context, '无法连接到服务器，请检查服务器设置', MessageType.warning);
      return false;
    }
    
    return true;
  }

  void _startLogin() {
    isLoading = true;
    notifyListeners();
    
    _loginTimer?.cancel();
    _loginTimer = Timer(_loginTimeout, _handleLoginTimeout);
  }

  void _handleLoginTimeout() {
    if (isLoading) {
      isLoading = false;
      notifyListeners();
      
      final context = WidgetsBinding.instance.focusManager.primaryFocus?.context;
      if (context != null) {
        _showToast(
          context, 
          '登录请求超时，请检查网络连接或稍后重试', 
          MessageType.warning,
          duration: const Duration(seconds: 3)
        );
      }
      
      log.w(_logTag, '登录请求超时', {
        'username': _loginModel.username,
        'serverConnected': _serverService.isConnected,
        'timeout': _loginTimeout.inSeconds,
      });
    }
  }

  Future<void> _updateLoginData() async {
    await _loginModel.updateUsername(usernameController.text.trim());
    await _loginModel.updatePassword(passwordController.text);
  }

  Future<void> _sendLoginRequest() async {
    final request = _buildLoginRequest();
    
    if (!_serverService.isConnected) {
      throw Exception('WebSocket连接已断开');
    }
    
    final requestJson = jsonEncode(request);
    _serverService.sendMessage(requestJson);
    
    // 记录详细的请求信息（密码脱敏）
    final logRequest = Map<String, dynamic>.from(request);
    if (logRequest['content'] != null && logRequest['content']['password'] != null) {
      logRequest['content']['password'] = '******';
    }
    
    log.i(_logTag, '发送登录请求', {
      'username': _loginModel.username,
      'hasToken': _loginModel.token.isNotEmpty,
      'serverConnected': _serverService.isConnected,
      'request': logRequest,
    });
  }

  Map<String, dynamic> _buildLoginRequest() {
    return {
      'action': 'login_read',
      'content': {
        'username': _loginModel.username,
        'password': _loginModel.password,
        'token': _loginModel.token,
        // 使用身份验证模式，不包含isPro字段，让服务器返回真实的Pro状态
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
    };
  }

  void _handleLoginError(BuildContext context, dynamic error) {
    _loginTimer?.cancel();
    isLoading = false;
    notifyListeners();
    
    log.e(_logTag, '登录请求失败', error.toString());
    _showToast(context, '发送登录请求失败: ${error.toString()}', MessageType.error);
  }

  // 处理登录响应
  void _handleLoginResponse(Map<String, dynamic> response) {
    _loginTimer?.cancel();
    
    // 检查是否为真正的用户登录流程
    // 只有在isLoading为true时才表示用户主动登录，才应该更新Pro状态
    final isUserLogin = isLoading;
    
    if (!isUserLogin) {
      // 这是首页保存流程中的登录验证响应，不应该更新Pro状态
      log.i(_logTag, '忽略首页保存流程的login_read_response，不更新Pro状态');
      return;
    }
    
    isLoading = false;
    notifyListeners();
    
    final success = response['status'] == 'success';
    final message = response['message'] ?? '';
    final context = WidgetsBinding.instance.focusManager.primaryFocus?.context;
    
    log.i(_logTag, '处理用户登录响应: ${success ? '成功' : '失败'}, 消息: $message');
    
    if (success) {
      _handleLoginSuccess(response, context, message);
    } else {
      _handleLoginFailure(context, message);
    }
  }
  
  void _handleLoginSuccess(Map<String, dynamic> response, BuildContext? context, String message) {
    final data = response['data'];
    final userInfo = data?['userInfo'];
    final homeConfig = data?['homeConfig'];
    
    final userIsPro = userInfo?['isPro'] == true;
    final token = userInfo?['token'] ?? '';
    // final lastLoginTime = userInfo?['lastLoginTime'] ?? DateTime.now().toIso8601String();
    
    log.i(_logTag, '登录成功，处理服务器返回数据', {
      'serverIsPro': userIsPro,
      'currentIsPro': _loginModel.isPro,
      'token': token.isNotEmpty ? '已获取' : '未获取',
      'hasHomeConfig': homeConfig != null,
    });
    
         // 保存用户认证信息
     _saveAuthInfo(token, userIsPro);
     _updateLoginStatus(userIsPro);
     
     // 处理首页配置数据
     if (homeConfig != null) {
       _processHomeConfig(homeConfig);
     }
    
    // 强制同步Pro状态，确保与服务器一致
    _loginModel.forceUpdateProStatus(userIsPro, reason: '登录成功同步');
    
    if (context != null) {
      _showSuccessAndNavigate(context, message, userIsPro);
    }
  }
  
  void _saveAuthInfo(String token, bool userIsPro) {
    _authModel.setAuthInfo(
      username: _loginModel.username,
      token: token,
      rememberPassword: true,
    );
    
    _loginModel.saveLoginInfo(
      username: _loginModel.username,
      password: _loginModel.password,
      token: token,
      rememberPassword: true,
      loginStatus: 'true',
      lastLoginTime: DateTime.now().toIso8601String(),
      isPro: userIsPro,
    );
  }
  
  void _updateLoginStatus(bool userIsPro) {
    _loginModel.updateProStatus(userIsPro);
    notifyListeners();
  }
  
  // 处理首页配置数据
  void _processHomeConfig(Map<String, dynamic> homeConfig) {
    try {
      final gameName = homeConfig['gameName']?.toString();
      final cardKey = homeConfig['cardKey']?.toString();
      
      if (gameName?.isNotEmpty == true) {
        _gameModel.updateCurrentGame(gameName!);
        log.i(_logTag, '更新游戏名称', gameName);
      }
      
      if (cardKey?.isNotEmpty == true) {
        _gameModel.updateCardKey(cardKey!);
        log.i(_logTag, '更新卡密信息', '***已获取***');
      }
    } catch (e) {
      log.e(_logTag, '处理首页配置数据失败', e.toString());
    }
  }
  
  void _showSuccessAndNavigate(BuildContext context, String message, bool userIsPro) {
    final successMsg = _buildSuccessMessage(message, userIsPro);
    _showToast(context, successMsg, MessageType.success);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        // 确保连接状态在页面跳转后仍然有效
        _ensureConnectionAfterLogin(context);

        _loginModel.updateRefreshAfterLogin(true);
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      }
    });
  }

  /// 确保登录后连接状态有效
  void _ensureConnectionAfterLogin(BuildContext context) {
    log.i(_logTag, '检查登录后连接状态', {
      'isConnected': _serverService.isConnected,
      'serverAddress': _serverService.serverAddress,
      'serverPort': _serverService.serverPort,
    });

    // 如果连接已断开，尝试重新连接
    if (!_serverService.isConnected &&
        _serverService.serverAddress.isNotEmpty &&
        _serverService.serverPort.isNotEmpty) {
      log.w(_logTag, '登录后发现连接已断开，尝试重新连接');

      // 异步重新连接，避免阻塞页面跳转
      Future.delayed(const Duration(milliseconds: 500), () async {
        await _serverService.handleConnectService(
          context,
          _serverService.serverAddress,
          _serverService.serverPort,
          _loginModel.token,
        );

        log.i(_logTag, '重新连接完成', {
          'isConnected': _serverService.isConnected,
        });
      });
    }
  }
  
  String _buildSuccessMessage(String message, bool userIsPro) {
    final baseMessage = message.isNotEmpty ? message : '登录成功';
    return userIsPro ? '$baseMessage (Pro版)' : baseMessage;
  }
  
  void _handleLoginFailure(BuildContext? context, String message) {
    _loginModel.updateLoginStatus('false');
    notifyListeners();
    
    if (context != null) {
      final errorMessage = _getDetailedErrorMessage(message);
      _showToast(context, errorMessage, MessageType.error, duration: const Duration(seconds: 3));
      
      // 记录详细的错误日志
      log.w(_logTag, '登录失败详情', {
        'errorMessage': message,
        'displayMessage': errorMessage,
        'username': _loginModel.username,
      });
    }
  }
  
  // 获取详细的错误消息
  String _getDetailedErrorMessage(String serverMessage) {
    if (serverMessage.isEmpty) {
      return '登录失败，请稍后重试';
    }
    
    // 根据最新API规范提供更友好的错误提示
    switch (serverMessage) {
      case '用户名错误':
        return '用户名不存在，请检查用户名或注册新账号';
      case '密码错误':
        return '密码错误，请检查密码后重试';
      case '用户名或密码错误':
        // 兼容旧版本API
        return '用户名或密码错误，请检查后重试。如果您还没有账号，请先注册。';
      case '令牌无效或已过期':
        return '登录凭证已过期，请重新输入密码登录';
      case '登录失败，请稍后重试':
        return '服务器繁忙，请稍后重试';
      default:
        return serverMessage.isNotEmpty ? serverMessage : '登录失败，请检查网络连接';
    }
  }
  
  // 处理首页配置响应
  void _handleHomeConfigResponse(Map<String, dynamic> response) {
    if (_isDisposed || response['status'] != 'ok') return;
    
    try {
      final data = response['data'];
      if (data == null) return;
      
      if (data['gameName']?.toString().isNotEmpty == true) {
        _gameModel.updateCurrentGame(data['gameName'].toString());
      }
      
      if (data['cardKey']?.toString().isNotEmpty == true) {
        _gameModel.updateCardKey(data['cardKey'].toString());
      }
    } catch (e) {
      log.e(_logTag, '处理首页配置失败', e.toString());
    }
  }
  
  // 处理注册按钮点击
  void handleRegister(BuildContext context) {
    log.i(_logTag, '跳转到注册页面');
    Navigator.pushNamed(context, '/register');
  }
  
  // 处理连接服务按钮点击
  Future<void> handleConnectService(BuildContext context) async {
    print('🔗 LoginController调用连接服务 (ServerService实例: ${_serverService.hashCode})'); // 强制输出
    await _serverService.handleConnectService(
      context,
      serverAddressController.text.trim(),
      portController.text.trim(),
      _loginModel.token,
    );
    print('🔗 连接完成，状态: isConnected=${_serverService.isConnected}'); // 强制输出
  }
  
  // 获取服务器配置输入字段数据（水平排列）
  List<Map<String, dynamic>> getServerInputFieldsData() {
    return [
      {
        'label': '服务器地址',
        'hint': '请输入服务器IP地址',
        'icon': Icons.cloud_outlined,
        'onChanged': updateServerAddress,
        'flex': 3, // 服务器地址占更多空间
      },
      {
        'label': '端口',
        'hint': '端口号',
        'icon': Icons.compare_arrows,
        'keyboardType': TextInputType.number,
        'onChanged': updatePort,
        'flex': 2, // 端口占较少空间
      },
    ];
  }

  // 获取用户认证输入字段数据（垂直排列）
  List<Map<String, dynamic>> getUserInputFieldsData() {
    return [
      {
        'label': '用户名',
        'hint': '请输入用户名',
        'icon': Icons.person,
        'onChanged': updateUsername,
      },
      {
        'label': '密码',
        'hint': '请输入密码',
        'icon': Icons.lock,
        'isPassword': true,
        'onChanged': updatePassword,
      },
    ];
  }

  // 保持向后兼容的方法
  List<Map<String, dynamic>> getInputFieldsData() {
    return [
      ...getServerInputFieldsData(),
      ...getUserInputFieldsData(),
    ];
  }
  
  // 释放资源
  @override
  void dispose() {
    _isDisposed = true;
    
    // 移除监听器
    if (_serverStatusListener != null) {
      _serverService.removeListener(_serverStatusListener!);
      _serverStatusListener = null;
    }
    
    // 取消计时器
    _loginTimer?.cancel();
    
    // 移除WebSocket消息监听
    _serverService.removeMessageListener(_handleServerMessage);
    
    // 释放控制器
    for (var controller in controllers) {
      controller.dispose();
    }
    
    log.i(_logTag, '登录控制器资源已释放');
    super.dispose();
  }
} 