// ignore_for_file: use_super_parameters, library_private_types_in_public_api

import 'package:flutter/material.dart';
import '../controllers/fire_controller.dart';
import 'package:provider/provider.dart';
import '../component/Button_component.dart';
import '../component/Slider_component.dart';

/// 射击设置屏幕 - 负责射击参数调整的UI
class FireScreen extends StatelessWidget {
  const FireScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 使用Consumer获取FireController并监听其变化
    return Consumer<FireController>(
      builder: (context, fireController, _) {
        return Scaffold(
          body: Padding(
            padding: const EdgeInsets.all(12), // 减小整体内边距
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部标题栏和按钮
                _buildHeader(fireController, context),
                
                const SizedBox(height: 10), // 减小顶部与说明卡片的间距
                
                // 页面内容描述
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 4.0),
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Text(
                    '调整游戏中的射击参数。不同的参数将影响射击时的速度、间隔和响应时间，找到最适合你的设置。',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
                
                const SizedBox(height: 8), // 减小说明卡片与参数区域的间距
                
                // 射击参数调整区域
                Expanded(
                  child: SingleChildScrollView(
                    child: _buildAllFireSliders(fireController, context),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    );
  }
  
  /// 构建顶部标题和按钮
  Widget _buildHeader(FireController controller, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 页面标题
        const Flexible(
          child: Text(
            '射击设置',
            style: TextStyle(
              fontSize: 22, // 稍微减小字体大小
              fontWeight: FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        
        // 按钮组
        Row(
          children: [
            // 刷新按钮
            Tooltip(
              message: '刷新参数',
              child: Container(
                height: 32, // 与ButtonSize.small的高度一致
                width: 32,  // 保持圆形比例
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF4CAF50), // 浅绿色
                      Color(0xFF2E7D32), // 深绿色
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2E7D32).withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: () => controller.requestFireParams(),
                    child: Container(
                      alignment: Alignment.center,
                      child: const Icon(
                        Icons.refresh,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(width: 10), // 减小按钮间距

            // 保存按钮
            Tooltip(
              message: '保存当前设置',
              child: Container(
                height: 32,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(6),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(6),
                    onTap: () => controller.saveFireConfig(context),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.save,
                          color: Colors.white,
                          size: 18,
                        ),
                        const SizedBox(width: 6),
                        const Text(
                          '保存',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建所有射击相关的滑块
  Widget _buildAllFireSliders(FireController controller, BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 基于屏幕宽度确定布局方式
        final bool isWideScreen = constraints.maxWidth > 768;
        
        // 步枪参数组
        final Widget rifleParamsCard = createSlider(
          cardTitle: '步枪射击参数',
          configs: [
            SliderConfig(
              title: '步枪休眠时间',
              value: controller.rifleSleep.toDouble(),
              min: 0.0,
              max: 200.0,
              step: 1.0,
              onChanged: (value) => controller.rifleSleep = value.toInt(),
              onChangeEnd: (_) => controller.handleParameterChanged('rifleSleep', controller.rifleSleep, context),
              decimalPlaces: 0,
              suffix: 'ms',
            ),
            SliderConfig(
              title: '步枪间隔时间',
              value: controller.rifleInterval.toDouble(),
              min: 0.0,
              max: 200.0,
              step: 1.0,
              onChanged: (value) => controller.rifleInterval = value.toInt(),
              onChangeEnd: (_) => controller.handleParameterChanged('rifleInterval', controller.rifleInterval, context),
              decimalPlaces: 0,
              suffix: 'ms',
            ),
          ],
        );
        
        // 其他武器参数组
        final Widget otherGunsParamsCard = createSlider(
          cardTitle: '其他武器参数',
          configs: [
            SliderConfig(
              title: '狙击休眠时间',
              value: controller.sniperSleep.toDouble(),
              min: 10.0,
              max: 500.0,
              step: 1.0,
              onChanged: (value) => controller.sniperSleep = value.toInt(),
              onChangeEnd: (_) => controller.handleParameterChanged('sniperSleep', controller.sniperSleep, context),
              decimalPlaces: 0,
              suffix: 'ms',
            ),
            SliderConfig(
              title: '狙击间隔时间',
              value: controller.sniperInterval.toDouble(),
              min: 50.0,
              max: 1000.0,
              step: 1.0,
              onChanged: (value) => controller.sniperInterval = value.toInt(),
              onChangeEnd: (_) => controller.handleParameterChanged('sniperInterval', controller.sniperInterval, context),
              decimalPlaces: 0,
              suffix: 'ms',
            ),
            SliderConfig(
              title: '切枪睡眠间隔',
              value: controller.weaponSwitchSleep.toDouble(),
              min: 10.0,
              max: 200.0,
              step: 1.0,
              onChanged: (value) => controller.weaponSwitchSleep = value.toInt(),
              onChangeEnd: (_) => controller.handleParameterChanged('weaponSwitchSleep', controller.weaponSwitchSleep, context),
              decimalPlaces: 0,
              suffix: 'ms',
            ),
          ],
        );
        
        // 根据屏幕宽度选择布局方式
        return Column(
          children: [
            // 卡片布局
            if (isWideScreen)
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: rifleParamsCard),
                  const SizedBox(width: 8), // 减小卡片之间的横向间距
                  Expanded(child: otherGunsParamsCard),
                ],
              )
            else
              Column(
                children: [
                  rifleParamsCard,
                  const SizedBox(height: 8), // 减小卡片之间的纵向间距
                  otherGunsParamsCard,
                ],
              ),
            
            const SizedBox(height: 10), // 减小卡片与底部按钮的间距
            
            // 重置按钮
            Center(
              child: Button.create(ButtonConfig.textWithIcon(
                '恢复默认值',
                Icons.restore,
                onPressed: () => controller.resetToDefaults(context),
                type: ButtonType.secondary,
                size: ButtonSize.small,
              )),
            ),
          ],
        );
      }
    );
  }
} 