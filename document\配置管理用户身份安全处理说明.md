# 配置管理用户身份安全处理说明

## 📋 安全需求

为了保护用户隐私和账户安全，配置管理系统需要确保用户名（username）和卡密（cardKey）等敏感身份信息不会在配置导入导出过程中泄露或被误用。

## 🔒 安全原则

### 1. 身份信息唯一性
- **用户名（username）**：每个用户的唯一标识，不应被其他用户导入
- **卡密（cardKey）**：用户的认证凭据，具有高度敏感性，绝不能共享

### 2. 配置分离原则
- **可共享配置**：游戏设置、功能参数、PID参数等技术配置
- **不可共享信息**：用户身份标识、认证凭据等个人信息

### 3. 安全边界
- **导出时**：自动过滤敏感信息，确保导出的配置不包含身份数据
- **导入时**：忽略导入数据中的身份信息，使用当前用户的身份

## 🔧 技术实现

### 1. 导出时的安全过滤

#### 过滤敏感信息方法
```dart
/// 过滤敏感的home配置数据
Map<String, dynamic> _filterSensitiveHomeData(Map<String, dynamic> homeData) {
  // 创建副本以避免修改原始数据
  final filteredData = Map<String, dynamic>.from(homeData);
  
  // 移除敏感信息：用户名和卡密
  filteredData.remove('username');
  filteredData.remove('cardKey');
  
  _logger.i(_tag, '已过滤敏感信息：username, cardKey');
  return filteredData;
}
```

#### 配置收集时的应用
```dart
if (configType == 'all' || configType == 'home') {
  final homeModel = Provider.of<HomeModel>(context, listen: false);
  homeModel.initialize(context);
  
  // 获取完整的home配置，但排除敏感信息
  final homeData = homeModel.toJson();
  final filteredHomeData = _filterSensitiveHomeData(homeData);
  result['home'] = filteredHomeData;
}
```

### 2. 导入时的安全处理

#### 忽略敏感信息导入
```dart
// 1. 先更新本地GameModel，立即触发UI更新
if (content['gameName'] != null) {
  final newGame = content['gameName'].toString();
  await _gameModel.updateCurrentGame(newGame);
}

// 注意：不导入cardKey，保持当前用户的卡密不变
_logger.i(_tag, '跳过卡密导入，保持当前用户卡密: ${_gameModel.cardKey}');

if (content['heartbeatInterval'] != null) {
  final newInterval = _parseIntValue(content['heartbeatInterval']);
  await _gameModel.updateHeartbeatInterval(newInterval);
}
```

#### 使用当前用户身份
```dart
// 构建首页修改请求发送到服务器，更新数据库
// 注意：始终使用当前用户的身份信息，不使用导入数据中的username和cardKey
final request = {
  'action': 'home_modify',
  'content': {
    'username': _authModel.username, // 使用当前用户名
    'gameName': content['gameName'] ?? _gameModel.currentGame,
    'cardKey': _gameModel.cardKey, // 使用当前用户的卡密
    'heartbeatInterval': content['heartbeatInterval'] ?? _gameModel.heartbeatInterval,
    'updatedAt': DateTime.now().toIso8601String(),
  }
};
```

## 🛡️ 安全保障

### 1. 导出安全
- ✅ **自动过滤**：导出时自动移除username和cardKey字段
- ✅ **数据完整性**：保留所有技术配置参数
- ✅ **日志记录**：记录过滤操作，便于审计

### 2. 导入安全
- ✅ **身份隔离**：导入时忽略外来的身份信息
- ✅ **当前用户优先**：始终使用当前登录用户的身份
- ✅ **配置应用**：正确应用技术配置参数

### 3. 传输安全
- ✅ **无敏感数据**：配置文件和剪切板数据不包含敏感信息
- ✅ **安全分享**：用户可以安全地分享配置给其他人
- ✅ **隐私保护**：接收方无法获取原用户的身份信息

## 📱 用户体验

### 1. 导出体验
**用户操作**：点击导出配置
**系统行为**：
1. 收集当前所有配置参数
2. 自动过滤敏感的身份信息
3. 生成安全的配置文件/剪切板数据
4. 提示用户导出成功

**用户感知**：无感知的安全处理，正常的导出体验

### 2. 导入体验
**用户操作**：导入他人分享的配置
**系统行为**：
1. 解析导入的配置数据
2. 忽略其中的身份信息（如果有）
3. 应用技术配置参数
4. 使用当前用户的身份信息
5. 更新本地配置和服务器数据

**用户感知**：配置正确应用，身份信息保持不变

### 3. 分享场景
**场景A - 用户A分享配置给用户B**：
1. 用户A导出配置（不包含A的身份信息）
2. 用户B导入配置（使用B的身份信息）
3. 用户B获得A的技术配置，但保持自己的身份

**场景B - 团队配置标准化**：
1. 管理员制作标准配置模板
2. 团队成员导入标准配置
3. 每个成员保持自己的身份，但使用统一的技术参数

## 🔍 安全验证

### 1. 导出验证
**验证点**：
- 导出的JSON数据不包含`username`字段
- 导出的JSON数据不包含`cardKey`字段
- 导出的数据包含所有其他配置参数

**测试方法**：
```json
// 导出的配置应该是这样的
{
  "home": {
    "action": "home_modify",
    "content": {
      // "username": "...", // 应该被过滤掉
      // "cardKey": "...",  // 应该被过滤掉
      "gameName": "csgo2",
      "heartbeatInterval": 30,
      "createdAt": "2025-01-01T00:00:00.000Z",
      "updatedAt": "2025-01-01T00:00:00.000Z"
    }
  },
  "function": { ... },
  "pid": { ... }
  // ... 其他配置
}
```

### 2. 导入验证
**验证点**：
- 导入后用户名保持为当前登录用户
- 导入后卡密保持为当前用户的卡密
- 技术配置参数正确应用

**测试方法**：
1. 记录导入前的用户身份信息
2. 导入包含其他用户身份的配置
3. 验证导入后身份信息未改变
4. 验证技术配置已正确应用

## 📊 安全日志

### 1. 导出日志
```
[ConfigManagementController] 已过滤敏感信息：username, cardKey
[ConfigManagementController] 配置导出到剪切板成功
```

### 2. 导入日志
```
[ConfigManagementController] 提取的首页配置内容 {gameName: csgo2, heartbeatInterval: 30, content_keys: [gameName, heartbeatInterval, createdAt, updatedAt], note: 已过滤敏感信息(username, cardKey)}
[ConfigManagementController] 跳过卡密导入，保持当前用户卡密: ****
[ConfigManagementController] 更新本地游戏选择: csgo2 -> valorant
```

## ✅ 安全效果

### 1. 隐私保护
- **用户身份隔离**：每个用户的身份信息完全独立
- **配置安全分享**：可以安全地分享技术配置
- **无意外泄露**：系统级别防止身份信息泄露

### 2. 系统安全
- **认证完整性**：用户认证体系不受配置导入影响
- **权限边界**：配置导入不能越权访问其他用户资源
- **数据一致性**：身份信息与配置数据的一致性

### 3. 用户信任
- **透明处理**：用户了解系统如何处理敏感信息
- **安全保障**：用户可以放心分享和使用配置
- **隐私控制**：用户对自己的身份信息有完全控制权

---

> **安全总结**: 通过导出时过滤敏感信息和导入时使用当前用户身份，确保配置管理的安全性和隐私保护
> **最后更新**: 2025年7月18日 | **维护者**: Flutter开发团队
