// ignore_for_file: use_super_parameters, library_private_types_in_public_api, deprecated_member_use, avoid_print

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:blui/views/side_screen.dart';
import 'package:blui/views/header_screen.dart';
import 'package:blui/models/sidebar_model.dart';
import 'package:blui/controllers/side_controller.dart';
import 'package:blui/utils/logger.dart';
import 'package:blui/models/login_model.dart';

/// 主布局组件 - 负责应用整体布局管理
class MainLayout extends StatefulWidget {
  // 布局常量
  static const String appVersion = "1.0.0";
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 768.0;
  static const double desktopBreakpoint = 1024.0;
  static const EdgeInsets contentPadding = EdgeInsets.all(16.0);
  static const EdgeInsets mobilePadding = EdgeInsets.all(8.0);
  
  // 侧边栏宽度配置
  static const double desktopSidebarWidth = 200.0;
  static const double mobileSidebarWidth = 60.0;
  
  // 页面映射
  static const Map<String, int> pageIndices = {
    'login': 0,
    'register': 1,
    'button': 2,
    'message': 3,
    'data': 4,
    'component': 5,
    'side': 6,
  };

  final Widget child;
  final String currentPageId;
  final VoidCallback onLogout;
  final VoidCallback onRefreshSystem;
  final VoidCallback onRefreshData;
  
  const MainLayout({
    Key? key,
    required this.child,
    required this.currentPageId,
    required this.onLogout,
    required this.onRefreshSystem,
    required this.onRefreshData,
  }) : super(key: key);
  
  @override
  _MainLayoutState createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  static const String _logTag = 'MainLayout';
  
  final Logger _logger = Logger();
  int _currentPageIndex = 0;
  
  @override
  void initState() {
    super.initState();
    _initializeLayout();
  }
  
  @override
  void didUpdateWidget(MainLayout oldWidget) {
    super.didUpdateWidget(oldWidget);
    _handlePageChange(oldWidget.currentPageId, widget.currentPageId);
  }

  /// 初始化布局
  void _initializeLayout() {
    _currentPageIndex = MainLayout.pageIndices[widget.currentPageId] ?? 0;
    _logger.i(_logTag, 'MainLayout已初始化，当前页面: ${widget.currentPageId}');
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performPostInitialization();
    });
  }

  /// 执行初始化后操作
  void _performPostInitialization() {
    if (!mounted) return;
    
    try {
      final loginModel = Provider.of<LoginModel>(context, listen: false);
      if (loginModel.refreshAfterLogin) {
        _logger.i(_logTag, '检测到登录后刷新标记');
        loginModel.updateRefreshAfterLogin(false);
        _executePostLoginRefresh();
      } else {
        _refreshCurrentPageData();
      }
    } catch (e) {
      _logger.e(_logTag, '初始化后操作失败', e.toString());
    }
  }

  /// 执行登录后刷新
  void _executePostLoginRefresh() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      
      try {
        _logger.i(_logTag, '执行登录后数据刷新');
        final sideController = Provider.of<SideController>(context, listen: false);
        sideController.refreshData(context);
      } catch (e) {
        _logger.e(_logTag, '登录后刷新失败', e.toString());
      }
    });
  }

  /// 刷新当前页面数据
  void _refreshCurrentPageData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      
      try {
        final sideController = Provider.of<SideController>(context, listen: false);
        sideController.refreshPageData(widget.currentPageId, context);
        _logger.i(_logTag, '已刷新当前页面: ${widget.currentPageId}');
      } catch (e) {
        _logger.e(_logTag, '刷新当前页面失败', e.toString());
      }
    });
  }

  /// 处理页面变化
  void _handlePageChange(String oldPageId, String newPageId) {
    if (oldPageId == newPageId) return;
    
    _logger.i(_logTag, '页面变化: $oldPageId -> $newPageId');
    
    final newIndex = MainLayout.pageIndices[newPageId] ?? 0;
    if (_currentPageIndex != newIndex) {
      setState(() => _currentPageIndex = newIndex);
    }
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) _refreshCurrentPageData();
    });
  }

  /// 处理菜单选择
  void _handleMenuSelection(String menuId) {
    _logger.i(_logTag, '菜单选择: $menuId');
    
    // 获取路由映射
    final routeMap = {
      'home': '/',
      'function': '/function',
      'pid': '/pid',
      'fov': '/fov',
      'aim': '/aim',
      'fire': '/fire',
      'data_collection': '/data_collection',
      'config_management': '/config_management',
    };
    
    final route = routeMap[menuId];
    if (route == null) {
      _logger.w(_logTag, '未找到菜单ID对应的路由: $menuId');
      return;
    }
    
    // 如果当前已经在目标页面，只刷新数据
    if (widget.currentPageId == menuId) {
      _logger.i(_logTag, '当前已在目标页面，仅刷新数据: $menuId');
      _refreshPageData(menuId);
      return;
    }
    
    // 执行页面导航
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      
      try {
        // 使用pushReplacementNamed进行页面替换
        Navigator.of(context).pushReplacementNamed(route).then((_) {
          _logger.i(_logTag, '页面导航成功: $menuId -> $route');
          
          // 导航完成后刷新数据
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _refreshPageData(menuId);
            }
          });
        }).catchError((error) {
          _logger.e(_logTag, '页面导航失败', error.toString());
        });
      } catch (e) {
        _logger.e(_logTag, '菜单选择处理失败', e.toString());
      }
    });
  }
  
  /// 刷新页面数据
  void _refreshPageData(String menuId) {
    try {
      final sideController = Provider.of<SideController>(context, listen: false);
      sideController.requestPageData(menuId, context);
      
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          sideController.refreshPageData(menuId, context);
        }
      });
    } catch (e) {
      _logger.e(_logTag, '刷新页面数据失败', e.toString());
    }
  }

  /// 处理数据刷新
  void _handleDataRefresh() {
    _logger.i(_logTag, '执行数据刷新');
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      
      try {
        final sideController = Provider.of<SideController>(context, listen: false);
        sideController.refreshData(context);
        widget.onRefreshData();
        _logger.i(_logTag, '数据刷新完成');
      } catch (e) {
        _logger.e(_logTag, '数据刷新失败', e.toString());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SidebarModel>(
      builder: (context, sidebarModel, _) {
        final layoutConfig = _calculateLayoutConfig(context);
        
        return Scaffold(
          backgroundColor: Colors.grey[100],
          body: SafeArea(
            child: Container(
              margin: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10.0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12.0),
                child: Column(
                  children: [
                    _buildHeader(layoutConfig),
                    Expanded(
                      child: _buildMainContent(layoutConfig, sidebarModel),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 计算布局配置
  LayoutConfig _calculateLayoutConfig(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    
    // 通知侧边栏模型屏幕尺寸变化，自动调整展开/收起状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        try {
          final sidebarModel = Provider.of<SidebarModel>(context, listen: false);
          sidebarModel.adjustForScreenWidth(screenWidth);
        } catch (e) {
          _logger.e(_logTag, '调整侧边栏状态失败', e.toString());
        }
      }
    });
    
    return LayoutConfig(
      isMobile: screenWidth < MainLayout.mobileBreakpoint,
      isTablet: screenWidth >= MainLayout.mobileBreakpoint && screenWidth < MainLayout.desktopBreakpoint,
      isDesktop: screenWidth >= MainLayout.desktopBreakpoint,
      contentPadding: screenWidth < MainLayout.mobileBreakpoint 
          ? MainLayout.mobilePadding 
          : MainLayout.contentPadding,
      statusBarHeight: statusBarHeight,
      sidebarWidth: screenWidth < MainLayout.mobileBreakpoint 
          ? MainLayout.mobileSidebarWidth 
          : MainLayout.desktopSidebarWidth,
    );
  }

  /// 构建页头
  Widget _buildHeader(LayoutConfig config) {
    return SizedBox(
      height: HeaderConstants.totalHeight, // 使用HeaderConstants.totalHeight = 44px状态行 + 64px主要控制行
      child: Container(
        margin: const EdgeInsets.only(bottom: 2.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12.0),
            topRight: Radius.circular(12.0),
          ),
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.withValues(alpha: 0.15),
              width: 1.0,
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 6.0,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: HeaderScreen(
          onLogout: widget.onLogout,
          onRefreshSystem: widget.onRefreshSystem,
          onRefreshData: _handleDataRefresh, // 传递数据刷新回调
        ),
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent(LayoutConfig config, SidebarModel sidebarModel) {
    return Container(
      margin: const EdgeInsets.only(top: 2.0),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.03),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12.0),
          bottomRight: Radius.circular(12.0),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 永久显示的侧边栏
          _buildSidebar(config),
          
          // 分隔线
          _buildDivider(sidebarModel),
          
          // 主内容区域
          _buildExpandedContent(config),
        ],
      ),
    );
  }

  /// 构建侧边栏
  Widget _buildSidebar(LayoutConfig config) {
    return Consumer<SidebarModel>(
      builder: (context, sidebarModel, child) {
        // 使用SidebarModel的实际宽度，而不是固定配置
        final actualWidth = sidebarModel.currentWidth;
        
        return Container(
          width: actualWidth,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12.0),
            ),
            border: Border(
              right: BorderSide(
                color: Colors.grey.withValues(alpha: 0.12),
                width: 1.0,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 2.0,
                offset: const Offset(1, 0),
              ),
            ],
          ),
          child: SidebarScreen(
            selectedMenuId: widget.currentPageId,
            onMenuSelected: _handleMenuSelection,
            onRefresh: _handleDataRefresh,
            version: MainLayout.appVersion,
            initialCollapsed: config.isMobile, // 移动端使用收起状态（仅图标）
          ),
        );
      },
    );
  }

  /// 构建分隔线
  Widget _buildDivider(SidebarModel sidebarModel) {
    return SizedBox(width: 0); // 移除分隔线，因为侧边栏已有边框
  }

  /// 构建扩展内容区域
  Widget _buildExpandedContent(LayoutConfig config) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            bottomRight: Radius.circular(12.0),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.02),
              blurRadius: 2.0,
              offset: const Offset(-1, 0),
            ),
          ],
        ),
        margin: const EdgeInsets.all(6.0),
        child: Container(
          padding: config.contentPadding,
          child: widget.child,
        ),
      ),
    );
  }
}

/// 布局配置类
class LayoutConfig {
  final bool isMobile;
  final bool isTablet;
  final bool isDesktop;
  final EdgeInsets contentPadding;
  final double statusBarHeight;
  final double sidebarWidth;

  const LayoutConfig({
    required this.isMobile,
    required this.isTablet,
    required this.isDesktop,
    required this.contentPadding,
    required this.statusBarHeight,
    required this.sidebarWidth,
  });
}
