// ignore_for_file: library_private_types_in_public_api, use_super_parameters, deprecated_member_use, unused_field, unused_element

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/function_model.dart';
import '../../models/login_model.dart';
import '../../component/dropdown_component.dart';

/// 卡片数据模型
class CardData {
  String presetName;
  String hotkey;
  String aiMode;
  String lockPosition;
  String selectedFaction;
  bool isSelected;
  bool triggerSwitch;
  bool weaponSwitch;  // 新增：切枪开关
  bool flashShield;   // 新增：背闪开关
  bool enabled;

  CardData({
    required this.presetName,
    required this.hotkey,
    required this.aiMode,
    required this.lockPosition,
    this.selectedFaction = '无',
    this.isSelected = false,
    this.triggerSwitch = false,
    this.weaponSwitch = false,  // 新增：切枪开关默认关闭
    this.flashShield = false,   // 新增：背闪开关默认关闭
    this.enabled = true,
  });
  
  /// 将卡片数据转换为Map
  Map<String, dynamic> toJson() {
    return {
      'presetName': presetName,
      'hotkey': hotkey,
      'aiMode': aiMode,
      'lockPosition': lockPosition,
      'selectedFaction': selectedFaction,
      'isSelected': isSelected,
      'triggerSwitch': triggerSwitch,
      'weaponSwitch': weaponSwitch,  // 新增：切枪开关
      'flashShield': flashShield,    // 新增：背闪开关
      'enabled': enabled,
    };
  }
  
  /// 从配置项创建卡片数据
  factory CardData.fromConfig(Map<String, dynamic> config, FunctionModel functionModel) {
    return CardData(
      presetName: config['presetName'] ?? '新配置',
      hotkey: config['hotkey'] ?? functionModel.hotkeys.first,
      aiMode: config['aiMode'] ?? functionModel.aiModes.first,
      lockPosition: config['lockPosition'] ?? functionModel.lockPositions.first,
      selectedFaction: config['selectedFaction'] ?? '无',
      triggerSwitch: config['triggerSwitch'] ?? false,
      weaponSwitch: config['weaponSwitch'] ?? false,  // 新增：切枪开关
      flashShield: config['flashShield'] ?? false,    // 新增：背闪开关
      enabled: config['enabled'] ?? true,
    );
  }
  
  /// 转换为配置模型格式
  Map<String, dynamic> toConfigFormat() {
    return {
      'presetName': presetName,
      'aiMode': aiMode,
      'lockPosition': lockPosition,
      'hotkey': hotkey,
      'selectedFaction': selectedFaction,
      'triggerSwitch': triggerSwitch,
      'weaponSwitch': weaponSwitch,  // 新增：切枪开关
      'flashShield': flashShield,    // 新增：背闪开关
      'enabled': enabled,
    };
  }
}

// 回调类型定义
typedef OnCardAdded = Function(CardData card);
typedef OnCardUpdated = Function(CardData card);
typedef OnCardPropertyChanged = Function(CardData card, String property, String newValue);
typedef OnCardBoolPropertyChanged = Function(CardData card, String property, bool newValue);
typedef OnGetAllCardsInfo = Function(List<CardData> cards);

/// 卡片管理组件
class CardManagerComponent extends StatefulWidget {
  final List<CardData> cards;
  final String appBarTitle;
  final OnCardAdded? onCardAdded;
  final OnCardUpdated? onCardUpdated;
  final OnCardPropertyChanged? onCardPropertyChanged;
  final OnCardBoolPropertyChanged? onCardBoolPropertyChanged;
  final OnGetAllCardsInfo? onGetAllCardsInfo;
  final bool externalPropertyHandling;
  final bool enableScroll;
  final bool hideAppBar;

  const CardManagerComponent({
    Key? key,
    required this.cards,
    this.appBarTitle = '卡片管理',
    this.onCardAdded,
    this.onCardUpdated,
    this.onCardPropertyChanged,
    this.onCardBoolPropertyChanged,
    this.onGetAllCardsInfo,
    this.externalPropertyHandling = false,
    this.enableScroll = true,
    this.hideAppBar = false,
  }) : super(key: key);

  @override
  _CardManagerComponentState createState() => _CardManagerComponentState();
}

class _CardManagerComponentState extends State<CardManagerComponent> {
  static const double _spacing = 12.0;
  static const EdgeInsets _padding = EdgeInsets.all(16.0);
  static const EdgeInsets _bottomPadding = EdgeInsets.only(bottom: 80);
  
  bool get _isMobileDevice => MediaQuery.of(context).size.width < 600;
  
  @override
  Widget build(BuildContext context) {
    // 添加安全检查，确保在构建时有有效的上下文
    if (!mounted) {
      return const SizedBox.shrink();
    }
    
    return Column(
      children: [
        if (!widget.hideAppBar) _buildAppBar(),
        Expanded(
          child: widget.cards.isEmpty 
              ? _buildEmptyView()
              : _buildCardGrid(),
        ),
      ],
    );
  }

  /// 构建应用栏
  Widget _buildAppBar() {
    return AppBar(
      title: Text(widget.appBarTitle),
      centerTitle: true,
      elevation: 2.0,
      toolbarHeight: _isMobileDevice ? 56.0 : 64.0,
      actions: [
        IconButton(
          icon: const Icon(Icons.save),
          tooltip: '保存配置',
          onPressed: () => widget.onGetAllCardsInfo?.call(widget.cards),
        ),
      ],
    );
  }

  /// 构建卡片网格
  Widget _buildCardGrid() {
    final shouldEnableScroll = widget.enableScroll || _isMobileDevice;
    
    // 为移动端优化布局 - Android平台特别优化
    if (_isMobileDevice) {
      return LayoutBuilder(
        builder: (context, constraints) {
          // 确保约束有效
          if (constraints.maxHeight <= 0 || constraints.maxWidth <= 0) {
            return const SizedBox.shrink();
          }
          
          // Android平台：使用更稳定的ListView布局
          return ListView.builder(
            padding: const EdgeInsets.all(8.0),
            physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
            itemCount: widget.cards.length,
            itemBuilder: (context, index) {
              // 添加安全检查
              if (!mounted || index >= widget.cards.length) {
                return const SizedBox.shrink();
              }
              
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: SizedBox(
                  width: constraints.maxWidth - 16.0, // 减去padding
                  child: _buildCard(widget.cards[index]),
                ),
              );
            },
          );
        },
      );
    }
    
    if (shouldEnableScroll) {
      return ListView(
        padding: _padding,
        physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
        children: [
          _buildCardLayout(),
          const SizedBox(height: 80),
        ],
      );
    } else {
      return SingleChildScrollView(
        physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
        child: Padding(
          padding: _padding,
          child: Column(
            children: [
              _buildCardLayout(),
              const SizedBox(height: 80),
            ],
          ),
        ),
      );
    }
  }

  /// 构建卡片布局
  Widget _buildCardLayout() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final columns = _calculateColumns(constraints.maxWidth);
        final cardWidth = _calculateCardWidth(constraints.maxWidth, columns);
        
        return Wrap(
          spacing: _spacing,
          runSpacing: _spacing,
          alignment: WrapAlignment.start,
          children: widget.cards.map((card) => 
            SizedBox(
              width: cardWidth,
              child: _buildCard(card),
            )
          ).toList(),
        );
      },
    );
  }

  /// 根据可用宽度计算合适的列数
  int _calculateColumns(double availableWidth) {
    if (availableWidth < 450) return 1;
    if (availableWidth < 800) return 2;
    if (availableWidth < 1200) return 3;
    return 4;
  }
  
  /// 计算卡片宽度
  double _calculateCardWidth(double availableWidth, int columns) {
    final totalSpacing = _spacing * (columns - 1);
    return (availableWidth - totalSpacing) / columns;
  }

  /// 构建卡片主体内容
  Widget _buildCard(CardData card) {
    // 添加安全检查
    if (!mounted) {
      return const SizedBox.shrink();
    }
    
    try {
      final loginModel = Provider.of<LoginModel>(context, listen: false);
      final functionModel = Provider.of<FunctionModel>(context, listen: false);
      final isPro = loginModel.isPro;
      
      return Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: EdgeInsets.zero,
        color: Colors.white,
        child: Padding(
          padding: EdgeInsets.all(_isMobileDevice ? 8.0 : 12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                card.presetName,
                style: TextStyle(
                  fontSize: _isMobileDevice ? 14 : 16, 
                  fontWeight: FontWeight.bold
                ),
                overflow: TextOverflow.ellipsis,
              ),
              Divider(height: _isMobileDevice ? 16 : 24),
              
              // 启用状态开关
              _buildSwitchRow(
                '启用状态:', 
                card.enabled, 
                (value) => _updateBoolProperty(card, 'enabled', value),
                isEnabled: true,
              ),
              SizedBox(height: _isMobileDevice ? 6 : 8),
              
              // 自动扳机开关
              _buildSwitchRow(
                '自动扳机:', 
                card.triggerSwitch, 
                (value) => _updateBoolProperty(card, 'triggerSwitch', value, requiresPro: true),
                isEnabled: isPro,
              ),
              SizedBox(height: _isMobileDevice ? 6 : 8),
              
              // 切枪开关
              _buildSwitchRow(
                '切枪功能:', 
                card.weaponSwitch, 
                (value) => _updateBoolProperty(card, 'weaponSwitch', value),
                isEnabled: isPro,  // 只有Pro用户才能启用切枪功能
              ),
              SizedBox(height: _isMobileDevice ? 6 : 8),
              
              // 背闪开关
              _buildSwitchRow(
                '背闪防护:', 
                card.flashShield, 
                (value) => _updateBoolProperty(card, 'flashShield', value),
                isEnabled: isPro,  // 只有Pro用户才能启用背闪防护
              ),
              SizedBox(height: _isMobileDevice ? 6 : 8),
              
              // 热键设置
              _buildIconDropdownRow(
                '触发热键:', 
                card.hotkey, 
                functionModel.hotkeys,
                Icons.keyboard,
                (value) => _updateStringProperty(card, 'hotkey', value!),
              ),
              SizedBox(height: _isMobileDevice ? 6 : 8),
              
              // AI模式设置
              _buildAiModeDropdownRow(
                'AI模式:', 
                card.aiMode, 
                functionModel.aiModes,
                isPro,
                (value) => _updateStringProperty(card, 'aiMode', value!, requiresPro: value != 'PID'),
              ),
              SizedBox(height: _isMobileDevice ? 6 : 8),
              
              // 锁定位置设置
              _buildLockPositionDropdownRow(
                '锁定位置:',
                card.lockPosition,
                functionModel.lockPositions,
                (value) => _updateStringProperty(card, 'lockPosition', value!),
              ),
              SizedBox(height: _isMobileDevice ? 6 : 8),
              
              // 阵营选择设置
              _buildFactionDropdownRow(
                '阵营选择:', 
                card.selectedFaction, 
                functionModel.factions,
                (value) => _updateStringProperty(card, 'selectedFaction', value!),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      // 如果构建卡片时出错，返回错误提示卡片
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: EdgeInsets.zero,
        color: Colors.red.shade50,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              const Icon(Icons.error, color: Colors.red),
              const SizedBox(height: 8),
              Text(
                '卡片加载失败',
                style: TextStyle(color: Colors.red.shade700),
              ),
              Text(
                card.presetName,
                style: TextStyle(fontSize: 12, color: Colors.red.shade600),
              ),
            ],
          ),
        ),
      );
    }
  }

  /// 统一的字符串属性更新方法
  void _updateStringProperty(CardData card, String property, String newValue, {bool requiresPro = false}) {
    // 检查阵营选择的Pro权限
    if (property == 'selectedFaction' && newValue != '无' && !_isProUser) {
      _showProRequiredMessage('阵营选择功能仅限Pro用户使用');
      return;
    }
    
    if (requiresPro && !_isProUser) {
      _showProRequiredMessage(property == 'aiMode' ? '高级AI模式仅限Pro用户使用' : 'Pro功能仅限Pro用户使用');
      return;
    }

    if (widget.externalPropertyHandling) {
      widget.onCardPropertyChanged?.call(card, property, newValue);
    } else {
      setState(() {
        switch (property) {
          case 'hotkey': card.hotkey = newValue; break;
          case 'aiMode': card.aiMode = newValue; break;
          case 'lockPosition': card.lockPosition = newValue; break;
          case 'selectedFaction': card.selectedFaction = newValue; break;
        }
      });
      widget.onCardUpdated?.call(card);
    }
  }

  /// 统一的布尔属性更新方法
  void _updateBoolProperty(CardData card, String property, bool newValue, {bool requiresPro = false}) {
    // 检查自动扳机的Pro权限
    if (property == 'triggerSwitch' && newValue && !_isProUser) {
      _showProRequiredMessage('自动扳机功能仅限Pro用户使用');
      return;
    }
    
    // 检查切枪功能的Pro权限
    if (property == 'weaponSwitch' && newValue && !_isProUser) {
      _showProRequiredMessage('切枪功能仅限Pro用户使用');
      return;
    }
    
    // 检查背闪防护的Pro权限
    if (property == 'flashShield' && newValue && !_isProUser) {
      _showProRequiredMessage('背闪防护功能仅限Pro用户使用');
      return;
    }
    
    if (requiresPro && newValue && !_isProUser) {
      _showProRequiredMessage('自动扳机功能仅限Pro用户使用');
      return;
    }

    if (widget.externalPropertyHandling) {
      widget.onCardBoolPropertyChanged?.call(card, property, newValue);
    } else {
      setState(() {
        switch (property) {
          case 'enabled': card.enabled = newValue; break;
          case 'triggerSwitch': card.triggerSwitch = newValue; break;
          case 'weaponSwitch': card.weaponSwitch = newValue; break;  // 新增：切枪开关处理
          case 'flashShield': card.flashShield = newValue; break;    // 新增：背闪开关处理
        }
      });
      widget.onCardUpdated?.call(card);
    }
  }

  /// 显示Pro权限提示
  void _showProRequiredMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 获取当前用户是否为Pro用户
  bool get _isProUser {
    final loginModel = Provider.of<LoginModel>(context, listen: false);
    return loginModel.isPro;
  }

  /// 构建Pro标识组件
  Widget _buildProBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.orange.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.shade300),
      ),
      child: Text(
        'Pro',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: Colors.orange.shade700,
        ),
      ),
    );
  }

  /// 构建带标签的IconDropdown下拉行
  Widget _buildIconDropdownRow(String label, String value, List<String> items, IconData itemIcon, Function(String?) onChanged) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey),
            ),
            child: IconDropdown(
              value: value,
              items: items.map((item) => IconDropdownItem(
                value: item,
                text: item,
                icon: itemIcon,
              )).toList(),
              onChanged: onChanged,
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              margin: EdgeInsets.zero,
              borderRadius: BorderRadius.circular(4),
              border: BorderSide.none, // 移除内部边框，使用外部容器的边框
              dropdownButtonColor: Colors.white,
              textStyle: TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
              iconSize: 18,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建锁定位置下拉行（支持不同图标）
  Widget _buildLockPositionDropdownRow(String label, String value, List<String> items, Function(String?) onChanged) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey),
            ),
            child: IconDropdown(
              value: value,
              items: items.map((item) => IconDropdownItem(
                value: item,
                text: item,
                icon: _getLockPositionIcon(item),
              )).toList(),
              onChanged: onChanged,
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              margin: EdgeInsets.zero,
              borderRadius: BorderRadius.circular(4),
              border: BorderSide.none, // 移除内部边框，使用外部容器的边框
              dropdownButtonColor: Colors.white,
              textStyle: TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
              iconSize: 18,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建带开关的行
  Widget _buildSwitchRow(String label, bool value, Function(bool) onChanged, {required bool isEnabled}) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        ),
        const Spacer(),
        Container(
          decoration: BoxDecoration(
            color: isEnabled ? Colors.grey.shade100 : Colors.grey.shade200,
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: Row(
            children: [
              Text(
                value ? '已启用' : '已禁用',
                style: TextStyle(
                  color: isEnabled 
                      ? (value ? Colors.green : Colors.grey)
                      : Colors.grey.shade500,
                  fontWeight: value ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              const SizedBox(width: 8),
              _buildSwitch(value, onChanged, isEnabled),
              // Pro标识（仅在Pro功能且非Pro用户时显示）
              if ((label == '自动扳机:' || label == '切枪功能:' || label == '背闪防护:') && !isEnabled) ...[
                const SizedBox(width: 8),
                _buildProBadge(),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 构建开关组件
  Widget _buildSwitch(bool value, Function(bool) onChanged, bool isEnabled) {
    return Container(
      width: 50,
      height: 26,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: isEnabled 
            ? (value ? Colors.green : Colors.grey.shade300)
            : Colors.grey.shade400,
      ),
      child: AnimatedAlign(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        alignment: value ? Alignment.centerRight : Alignment.centerLeft,
        child: GestureDetector(
          onTap: isEnabled ? () => onChanged(!value) : null,
          child: Container(
            width: 22,
            height: 22,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isEnabled ? Colors.white : Colors.grey.shade300,
              boxShadow: isEnabled ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 2,
                  spreadRadius: 0.5,
                )
              ] : null,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建AI模式下拉行，为每个选项添加Pro标识
  Widget _buildAiModeDropdownRow(String label, String value, List<String> items, bool isPro, Function(String?) onChanged) {
    // 显示所有选项，但对非Pro用户限制选择
    final displayItems = items;
    
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        ),
        Expanded(
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: displayItems.contains(value) ? value : 'PID',
                isExpanded: true,
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                icon: const Icon(Icons.arrow_drop_down),
                onChanged: (newValue) {
                  // 检查Pro权限
                  if (newValue != null && newValue != 'PID' && !isPro) {
                    _showProRequiredMessage('高级AI模式仅限Pro用户使用');
                    return;
                  }
                  onChanged(newValue);
                },
                items: displayItems.map((item) => _buildAiModeDropdownItem(item, isPro)).toList(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建AI模式下拉选项
  DropdownMenuItem<String> _buildAiModeDropdownItem(String item, bool isPro) {
    // PID是基础功能，其他模式（FOV、FOVPID、所有冲锋狙模式）都是Pro功能
    final isProFeature = item != 'PID';
    
    return DropdownMenuItem<String>(
      value: item,
      enabled: isPro || !isProFeature, // 非Pro用户只能选择PID
      child: Row(
        children: [
          const Icon(Icons.smart_toy, size: 18),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              isProFeature && !isPro ? '$item (Pro)' : item,
              style: TextStyle(
                fontSize: 14,
                color: (isPro || !isProFeature) ? Colors.black : Colors.grey,
              ),
            ),
          ),
          if (isProFeature && !isPro) _buildProBadge(),
        ],
      ),
    );
  }

  /// 构建阵营选择下拉行
  Widget _buildFactionDropdownRow(String label, String value, List<String> items, Function(String?) onChanged) {
    final isPro = _isProUser;
    // 显示所有选项，但对非Pro用户限制选择
    final displayItems = items;
    
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey),
            ),
            child: IconDropdown(
              value: displayItems.contains(value) ? value : '无',
              items: displayItems.map((item) => _buildFactionDropdownItem(item, isPro)).toList(),
              onChanged: (newValue) {
                // 检查Pro权限
                if (newValue != null && newValue != '无' && !isPro) {
                  _showProRequiredMessage('阵营选择功能仅限Pro用户使用');
                  return;
                }
                onChanged(newValue);
              },
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              margin: EdgeInsets.zero,
              borderRadius: BorderRadius.circular(4),
              border: BorderSide.none, // 移除内部边框，使用外部容器的边框
              dropdownButtonColor: Colors.white,
              textStyle: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
              iconSize: 18,
            ),
          ),
        ),
      ],
    );
  }
  
  /// 构建阵营选择下拉选项
  IconDropdownItem _buildFactionDropdownItem(String item, bool isPro) {
    final isProFeature = item != '无';
    
    return IconDropdownItem(
      value: item,
      text: isProFeature && !isPro ? '$item (Pro)' : item,
      icon: _getFactionIcon(item),
    );
  }

  /// 获取阵营对应的图标
  IconData _getFactionIcon(String faction) {
    switch (faction) {
      case '警方':
        return Icons.shield;
      case '匪方':
        return Icons.person_outline;
      case '无':
      default:
        return Icons.remove_circle_outline;
    }
  }

  /// 获取锁定部位对应的图标
  IconData _getLockPositionIcon(String position) {
    switch (position) {
      case '头部':
        return Icons.face;
      case '颈部':
        return Icons.accessibility_new;
      case '胸部':
        return Icons.favorite;
      case '随机部位':
        return Icons.shuffle;
      default:
        return Icons.gps_fixed;
    }
  }

  /// 构建空视图
  Widget _buildEmptyView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.dashboard_customize, size: 80, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            '暂无卡片',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.grey),
          ),
          SizedBox(height: 8),
          Text('请等待系统加载预设配置', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }
}
