# 连接/断开功能测试说明

## 测试目标
验证页头重连按钮的连接/断开切换功能是否正常工作。

## 测试步骤

### 1. 未连接状态测试
**初始状态**: 服务器未连接
- **预期图标**: 绿色连接图标 (Icons.link)
- **预期颜色**: 绿色 (ButtonType.success)
- **菜单文本**: "连接服务器"
- **操作**: 点击按钮
- **预期结果**: 显示"正在连接服务器..."，开始连接流程

### 2. 连接中状态测试
**状态**: 正在连接服务器
- **预期图标**: 蓝色同步图标 (Icons.sync)
- **预期颜色**: 蓝色 (ButtonType.primary)
- **菜单文本**: "连接中..."
- **按钮状态**: 禁用 (onPressed: null)
- **预期结果**: 按钮不可点击，显示连接进度

### 3. 已连接状态测试
**状态**: 服务器已连接
- **预期图标**: 橙色断开图标 (Icons.link_off)
- **预期颜色**: 橙色 (ButtonType.warning)
- **菜单文本**: "断开连接"
- **操作**: 点击按钮
- **预期结果**: 显示"正在断开服务器连接..."，执行断开操作

### 4. 断开操作测试
**操作**: 从已连接状态点击断开
- **预期流程**: 
  1. 显示"正在断开服务器连接..."
  2. 调用 serverService.disconnect()
  3. 1秒后显示"已断开服务器连接"
  4. 按钮状态切换回未连接状态

### 5. 连接操作测试
**操作**: 从未连接状态点击连接
- **预期流程**:
  1. 显示"正在连接服务器..."
  2. 调用 headerController.handleRefreshSystem()
  3. 2秒后检查连接结果
  4. 连接成功: 显示"服务器连接成功" + 自动数据刷新
  5. 连接失败: 显示"服务器连接失败，请检查网络"

## 多布局测试

### 桌面端
- 系统操作按钮组中的连接/断开按钮
- 完整的图标和颜色显示

### 平板端
- 响应式布局下的按钮显示
- 确保图标和颜色正确

### 手机端
- 极小屏幕布局中的按钮
- 紧凑菜单中的连接/断开选项

## 状态切换验证

### 完整切换流程
1. 未连接 (绿色连接图标) → 点击 → 连接中 (蓝色同步图标)
2. 连接中 (蓝色同步图标) → 自动 → 已连接 (橙色断开图标)
3. 已连接 (橙色断开图标) → 点击 → 未连接 (绿色连接图标)

### 实时更新验证
- 使用Consumer<ServerService>监听状态变化
- 状态改变时图标、颜色、文本应立即更新
- 按钮的可用性应根据连接状态正确设置

## 错误处理测试

### 连接失败场景
- 服务器地址错误
- 网络不可用
- 服务器未启动

### 断开异常场景
- 网络突然断开
- 服务器主动断开连接

## 预期用户体验

### 视觉反馈
- 用户能够一眼看出当前连接状态
- 不同状态下的图标和颜色有明确的语义
- 连接中状态明确显示正在处理

### 操作指引
- 未连接时提示可以连接
- 已连接时提示可以断开
- 连接中时禁用操作避免重复点击

### 消息提示
- 每个操作都有相应的Toast消息
- 操作结果有明确的成功/失败反馈
- 消息内容准确描述当前操作状态
