// ignore_for_file: prefer_final_fields

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

// 模型导入（需要用于类型声明，但部分在main.dart中手动注册）
import '../models/auth_model.dart';
import '../models/sidebar_model.dart';
import '../models/resource_model.dart';
import '../models/game_model.dart';
import '../models/login_model.dart';
import '../models/header_model.dart';
import '../models/function_model.dart';
import '../models/aim_model.dart';
import '../models/fov_model.dart';
import '../models/fire_model.dart';
import '../models/pid_model.dart';
import '../models/data_collection_model.dart';
import '../models/status_bar_model.dart';
import '../models/config_management_model.dart';
import '../models/home_model.dart';

// 控制器导入
import '../controllers/login_controller.dart';
import '../controllers/register_controller.dart';
import '../controllers/home_controller.dart';
import '../controllers/header_controller.dart';
import '../controllers/side_controller.dart';
import '../controllers/aim_controller.dart';
import '../controllers/fire_controller.dart';
import '../controllers/fov_controller.dart';
import '../controllers/pid_controller.dart';
import '../controllers/function_controller.dart';
import '../controllers/data_collection_controller.dart';
import '../controllers/config_management_controller.dart';

// 服务导入
import '../services/server_service.dart';
import '../services/auth_service.dart';

import 'logger.dart';
import 'app_constants.dart';

/// Provider管理器 - 统一Provider注册和管理
/// 
/// 这个类提供了统一的Provider注册和管理机制，简化了main.dart中的
/// Provider配置，并确保依赖注入的正确性和一致性。
class ProviderManager {
  static final Logger _logger = Logger();
  static const String _logTag = AppConstants.LOG_TAG_APP;

  /// ========== 基础Provider注册 ==========

  /// 获取所有基础ChangeNotifierProvider
  static List<dynamic> getBasicProviders() {
    _logger.i(_logTag, '注册基础Provider');
    
    return [
      // 核心模型（排除在main.dart中已手动注册的模型）
      // AuthModel, GameModel, LoginModel, UIConfigModel, AppSettingsModel 在main.dart中手动注册
      ChangeNotifierProvider(create: (_) => SidebarModel()),
      ChangeNotifierProvider(create: (_) => ResourceModel()),
      ChangeNotifierProvider(create: (_) => HeaderModel()),
      
      // 功能配置模型
      ChangeNotifierProvider(create: (_) => FunctionModel()),
      ChangeNotifierProvider(create: (_) => AimModel()),
      ChangeNotifierProvider(create: (_) => FovModel()),
      ChangeNotifierProvider(create: (_) => FireModel()),
      ChangeNotifierProvider(create: (_) => PidModel()),
      ChangeNotifierProvider(create: (_) => DataCollectionModel()),
      ChangeNotifierProvider(create: (_) => StatusBarModel()),
      ChangeNotifierProvider(create: (_) => ConfigManagementModel()),
      ChangeNotifierProvider(create: (_) => HomeModel()),
      
      // 服务
      ChangeNotifierProvider(create: (_) => ServerService()),
      ChangeNotifierProvider(create: (_) => AuthService()),
    ];
  }

  /// ========== 代理Provider注册 ==========

  /// 获取所有ChangeNotifierProxyProvider
  static List<dynamic> getProxyProviders() {
    _logger.i(_logTag, '注册代理Provider');

    return [
      // 登录控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider5<ServerService, AuthService, AuthModel, LoginModel, GameModel, LoginController?>(
        create: (_) => null, // 不在create中创建实例，避免使用独立的服务实例
        update: (_, serverService, authService, authModel, loginModel, gameModel, controller) {
          // 如果已有实例且依赖没有变化，重用现有实例
          if (controller != null) {
            return controller;
          }
          // 只有在没有实例时才创建新实例，使用Provider中的服务实例
          return LoginController(
            serverService: serverService, // 使用Provider中的ServerService实例
            authService: authService,     // 使用Provider中的AuthService实例
            authModel: authModel,
            loginModel: loginModel,
            gameModel: gameModel,
          );
        },
      ),

      // 注册控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider4<ServerService, LoginModel, GameModel, AuthModel, RegisterController?>(
        create: (_) => null,
        update: (_, serverService, loginModel, gameModel, authModel, controller) {
          if (controller != null) return controller;
          return RegisterController(
            serverService: serverService,
            loginModel: loginModel,
            gameModel: gameModel,
            authModel: authModel,
          );
        },
      ),

      // 首页控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider4<ServerService, AuthModel, GameModel, LoginModel, HomeController?>(
        create: (_) => null,
        update: (_, serverService, authModel, gameModel, loginModel, controller) {
          if (controller != null) return controller;
          return HomeController(
            serverService: serverService,
            authModel: authModel,
            gameModel: gameModel,
            loginModel: loginModel,
          );
        },
      ),

      // 页头控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider5<ServerService, AuthModel, GameModel, LoginModel, HeaderModel, HeaderController?>(
        create: (_) => null,
        update: (_, serverService, authModel, gameModel, loginModel, headerModel, controller) {
          if (controller != null) {
            // 如果复用之前的实例，只在必要时重新注册状态更新回调
            // 避免频繁的重新注册导致不必要的日志输出
            controller.reregisterStatusUpdateCallback();
            return controller;
          }
          return HeaderController(
            serverService: serverService,
            authModel: authModel,
            gameModel: gameModel,
            loginModel: loginModel,
            headerModel: headerModel,
          );
        },
      ),

      // 侧边栏控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider4<ServerService, SidebarModel, AuthModel, GameModel, SideController?>(
        create: (_) => null,
        update: (_, serverService, sidebarModel, authModel, gameModel, controller) {
          if (controller != null) return controller;
          return SideController(
            serverService: serverService,
            sidebarModel: sidebarModel,
            authModel: authModel,
            gameModel: gameModel,
          );
        },
      ),

      // 瞄准控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider4<ServerService, AuthModel, GameModel, AimModel, AimController?>(
        create: (_) => null, // 不在create中创建实例
        update: (_, serverService, authModel, gameModel, aimModel, controller) {
          // 如果已有实例，重用现有实例
          if (controller != null) {
            return controller;
          }
          // 只有在没有实例时才创建新实例
          final newController = AimController();
          newController.initialize(serverService, authModel, aimModel, gameModel);
          return newController;
        },
      ),

      // 开火控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider4<ServerService, AuthModel, GameModel, FireModel, FireController?>(
        create: (_) => null,
        update: (_, serverService, authModel, gameModel, fireModel, controller) {
          if (controller != null) return controller;
          return FireController(
            serverService: serverService,
            authModel: authModel,
            gameModel: gameModel,
            fireModel: fireModel,
          );
        },
      ),

      // 视野控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider4<ServerService, AuthModel, GameModel, FovModel, FovController?>(
        create: (_) => null,
        update: (_, serverService, authModel, gameModel, fovModel, controller) {
          if (controller != null) return controller;
          return FovController(
            serverService: serverService,
            authModel: authModel,
            gameModel: gameModel,
            fovModel: fovModel,
          );
        },
      ),

      // PID控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider4<ServerService, AuthModel, GameModel, PidModel, PidController?>(
        create: (_) => null,
        update: (_, serverService, authModel, gameModel, pidModel, controller) {
          if (controller != null) return controller;
          return PidController(
            serverService: serverService,
            authModel: authModel,
            gameModel: gameModel,
            pidModel: pidModel,
          );
        },
      ),

      // 功能控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider5<ServerService, AuthModel, GameModel, LoginModel, FunctionModel, FunctionController?>(
        create: (_) => null,
        update: (_, serverService, authModel, gameModel, loginModel, functionModel, controller) {
          if (controller != null) return controller;
          return FunctionController(
            functionModel: functionModel,
            serverService: serverService,
            authModel: authModel,
            gameModel: gameModel,
            loginModel: loginModel,
          );
        },
      ),

      // 数据采集控制器 - 依赖多个服务和模型
      ChangeNotifierProxyProvider4<ServerService, AuthModel, GameModel, DataCollectionModel, DataCollectionController?>(
        create: (_) => null,
        update: (_, serverService, authModel, gameModel, dataModel, controller) {
          if (controller != null) return controller;
          return DataCollectionController(
            serverService: serverService,
            authModel: authModel,
            gameModel: gameModel,
            dataCollectionModel: dataModel,
          );
        },
      ),

      // 配置管理控制器 - 依赖配置管理模型、认证模型、游戏模型、服务器服务和页头控制器
      ChangeNotifierProxyProvider5<ConfigManagementModel, AuthModel, GameModel, ServerService, HeaderController, ConfigManagementController?>(
        create: (_) => null,
        update: (_, configModel, authModel, gameModel, serverService, headerController, controller) {
          if (controller != null) return controller;
          return ConfigManagementController(
            configModel: configModel,
            authModel: authModel,
            gameModel: gameModel,
            serverService: serverService,
            headerController: headerController,
          );
        },
      ),
    ];
  }

  /// ========== 完整Provider列表 ==========

  /// 获取所有Provider（基础 + 代理）
  static List<dynamic> getAllProviders() {
    final basicProviders = getBasicProviders();
    final proxyProviders = getProxyProviders();

    _logger.i(_logTag, 'Provider注册完成', {
      'basicProviders': basicProviders.length,
      'proxyProviders': proxyProviders.length,
      'totalProviders': basicProviders.length + proxyProviders.length,
    });

    // 先注册基础Provider，再注册代理Provider（确保依赖关系正确）
    // 不进行类型转换，保持原始类型信息
    final allProviders = <dynamic>[
      ...basicProviders,
      ...proxyProviders,
    ];

    // 调试：输出Provider类型
    _logger.d(_logTag, '注册的Provider类型', {
      'basicTypes': basicProviders.map((p) => p.runtimeType.toString()).toList(),
      'proxyTypes': proxyProviders.map((p) => p.runtimeType.toString()).toList(),
    });

    return allProviders;
  }

  /// ========== 便捷方法 ==========

  /// 创建MultiProvider
  static MultiProvider createMultiProvider({required Widget child}) {
    return MultiProvider(
      providers: getAllProviders().cast(),
      child: child,
    );
  }

  /// 获取Provider统计信息
  static Map<String, dynamic> getProviderStats() {
    final basicProviders = getBasicProviders();
    final proxyProviders = getProxyProviders();
    
    return {
      'basicProviders': basicProviders.length,
      'proxyProviders': proxyProviders.length,
      'totalProviders': basicProviders.length + proxyProviders.length,
      'modelProviders': _countModelProviders(basicProviders),
      'serviceProviders': _countServiceProviders(basicProviders),
      'controllerProviders': proxyProviders.length,
    };
  }

  /// ========== 私有辅助方法 ==========

  /// 统计模型Provider数量
  static int _countModelProviders(List<dynamic> providers) {
    // 基础模型Provider数量：SidebarModel, ResourceModel, HeaderModel
    // 功能配置模型：FunctionModel, AimModel, FovModel, FireModel, PidModel, DataCollectionModel, StatusBarModel, ConfigManagementModel, HomeModel
    return 12; // 3个基础模型 + 9个功能模型
  }

  /// 统计服务Provider数量
  static int _countServiceProviders(List<dynamic> providers) {
    return 2; // ServerService 和 AuthService
  }

  /// ========== 调试和监控方法 ==========

  /// 验证Provider依赖关系
  static bool validateProviderDependencies() {
    try {
      // 这里可以添加依赖关系验证逻辑
      _logger.i(_logTag, 'Provider依赖关系验证通过');
      return true;
    } catch (e) {
      _logger.e(_logTag, 'Provider依赖关系验证失败', e.toString());
      return false;
    }
  }

  /// 获取Provider类型列表
  static List<String> getProviderTypes() {
    return [
      // 模型类型
      'AuthModel',
      'AppSettingsModel',
      'SidebarModel',
      'ResourceModel',
      'GameModel',
      'LoginModel',
      'UIConfigModel',
      'HeaderModel',
      'FunctionModel',
      'AimModel',
      'FovModel',
      'FireModel',
      'PidModel',
      'DataCollectionModel',
      'StatusBarModel',
      'ConfigManagementModel',
      'HomeModel',
      
      // 服务类型
      'ServerService',
      'AuthService',
      
      // 控制器类型
      'LoginController',
      'RegisterController',
      'HomeController',
      'HeaderController',
      'SideController',
      'AimController',
      'FireController',
      'FovController',
      'PidController',
      'FunctionController',
      'DataCollectionController',
      'ConfigManagementController',
    ];
  }

  /// 检查Provider是否已注册
  static bool isProviderRegistered(BuildContext context, Type providerType) {
    try {
      Provider.of(context, listen: false);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取Provider实例（用于调试）
  static T? getProvider<T>(BuildContext context) {
    try {
      return Provider.of<T>(context, listen: false);
    } catch (e) {
      _logger.w(_logTag, 'Provider获取失败', {
        'type': T.toString(),
        'error': e.toString(),
      });
      return null;
    }
  }

  /// 监听Provider变化（用于调试）
  static void monitorProviderChanges(BuildContext context) {
    // 这里可以添加Provider变化监听逻辑
    _logger.d(_logTag, 'Provider变化监听已启动');
  }
}
