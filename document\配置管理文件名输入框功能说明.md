# 配置管理文件名输入框功能说明

## 📋 功能概述

在配置管理页面的导出区域新增了一个文件名输入框，允许用户自定义导出文件的名称，提升用户体验和文件管理的便利性。

## 🎯 功能特性

### 1. 自定义文件名输入
- **输入框位置**：位于导出区域的配置类型选择器和导出按钮之间
- **输入提示**：显示"输入自定义文件名（可选）"的占位符文本
- **可选性**：留空时将使用默认的时间戳文件名

### 2. 智能文件名处理
- **自动扩展名**：系统自动为文件名添加 `.json` 扩展名
- **扩展名检测**：如果用户输入的文件名已包含 `.json`，则不会重复添加
- **默认文件名**：格式为 `BlWeb_配置类型_日期_时间.json`

### 3. 便捷操作按钮
- **清空按钮**：当输入框有内容时显示，点击可清空输入
- **生成默认文件名按钮**：点击可自动填入默认格式的文件名
- **实时更新**：按钮显示状态根据输入内容实时更新

## 🔧 技术实现

### 1. ConfigManagementController 扩展

#### 新增属性
```dart
// 文件名输入控制器
final TextEditingController _fileNameController = TextEditingController();

// Getters
TextEditingController get fileNameController => _fileNameController;
```

#### 新增方法
```dart
/// 清空文件名输入框
void clearFileName() {
  _fileNameController.clear();
  notifyListeners();
}

/// 设置文件名
void setFileName(String fileName) {
  _fileNameController.text = fileName;
  notifyListeners();
}

/// 处理文件名输入变化
void onFileNameChanged(String value) {
  notifyListeners();
}

/// 生成默认文件名
String generateDefaultFileName(String configType) {
  final now = DateTime.now();
  final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
  final timeStr = '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
  
  final typeName = ConfigManagementModel.configTypes[configType] ?? configType;
  return 'BlWeb_${typeName}_${dateStr}_$timeStr';
}
```

#### 导出方法修改
```dart
// 获取自定义文件名
final customFileName = _fileNameController.text.trim().isEmpty 
    ? null 
    : _fileNameController.text.trim();

// 执行导出
final success = await _configModel.exportConfig(
  configData: configData,
  configType: configType,
  customFileName: customFileName,
);
```

### 2. ConfigManagementModel 优化

#### 文件名处理逻辑
```dart
// 生成文件名
String fileName;
if (customFileName != null && customFileName.isNotEmpty) {
  // 确保自定义文件名有.json扩展名
  fileName = customFileName.endsWith('.json') 
      ? customFileName 
      : '$customFileName.json';
} else {
  fileName = _generateFileName(configType);
}
```

### 3. ConfigManagementScreen UI组件

#### 文件名输入框组件
```dart
Widget _buildFileNameInput(ConfigManagementController controller) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // 标题和帮助图标
      Row(
        children: [
          const Text('文件名', style: TextStyle(...)),
          Tooltip(
            message: '留空将使用默认文件名（包含时间戳）',
            child: Icon(Icons.help_outline, ...),
          ),
        ],
      ),
      
      // 输入框
      Container(
        child: Consumer<ConfigManagementController>(
          builder: (context, ctrl, child) {
            return TextField(
              controller: ctrl.fileNameController,
              onChanged: (value) => ctrl.onFileNameChanged(value),
              decoration: InputDecoration(
                hintText: '输入自定义文件名（可选）',
                suffixIcon: Row(
                  children: [
                    // 清空按钮（条件显示）
                    if (ctrl.fileNameController.text.isNotEmpty)
                      IconButton(
                        icon: Icon(Icons.clear),
                        onPressed: () => ctrl.clearFileName(),
                      ),
                    // 生成默认文件名按钮
                    IconButton(
                      icon: Icon(Icons.auto_awesome),
                      onPressed: () {
                        final defaultName = ctrl.generateDefaultFileName(_selectedConfigType);
                        ctrl.setFileName(defaultName);
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
      
      // 提示文本
      Text('提示：文件将自动添加 .json 扩展名'),
    ],
  );
}
```

## 🎨 用户界面设计

### 1. 布局结构
```
导出配置区域
├── 说明文本
├── 文件名输入框
│   ├── 标题 + 帮助图标
│   ├── 输入框 + 操作按钮
│   └── 提示文本
├── 导出按钮
└── 导出状态显示
```

### 2. 视觉设计
- **输入框样式**：圆角边框，白色背景，灰色边框
- **按钮图标**：清空按钮使用 `Icons.clear`，生成按钮使用 `Icons.auto_awesome`
- **颜色方案**：清空按钮灰色，生成按钮蓝色
- **提示文本**：小字号，灰色文字

### 3. 交互设计
- **实时反馈**：输入内容变化时，清空按钮显示/隐藏
- **工具提示**：帮助图标和按钮都有详细的工具提示
- **键盘支持**：支持标准的文本输入和编辑操作

## 📱 使用场景

### 1. 默认使用
- 用户不输入文件名，系统使用默认格式
- 文件名示例：`BlWeb_全部配置_20250718_1430.json`

### 2. 自定义文件名
- 用户输入：`我的配置备份`
- 实际文件名：`我的配置备份.json`

### 3. 快速生成
- 点击生成按钮，自动填入默认格式文件名
- 用户可以在此基础上进行修改

## ✅ 功能优势

### 1. 用户体验提升
- **个性化命名**：用户可以使用有意义的文件名
- **文件管理便利**：便于识别和管理导出的配置文件
- **操作简便**：提供快捷操作按钮，减少输入工作量

### 2. 系统健壮性
- **自动扩展名**：确保文件格式正确
- **输入验证**：处理各种输入情况
- **向下兼容**：不影响现有的默认文件名机制

### 3. 界面一致性
- **设计统一**：与现有UI风格保持一致
- **响应式设计**：适配不同屏幕尺寸
- **状态反馈**：提供清晰的操作反馈

## 🔄 后续扩展

### 可能的功能增强
1. **文件名模板**：预设多种文件名格式模板
2. **历史记录**：记住用户常用的文件名
3. **文件名验证**：检查文件名的合法性
4. **批量导出**：支持批量导出时的文件名规则

---

> **功能总结**: 通过添加文件名输入框，用户现在可以自定义导出文件的名称，提升了配置管理的用户体验
> **最后更新**: 2025年7月18日 | **维护者**: Flutter开发团队
