// ignore_for_file: use_build_context_synchronously, unnecessary_string_interpolations, unused_local_variable, unused_field, prefer_final_fields, unused_import

import 'package:flutter/material.dart';
import '../component/message_component.dart';
import '../models/login_model.dart';
import '../models/game_model.dart';
import '../models/auth_model.dart';
import '../utils/logger.dart';
// 不再需要直接导入WebSocket工具，使用ServerService
import '../services/server_service.dart'; // 导入ServerService
import '../controllers/side_controller.dart'; // 导入SideController
import 'dart:convert'; // 导入JSON转换库
import 'dart:async'; // 导入异步库
import 'package:provider/provider.dart';

/// 注册控制器，处理注册页面的所有逻辑
class RegisterController extends ChangeNotifier {
  static const Duration _registerTimeout = Duration(seconds: 10);
  static const String _logTag = 'RegisterController';

  
  final log = Logger();
  final ServerService _serverService;
  final LoginModel _loginModel;
  final GameModel _gameModel;
  final AuthModel _authModel;
  
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  
  bool agreeToTerms = false;
  bool isLoading = false;
  bool _isPro = false;
  List<bool> passwordVisibleList = [false, false];
  
  Timer? _registerTimer;
  bool _isDisposed = false;
  
  // 控制器列表
  List<TextEditingController> get controllers => [
    usernameController,
    passwordController,
    confirmPasswordController
  ];
  
  // 构造函数，初始化控制器
  RegisterController({
    required ServerService serverService,
    required LoginModel loginModel,
    required GameModel gameModel,
    required AuthModel authModel,
  })  : _serverService = serverService,
        _loginModel = loginModel,
        _gameModel = gameModel,
        _authModel = authModel {
    _setupListeners();
  }
  
  void _setupListeners() {
    _serverService.addMessageListener(_handleServerMessage);
  }
  
  // 处理服务器消息
  void _handleServerMessage(dynamic message) {
    if (_isDisposed) return;
    
    try {
      final responseData = jsonDecode(message.toString()) as Map<String, dynamic>;
      final action = responseData['action'] ?? '';
      
      // 严格按照API文档处理注册响应
      if (action == 'register_modify_response') {
        _handleRegisterResponse(responseData);
      }
    } catch (e) {
      log.e(_logTag, '解析服务器消息失败', e.toString());
    }
  }
  
  // 处理注册响应
  void _handleRegisterResponse(Map<String, dynamic> response) {
    _registerTimer?.cancel();
    
    if (isLoading) {
      isLoading = false;
      notifyListeners();
    }
    
    // 严格按照API文档检查状态：只接受"success"或"error"
    final status = response['status'] ?? '';
    final success = status == 'success';
    final message = response['message'] ?? '';
    
    log.i(_logTag, '注册响应: ${success ? '成功' : '失败'}, 状态: $status');
    
    if (success) {
      // 异步处理注册成功，但不等待结果
      _handleRegisterSuccess(response, message);
    } else {
      _handleRegisterFailure(message);
    }
  }
  
  Future<void> _handleRegisterSuccess(Map<String, dynamic> response, String message) async {
    log.i(_logTag, '开始处理注册成功响应');
    
    await _updateModelsFromResponse(response);
    await _updateLoginStatus();
    _resetFields();
    
    final context = WidgetsBinding.instance.focusManager.primaryFocus?.context;
    if (context != null) {
      log.i(_logTag, '找到有效的context，准备显示成功消息并跳转');
      _showSuccessAndNavigate(context, message);
    } else {
      log.w(_logTag, '未找到有效的context，无法跳转页面');
      // 尝试从注册页面的context获取
      _tryNavigateFromRegisterScreen(message);
    }
  }
  
  Future<void> _updateModelsFromResponse(Map<String, dynamic> response) async {
    try {
      final data = response['data'];
      if (data == null) {
        log.w(_logTag, '响应中没有data字段');
        return;
      }
      
      // 按照API文档处理userInfo
      final userInfo = data['userInfo'];
      if (userInfo != null) {
        final username = userInfo['username']?.toString() ?? '';
        final token = userInfo['token']?.toString() ?? '';
        final isPro = userInfo['isPro'] == true;
        
        log.i(_logTag, '处理用户信息', {
          'username': username,
          'hasToken': token.isNotEmpty,
          'isPro': isPro
        });
        
        // 更新登录模型
        if (username.isNotEmpty) {
          await _loginModel.updateUsername(username);
        }
        
        if (token.isNotEmpty) {
          await _loginModel.updateToken(token);
        }
        
        // 更新Pro状态
        await _loginModel.updateProStatus(isPro);
        
        // 重要：设置AuthModel的认证状态
        if (username.isNotEmpty && token.isNotEmpty) {
          await _authModel.setAuthInfo(
            username: username,
            token: token,
            rememberPassword: true,
          );
          log.i(_logTag, 'AuthModel认证状态已设置', {
            'isAuthenticated': _authModel.isAuthenticated,
            'username': _authModel.username
          });
        } else {
          log.w(_logTag, '用户名或token为空，无法设置认证状态');
        }
        
        log.i(_logTag, '用户信息已更新', {
          'username': username,
          'isPro': isPro,
          'hasToken': token.isNotEmpty
        });
      } else {
        log.w(_logTag, '响应中没有userInfo字段');
      }
      
      // 按照API文档处理homeConfig
      final homeConfig = data['homeConfig'];
      if (homeConfig != null) {
        final gameName = homeConfig['gameName']?.toString() ?? 'csgo2';
        final cardKey = homeConfig['cardKey']?.toString() ?? '';
        
        await _gameModel.updateCurrentGame(gameName);
        await _gameModel.updateCardKey(cardKey);
        
        log.i(_logTag, '游戏配置已更新', {
          'gameName': gameName,
          'hasCardKey': cardKey.isNotEmpty
        });
      } else {
        log.w(_logTag, '响应中没有homeConfig字段');
      }
    } catch (e) {
      log.e(_logTag, '更新配置出错', e.toString());
    }
  }
  
  Future<void> _updateLoginStatus() async {
    // 保存完整的登录信息，使用服务器返回的Pro状态（已经通过updateProStatus更新到LoginModel中）
    await _loginModel.saveLoginInfo(
      username: usernameController.text.trim(),
      password: passwordController.text.trim(),
      token: _loginModel.token,
      rememberPassword: true,
      loginStatus: 'true',
      lastLoginTime: DateTime.now().toIso8601String(),
      isPro: _loginModel.isPro, // 使用服务器返回的Pro状态，而不是用户选择的_isPro
    );
    
    // 设置刷新标志，以便主页面知道需要刷新
    _loginModel.updateRefreshAfterLogin(true);
    
    log.i(_logTag, '登录状态已更新', {
      'username': usernameController.text.trim(),
      'isPro': _loginModel.isPro, // 记录服务器返回的实际Pro状态
      'hasToken': _loginModel.token.isNotEmpty
    });
  }
  
  void _resetFields() {
    usernameController.clear();
    passwordController.clear();
    confirmPasswordController.clear();
    agreeToTerms = false;
    _isPro = false;
    notifyListeners();
  }
  
  void _showSuccessAndNavigate(BuildContext context, String message) {
    final successMsg = _buildSuccessMessage(message);
    log.i(_logTag, '显示成功消息', {'message': successMsg});
    
    _showMessage(context, successMsg, MessageType.success);
    
    // 检查认证状态
    log.i(_logTag, '跳转前检查状态', {
      'isAuthenticated': _authModel.isAuthenticated,
      'loginStatus': _loginModel.loginStatus,
      'username': _authModel.username,
      'hasToken': _authModel.token.isNotEmpty
    });
    
    // 延迟跳转，确保消息显示完成和状态更新完成
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (context.mounted) {
        log.i(_logTag, '开始执行页面跳转到首页');
        try {
          // 使用pushReplacementNamed替换当前页面，避免路由栈问题
          Navigator.of(context).pushReplacementNamed('/');
          log.i(_logTag, '页面跳转命令已执行');
        } catch (e) {
          log.e(_logTag, '页面跳转失败', e.toString());
          // 如果跳转失败，尝试使用pushNamedAndRemoveUntil
          try {
            Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
            log.i(_logTag, '备用跳转方式执行成功');
          } catch (e2) {
            log.e(_logTag, '备用跳转方式也失败', e2.toString());
          }
        }
      } else {
        log.w(_logTag, 'Context已销毁，无法跳转');
      }
    });
  }
  
  String _buildSuccessMessage(String message) {
    final baseMessage = message.isNotEmpty ? message : '注册成功';
    return _loginModel.isPro ? '$baseMessage (Pro版)' : baseMessage;
  }
  
  void _handleRegisterFailure(String message) {
    final context = WidgetsBinding.instance.focusManager.primaryFocus?.context;
    if (context != null) {
      // 根据API文档中定义的常见错误消息进行特殊处理
      String errorMessage;
      if (message.contains('用户名已存在')) {
        errorMessage = '用户名已存在，请选择其他用户名';
      } else if (message.contains('密码不符合要求')) {
        errorMessage = '密码不符合要求，请检查密码格式';
      } else if (message.contains('注册失败，请重试')) {
        errorMessage = '服务器内部错误，注册失败，请稍后重试';
      } else {
        errorMessage = message.isNotEmpty ? message : '注册失败，请重试';
      }
      
      log.w(_logTag, '注册失败', {'原始消息': message, '处理后消息': errorMessage});
      _showMessage(context, errorMessage, MessageType.error);
    }
  }
  
  // 处理注册按钮点击
  Future<void> handleRegister(BuildContext context, {bool isPro = false}) async {
    if (!_validateInput(context) || !await _ensureConnection(context)) return;
    
    _isPro = isPro;
    _startRegister();
    
    try {
      await _updateUserData();
      await _sendRegisterRequest();
      _showMessage(context, '注册请求已发送，等待响应...', MessageType.info);
    } catch (e) {
      _handleRegisterError(context, e);
    }
  }
  
  bool _validateInput(BuildContext context) {
    final username = usernameController.text.trim();
    final password = passwordController.text.trim();
    final confirmPassword = confirmPasswordController.text.trim();
    
    if (username.isEmpty) {
      _showMessage(context, '用户名不能为空', MessageType.warning);
      return false;
    }
    
    if (username.length < 3) {
      _showMessage(context, '用户名长度不能少于3位', MessageType.warning);
      return false;
    }
    
    if (password.isEmpty) {
      _showMessage(context, '密码不能为空', MessageType.warning);
      return false;
    }
    
    if (password != confirmPassword) {
      _showMessage(context, '两次输入的密码不一致', MessageType.error);
      return false;
    }
    
    if (!agreeToTerms) {
      _showMessage(context, '请先同意用户协议与隐私政策', MessageType.warning);
      return false;
    }
    
    return true;
  }
  
  Future<bool> _ensureConnection(BuildContext context) async {
    if (_serverService.isConnected) return true;
    
    if (_loginModel.serverAddress.isEmpty || _loginModel.serverPort.isEmpty) {
      _showMessage(context, '未连接到服务器，请在登录页面连接服务器后再试', MessageType.warning);
      return false;
    }
    
    _showMessage(context, '正在连接到服务器...', MessageType.info);
    
    try {
      await _serverService.handleConnectService(
        context,
        _loginModel.serverAddress,
        _loginModel.serverPort,
        _loginModel.token,
      );
      
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (!_serverService.isConnected) {
        _showMessage(context, '无法连接到服务器，请在登录页面连接服务器后再试', MessageType.warning);
        return false;
      }
      
      return true;
    } catch (e) {
      _showMessage(context, '连接服务器失败: ${e.toString()}', MessageType.error);
      return false;
    }
  }
  
  void _startRegister() {
    isLoading = true;
    notifyListeners();
    
    _registerTimer?.cancel();
    _registerTimer = Timer(_registerTimeout, _handleRegisterTimeout);
  }
  
  void _handleRegisterTimeout() {
    if (isLoading) {
      isLoading = false;
      notifyListeners();
      log.w(_logTag, '注册请求超时');
    }
  }
  
  Future<void> _updateUserData() async {
    final username = usernameController.text.trim();
    final password = passwordController.text.trim();
    
    await _loginModel.updateUsername(username);
    await _loginModel.updatePassword(password);
    
    await _authModel.setAuthInfo(
      username: username,
      token: _loginModel.token,
      rememberPassword: true,
    );
  }
  
  Future<void> _sendRegisterRequest() async {
    final request = _buildRegisterRequest();
    
    // 验证请求格式是否符合API文档要求
    if (!_validateRequestFormat(request)) {
      throw Exception('请求格式不符合API要求');
    }
    
    if (!_serverService.isConnected) {
      throw Exception('WebSocket连接已断开');
    }
    
    final requestJson = jsonEncode(request);
    log.i(_logTag, '发送注册请求', {
      'isPro': _isPro,
      'username': request['content']['username'],
      'gameList': request['content']['gameList'],
      'defaultGame': request['content']['defaultGame']
    });
    
    _serverService.sendMessage(requestJson);
  }
  
  // 验证请求格式是否符合API文档要求
  bool _validateRequestFormat(Map<String, dynamic> request) {
    try {
      // 检查必需的顶级字段
      if (request['action'] != 'register_modify') {
        log.e(_logTag, '请求action不正确', request['action']);
        return false;
      }
      
      final content = request['content'];
      if (content == null) {
        log.e(_logTag, '请求缺少content字段');
        return false;
      }
      
      // 检查必需的content字段
      final requiredFields = ['gameList', 'defaultGame', 'username', 'password', 'isPro', 'createdAt', 'updatedAt'];
      for (final field in requiredFields) {
        if (!content.containsKey(field)) {
          log.e(_logTag, '请求缺少必需字段', field);
          return false;
        }
      }
      
      // 检查gameList格式
      final gameList = content['gameList'];
      if (gameList is! List || gameList.isEmpty) {
        log.e(_logTag, 'gameList格式不正确');
        return false;
      }
      
      // 检查必需的游戏是否都包含在内
      final requiredGames = ['apex', 'cf', 'cfhd', 'csgo2', 'sjz', 'ssjj2', 'pubg', 'wwqy'];
      for (final game in requiredGames) {
        if (!gameList.contains(game)) {
          log.e(_logTag, '缺少必需的游戏', game);
          return false;
        }
      }
      
      // 检查时间格式
      final createdAt = content['createdAt'];
      final updatedAt = content['updatedAt'];
      try {
        DateTime.parse(createdAt);
        DateTime.parse(updatedAt);
      } catch (e) {
        log.e(_logTag, '时间格式不正确', e.toString());
        return false;
      }
      
      log.i(_logTag, '请求格式验证通过');
      return true;
    } catch (e) {
      log.e(_logTag, '请求格式验证失败', e.toString());
      return false;
    }
  }
  
  Map<String, dynamic> _buildRegisterRequest() {
    return {
      'action': 'register_modify',
      'content': {
        'gameList': ['apex', 'cf', 'cfhd', 'csgo2', 'sjz', 'ssjj2', 'pubg', 'wwqy'],
        'defaultGame': 'csgo2',
        'username': usernameController.text.trim(),
        'password': passwordController.text.trim(),
        'isPro': _isPro,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
    };
  }
  
  void _handleRegisterError(BuildContext context, dynamic error) {
    _registerTimer?.cancel();
    isLoading = false;
    notifyListeners();
    
    log.e(_logTag, '注册请求失败', error.toString());
    _showMessage(context, '发送注册请求失败: ${error.toString()}', MessageType.error);
  }
  
  // 显示提示消息
  void _showMessage(BuildContext context, String message, MessageType type, {Duration? duration}) {
    if (context.mounted) {
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: type,
        duration: duration ?? const Duration(seconds: 2),
      );
    }
  }
  
  // 返回登录页
  void navigateToLogin(BuildContext context) {
    Navigator.pop(context);
  }
  
  // 定义输入字段列表
  List<Map<String, dynamic>> getInputFields() {
    return [
      {
        'label': '用户名',
        'hint': '请输入用户名',
        'icon': Icons.person,
        'onChanged': (String value) {},
        'validator': (String? value) {
          if (value == null || value.isEmpty) return '请输入用户名';
          if (value.length < 3) return '用户名长度不能少于3位';
          return null;
        }
      },
      {
        'label': '密码',
        'hint': '请输入密码',
        'icon': Icons.lock,
        'isPassword': true,
        'onChanged': (String value) {},
        'validator': (String? value) {
          if (value == null || value.isEmpty) return '请输入密码';
          return null;
        }
      },
      {
        'label': '确认密码',
        'hint': '请再次输入密码',
        'icon': Icons.lock_outline,
        'isPassword': true,
        'onChanged': (String value) {},
        'validator': (String? value) {
          if (value == null || value.isEmpty) return '请确认密码';
          if (value != passwordController.text) return '两次输入的密码不一致';
          return null;
        }
      },
    ];
  }
  
  // 释放资源
  @override
  void dispose() {
    _isDisposed = true;
    
    _registerTimer?.cancel();
    _serverService.removeMessageListener(_handleServerMessage);
    
    for (final controller in controllers) {
      controller.dispose();
    }
    
    log.i(_logTag, '注册控制器资源已释放');
    super.dispose();
  }
  
  // 更新密码可见性
  void updatePasswordVisibility(int index, bool isVisible) {
    if (index < passwordVisibleList.length) {
      passwordVisibleList[index] = isVisible;
      notifyListeners();
    }
  }
  
  // 更新同意条款状态
  void updateAgreeToTerms(bool value) {
    agreeToTerms = value;
    notifyListeners();
  }
  
    // 处理Pro注册按钮点击
  Future<void> handleProRegister(BuildContext context) async {
    await handleRegister(context, isPro: true);
  }
  
  // 测试方法：验证请求格式是否符合API文档
  Map<String, dynamic> getTestRegisterRequest({bool isPro = false}) {
    // 设置测试数据
    usernameController.text = 'testuser';
    passwordController.text = 'testpass123';
    _isPro = isPro;
    
    // 构建请求
    final request = _buildRegisterRequest();
    
    // 验证格式
    final isValid = _validateRequestFormat(request);
    log.i(_logTag, '测试请求格式验证', {
      'isPro': isPro,
      'isValid': isValid,
      'request': request
    });
    
    return request;
  }
  
  // 尝试从注册页面获取context并跳转
  void _tryNavigateFromRegisterScreen(String message) {
    // 延迟一下再尝试跳转，给UI一些时间更新
    Future.delayed(const Duration(milliseconds: 100), () {
      final context = WidgetsBinding.instance.focusManager.primaryFocus?.context;
      if (context != null) {
        log.i(_logTag, '延迟后找到context，执行跳转');
        _showSuccessAndNavigate(context, message);
      } else {
        log.e(_logTag, '延迟后仍未找到context，跳转失败');
      }
    });
  }
} 