import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// 数据收集模型 - 管理数据收集相关的配置和状态
class DataCollectionModel extends ChangeNotifier {
  // 常量定义
  static const String _keyPrefix = 'data_';
  static const bool _defaultIsEnabled = false;
  static const String _defaultMapHotkey = '右键';
  static const String _defaultTargetHotkey = '前侧';
  static const String _defaultCollectionName = '';
  static const String _defaultTeamSide = '无';

  // 数据收集参数
  bool _isEnabled = _defaultIsEnabled;
  String _mapHotkey = _defaultMapHotkey;
  String _targetHotkey = _defaultTargetHotkey;
  String _collectionName = _defaultCollectionName;
  String _teamSide = _defaultTeamSide;
  
  // 元数据
  String _username = 'admin';
  String _gameName = 'csgo2';
  String _createdAt = '';
  String _updatedAt = '';
  
  // Getters
  bool get isEnabled => _isEnabled;
  String get mapHotkey => _mapHotkey;
  String get targetHotkey => _targetHotkey;
  String get collectionName => _collectionName;
  String get teamSide => _teamSide;
  String get username => _username;
  String get gameName => _gameName;
  String get createdAt => _createdAt;
  String get updatedAt => _updatedAt;
  
  // Setters
  set isEnabled(bool value) {
    if (_isEnabled != value) {
      _isEnabled = value;
      _updateTimestamp();
      notifyListeners();
    }
  }
  
  set mapHotkey(String value) {
    if (_mapHotkey != value) {
      _mapHotkey = value;
      _updateTimestamp();
      notifyListeners();
    }
  }
  
  set targetHotkey(String value) {
    if (_targetHotkey != value) {
      _targetHotkey = value;
      _updateTimestamp();
      notifyListeners();
    }
  }
  
  set collectionName(String value) {
    if (_collectionName != value) {
      _collectionName = value;
      _updateTimestamp();
      notifyListeners();
    }
  }
  
  set teamSide(String value) {
    if (_teamSide != value && _isValidTeamSide(value)) {
      _teamSide = value;
      _updateTimestamp();
      notifyListeners();
    }
  }
  
  set username(String value) {
    if (_username != value) {
      _username = value;
      notifyListeners();
    }
  }
  
  set gameName(String value) {
    if (_gameName != value) {
      _gameName = value;
      notifyListeners();
    }
  }
  
  /// 构造函数
  DataCollectionModel() {
    // 移除直接调用loadSettings()，改为延迟初始化
    _initializeAsync();
  }
  
  /// 异步初始化
  void _initializeAsync() {
    // 使用microtask延迟执行，避免在构造函数中直接调用notifyListeners
    Future.microtask(() async {
      await loadSettings();
    });
  }
  
  /// 更新时间戳
  void _updateTimestamp() {
    _updatedAt = DateTime.now().toIso8601String();
  }
  
  /// 重置为默认值
  void resetToDefaults() {
    _isEnabled = _defaultIsEnabled;
    _mapHotkey = _defaultMapHotkey;
    _targetHotkey = _defaultTargetHotkey;
    _collectionName = _defaultCollectionName;
    _teamSide = _defaultTeamSide;
    _updateTimestamp();
    notifyListeners();
  }
  
  /// 从配置中加载设置
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 加载数据收集参数
    _isEnabled = prefs.getBool('${_keyPrefix}isEnabled') ?? _defaultIsEnabled;
    _mapHotkey = prefs.getString('${_keyPrefix}mapHotkey') ?? _defaultMapHotkey;
    _targetHotkey = prefs.getString('${_keyPrefix}targetHotkey') ?? _defaultTargetHotkey;
    _collectionName = prefs.getString('${_keyPrefix}collectionName') ?? _defaultCollectionName;
    _teamSide = prefs.getString('${_keyPrefix}teamSide') ?? _defaultTeamSide;
    
    // 加载元数据
    _username = prefs.getString('${_keyPrefix}username') ?? _username;
    _gameName = prefs.getString('${_keyPrefix}gameName') ?? _gameName;
    _createdAt = prefs.getString('${_keyPrefix}createdAt') ?? '';
    _updatedAt = prefs.getString('${_keyPrefix}updatedAt') ?? '';
    
    // 如果是首次加载，创建时间戳
    if (_createdAt.isEmpty) {
      _createdAt = DateTime.now().toIso8601String();
      _updatedAt = _createdAt;
    }
    
    notifyListeners();
  }
  
  /// 保存设置到持久化存储
  Future<void> saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 保存数据收集参数
    await Future.wait([
      prefs.setBool('${_keyPrefix}isEnabled', _isEnabled),
      prefs.setString('${_keyPrefix}mapHotkey', _mapHotkey),
      prefs.setString('${_keyPrefix}targetHotkey', _targetHotkey),
      prefs.setString('${_keyPrefix}collectionName', _collectionName),
      prefs.setString('${_keyPrefix}teamSide', _teamSide),
      prefs.setString('${_keyPrefix}username', _username),
      prefs.setString('${_keyPrefix}gameName', _gameName),
      prefs.setString('${_keyPrefix}createdAt', _createdAt),
      prefs.setString('${_keyPrefix}updatedAt', _updatedAt),
    ]);
  }
  
  /// 更新用户和游戏信息
  void updateUserGameInfo(String username, String gameName) {
    _username = username;
    _gameName = gameName;
    _updateTimestamp();
    notifyListeners();
  }
  
  /// 从JSON获取配置
  void fromJson(Map<String, dynamic> json) {
    final content = json['content'];
    if (content == null) return;

    _isEnabled = content['isEnabled'] ?? _defaultIsEnabled;
    _mapHotkey = content['mapHotkey'] ?? _mapHotkey;
    _targetHotkey = content['targetHotkey'] ?? _targetHotkey;
    _collectionName = content['collectionName'] ?? _collectionName;
    _teamSide = content['teamSide'] ?? _teamSide;
    
    _username = content['username'] ?? _username;
    _gameName = content['gameName'] ?? _gameName;
    _createdAt = content['createdAt'] ?? _createdAt;
    _updatedAt = content['updatedAt'] ?? _updatedAt;
    
    notifyListeners();
  }
  
  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'action': 'data_collection_modify',
      'content': {
        'username': _username,
        'gameName': _gameName,
        'isEnabled': _isEnabled,
        'mapHotkey': _mapHotkey,
        'targetHotkey': _targetHotkey,
        'collectionName': _collectionName,
        'teamSide': _teamSide,
        'createdAt': _createdAt,
        'updatedAt': _updatedAt,
      }
    };
  }
  
  /// 获取JSON字符串
  String toJsonString() => jsonEncode(toJson());

  bool _isValidTeamSide(String value) {
    return ['警方', '匪方', '无'].contains(value);
  }
} 