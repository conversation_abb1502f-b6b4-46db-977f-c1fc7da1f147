# Dart编程语言核心知识体系

## 🎯 Dart语言基础

### 变量与数据类型
```dart
// 变量声明
var name = 'Dart';           // 类型推导
String language = 'Dart';    // 显式类型
final version = '3.0';       // 运行时常量
const pi = 3.14159;          // 编译时常量

// 基础数据类型
int age = 25;
double height = 1.75;
bool isActive = true;
String message = 'Hello, Dart!';

// 可空类型
String? nullableName;        // 可以为null
String nonNullableName = 'Required';  // 不能为null

// 延迟初始化
late String description;     // 稍后初始化
late final String config = loadConfig();  // 延迟加载
```

### 函数与方法
```dart
// 基础函数
String greet(String name) {
  return 'Hello, $name!';
}

// 箭头函数
String greetShort(String name) => 'Hello, $name!';

// 可选参数
String introduce(String name, [int? age, String? city]) {
  var intro = 'I am $name';
  if (age != null) intro += ', $age years old';
  if (city != null) intro += ' from $city';
  return intro;
}

// 命名参数
String createProfile({
  required String name,
  int age = 0,
  String? email,
}) {
  return 'Profile: $name, $age, ${email ?? 'No email'}';
}

// 高阶函数
List<T> transform<T>(List<T> items, T Function(T) transformer) {
  return items.map(transformer).toList();
}

// 闭包
Function createMultiplier(int factor) {
  return (int value) => value * factor;
}
```

### 类与对象
```dart
// 基础类定义
class Person {
  String name;
  int age;
  
  // 构造函数
  Person(this.name, this.age);
  
  // 命名构造函数
  Person.guest() : name = 'Guest', age = 0;
  
  Person.fromJson(Map<String, dynamic> json)
      : name = json['name'],
        age = json['age'];
  
  // 方法
  void introduce() {
    print('Hi, I am $name, $age years old.');
  }
  
  // Getter和Setter
  String get displayName => name.toUpperCase();
  
  set displayName(String value) {
    name = value.toLowerCase();
  }
  
  // 操作符重载
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Person && other.name == name && other.age == age;
  }
  
  @override
  int get hashCode => name.hashCode ^ age.hashCode;
  
  @override
  String toString() => 'Person(name: $name, age: $age)';
}

// 继承
class Employee extends Person {
  String department;
  double salary;
  
  Employee(String name, int age, this.department, this.salary)
      : super(name, age);
  
  @override
  void introduce() {
    super.introduce();
    print('I work in $department department.');
  }
  
  // 抽象方法实现
  void work() {
    print('$name is working in $department');
  }
}
```

## 🔄 异步编程

### Future与async/await
```dart
// 基础Future
Future<String> fetchUserData(String userId) async {
  // 模拟网络请求
  await Future.delayed(Duration(seconds: 2));
  return 'User data for $userId';
}

// 错误处理
Future<String> fetchDataWithErrorHandling(String id) async {
  try {
    final data = await fetchUserData(id);
    return data;
  } catch (e) {
    print('Error fetching data: $e');
    return 'Default data';
  }
}

// 并发执行
Future<Map<String, String>> fetchMultipleData() async {
  final futures = [
    fetchUserData('1'),
    fetchUserData('2'),
    fetchUserData('3'),
  ];
  
  final results = await Future.wait(futures);
  
  return {
    'user1': results[0],
    'user2': results[1],
    'user3': results[2],
  };
}

// 超时处理
Future<String> fetchWithTimeout(String id) async {
  try {
    return await fetchUserData(id).timeout(Duration(seconds: 5));
  } on TimeoutException {
    return 'Request timed out';
  }
}
```

### Stream与流处理
```dart
// 创建Stream
Stream<int> numberStream() async* {
  for (int i = 1; i <= 10; i++) {
    await Future.delayed(Duration(milliseconds: 500));
    yield i;
  }
}

// Stream转换
Stream<String> processNumbers() {
  return numberStream()
      .where((number) => number % 2 == 0)  // 过滤偶数
      .map((number) => 'Number: $number')  // 转换为字符串
      .take(3);                            // 只取前3个
}

// StreamController
class DataService {
  final _controller = StreamController<String>();
  
  Stream<String> get dataStream => _controller.stream;
  
  void addData(String data) {
    _controller.add(data);
  }
  
  void addError(Object error) {
    _controller.addError(error);
  }
  
  void close() {
    _controller.close();
  }
}

// 监听Stream
void listenToStream() async {
  final service = DataService();
  
  // 监听数据
  service.dataStream.listen(
    (data) => print('Received: $data'),
    onError: (error) => print('Error: $error'),
    onDone: () => print('Stream closed'),
  );
  
  // 添加数据
  service.addData('Hello');
  service.addData('World');
  service.close();
}

// 广播Stream
Stream<int> createBroadcastStream() {
  return Stream.periodic(Duration(seconds: 1), (i) => i).asBroadcastStream();
}
```

## 🧩 泛型与集合

### 泛型编程
```dart
// 泛型类
class Box<T> {
  T _value;
  
  Box(this._value);
  
  T get value => _value;
  set value(T newValue) => _value = newValue;
  
  // 泛型方法
  R transform<R>(R Function(T) transformer) {
    return transformer(_value);
  }
}

// 泛型约束
class NumberBox<T extends num> {
  T _value;
  
  NumberBox(this._value);
  
  T get value => _value;
  
  bool isPositive() => _value > 0;
  T add(T other) => (_value + other) as T;
}

// 泛型函数
T getFirst<T>(List<T> items) {
  if (items.isEmpty) throw StateError('List is empty');
  return items.first;
}

List<R> mapList<T, R>(List<T> items, R Function(T) mapper) {
  return items.map(mapper).toList();
}
```

### 集合操作详解
```dart
// List操作
void listOperations() {
  List<int> numbers = [1, 2, 3, 4, 5];
  
  // 基础操作
  numbers.add(6);
  numbers.insert(0, 0);
  numbers.remove(3);
  numbers.removeAt(0);
  
  // 函数式操作
  var doubled = numbers.map((n) => n * 2).toList();
  var evens = numbers.where((n) => n % 2 == 0).toList();
  var sum = numbers.reduce((a, b) => a + b);
  var product = numbers.fold(1, (prev, curr) => prev * curr);
  
  // 排序
  numbers.sort();
  numbers.sort((a, b) => b.compareTo(a)); // 降序
  
  // 查找
  var first = numbers.firstWhere((n) => n > 3);
  var index = numbers.indexWhere((n) => n > 3);
  bool contains = numbers.any((n) => n > 10);
  bool allPositive = numbers.every((n) => n > 0);
}

// Set操作
void setOperations() {
  Set<String> fruits = {'apple', 'banana', 'orange'};
  Set<String> citrus = {'orange', 'lemon', 'lime'};
  
  // 集合运算
  var union = fruits.union(citrus);           // 并集
  var intersection = fruits.intersection(citrus); // 交集
  var difference = fruits.difference(citrus);  // 差集
  
  // 检查操作
  bool hasApple = fruits.contains('apple');
  bool isSubset = {'apple'}.difference(fruits).isEmpty;
}

// Map操作
void mapOperations() {
  Map<String, int> scores = {
    'Alice': 95,
    'Bob': 87,
    'Charlie': 92,
  };
  
  // 基础操作
  scores['David'] = 89;
  scores.remove('Bob');
  scores.putIfAbsent('Eve', () => 90);
  
  // 遍历
  scores.forEach((name, score) => print('$name: $score'));
  
  // 转换
  var names = scores.keys.toList();
  var values = scores.values.toList();
  var entries = scores.entries.map((e) => '${e.key}: ${e.value}').toList();
  
  // 过滤和映射
  var highScores = Map.fromEntries(
    scores.entries.where((e) => e.value > 90)
  );
  
  var normalizedScores = scores.map((name, score) => 
    MapEntry(name, score / 100.0)
  );
}
```

## 🎨 高级特性

### Mixin混入
```dart
// 定义Mixin
mixin Swimming {
  void swim() => print('Swimming');
  
  bool get canSwim => true;
}

mixin Flying {
  void fly() => print('Flying');
  
  double get maxAltitude => 1000.0;
}

mixin Walking {
  void walk() => print('Walking');
  
  double get speed => 5.0;
}

// 使用Mixin
class Animal {
  String name;
  Animal(this.name);
}

class Duck extends Animal with Swimming, Flying, Walking {
  Duck(String name) : super(name);
  
  @override
  double get maxAltitude => 500.0;  // 重写mixin属性
}

class Fish extends Animal with Swimming {
  Fish(String name) : super(name);
}

// 带约束的Mixin
mixin Domesticated on Animal {
  String? owner;
  
  void setOwner(String ownerName) {
    owner = ownerName;
    print('$name now belongs to $ownerName');
  }
}

class Dog extends Animal with Walking, Domesticated {
  Dog(String name) : super(name);
}
```

### 扩展方法
```dart
// 基础扩展
extension StringExtension on String {
  bool get isEmail {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(this);
  }
  
  String capitalize() {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
  }
  
  String truncate(int maxLength) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength)}...';
  }
}

// 泛型扩展
extension ListExtension<T> on List<T> {
  T? get firstOrNull => isEmpty ? null : first;
  T? get lastOrNull => isEmpty ? null : last;
  
  List<T> get unique => Set<T>.from(this).toList();
  
  List<List<T>> chunk(int size) {
    List<List<T>> chunks = [];
    for (int i = 0; i < length; i += size) {
      chunks.add(sublist(i, (i + size > length) ? length : i + size));
    }
    return chunks;
  }
}

// 数字扩展
extension NumExtension on num {
  bool get isEven => this % 2 == 0;
  bool get isOdd => this % 2 != 0;
  
  String get ordinal {
    if (this >= 11 && this <= 13) return '${this}th';
    switch (this % 10) {
      case 1: return '${this}st';
      case 2: return '${this}nd';
      case 3: return '${this}rd';
      default: return '${this}th';
    }
  }
}

// 使用扩展
void useExtensions() {
  // String扩展
  String email = '<EMAIL>';
  print(email.isEmail);  // true
  
  String name = 'john doe';
  print(name.capitalize());  // John doe
  
  // List扩展
  List<int> numbers = [1, 2, 2, 3, 3, 3];
  print(numbers.unique);  // [1, 2, 3]
  print(numbers.chunk(2));  // [[1, 2], [2, 3], [3, 3]]
  
  // Num扩展
  print(21.ordinal);  // 21st
  print(22.isEven);   // true
}
```

### 枚举增强
```dart
// 基础枚举
enum Status {
  pending,
  approved,
  rejected,
}

// 增强枚举
enum HttpStatus {
  ok(200, 'OK'),
  notFound(404, 'Not Found'),
  internalError(500, 'Internal Server Error');
  
  const HttpStatus(this.code, this.message);
  
  final int code;
  final String message;
  
  // 方法
  bool get isSuccess => code >= 200 && code < 300;
  bool get isError => code >= 400;
  
  // 静态方法
  static HttpStatus? fromCode(int code) {
    for (var status in HttpStatus.values) {
      if (status.code == code) return status;
    }
    return null;
  }
}

// 使用增强枚举
void useEnhancedEnum() {
  var status = HttpStatus.ok;
  print(status.code);      // 200
  print(status.message);   // OK
  print(status.isSuccess); // true
  
  var notFound = HttpStatus.fromCode(404);
  print(notFound?.message); // Not Found
}
```

## 🔧 元编程与注解

### 注解定义与使用
```dart
// 自定义注解
class Deprecated {
  final String message;
  const Deprecated(this.message);
}

class JsonKey {
  final String name;
  final bool includeIfNull;
  
  const JsonKey({required this.name, this.includeIfNull = true});
}

// 使用注解
class User {
  @JsonKey(name: 'user_id')
  final String id;
  
  @JsonKey(name: 'full_name')
  final String name;
  
  @JsonKey(includeIfNull: false)
  final String? email;
  
  @Deprecated('Use fullName instead')
  String get userName => name;
  
  const User({
    required this.id,
    required this.name,
    this.email,
  });
}
```

### 反射与镜像
```dart
import 'dart:mirrors';

// 反射工具类
class ReflectionHelper {
  static Map<String, dynamic> objectToMap(Object object) {
    final mirror = reflect(object);
    final classMirror = mirror.type;
    final result = <String, dynamic>{};
    
    // 获取所有字段
    classMirror.declarations.forEach((symbol, declaration) {
      if (declaration is VariableMirror && !declaration.isPrivate) {
        final name = MirrorSystem.getName(symbol);
        final value = mirror.getField(symbol).reflectee;
        result[name] = value;
      }
    });
    
    return result;
  }
  
  static T createInstance<T>(Type type, List<dynamic> args) {
    final classMirror = reflectClass(type);
    final instanceMirror = classMirror.newInstance(Symbol(''), args);
    return instanceMirror.reflectee as T;
  }
  
  static dynamic invokeMethod(Object object, String methodName, [List<dynamic>? args]) {
    final mirror = reflect(object);
    final result = mirror.invoke(Symbol(methodName), args ?? []);
    return result.reflectee;
  }
}
```

## 🛠️ 错误处理与调试

### 异常处理策略
```dart
// 自定义异常
class ValidationException implements Exception {
  final String message;
  final String field;
  
  ValidationException(this.message, {required this.field});
  
  @override
  String toString() => 'ValidationException: $message (field: $field)';
}

class NetworkException implements Exception {
  final int statusCode;
  final String message;
  
  NetworkException(this.statusCode, this.message);
  
  @override
  String toString() => 'NetworkException($statusCode): $message';
}

// 异常处理工具
class ErrorHandler {
  static void handleError(Object error, StackTrace stackTrace) {
    if (error is ValidationException) {
      print('Validation Error: ${error.message}');
      // 显示用户友好的错误信息
    } else if (error is NetworkException) {
      print('Network Error: ${error.message}');
      // 重试机制或离线模式
    } else {
      print('Unexpected Error: $error');
      // 发送错误报告
      sendErrorReport(error, stackTrace);
    }
  }
  
  static void sendErrorReport(Object error, StackTrace stackTrace) {
    // 发送到错误追踪服务
    print('Sending error report...');
  }
}

// 使用示例
Future<User> validateAndCreateUser(Map<String, dynamic> data) async {
  try {
    // 验证必填字段
    if (data['name'] == null || data['name'].toString().isEmpty) {
      throw ValidationException('Name is required', field: 'name');
    }
    
    if (data['email'] == null || !data['email'].toString().contains('@')) {
      throw ValidationException('Valid email is required', field: 'email');
    }
    
    // 创建用户
    return User(
      id: data['id'],
      name: data['name'],
      email: data['email'],
    );
    
  } catch (e, stackTrace) {
    ErrorHandler.handleError(e, stackTrace);
    rethrow;
  }
}
```

### 调试技巧
```dart
// 断言调试
void debugExample() {
  int value = 10;
  
  // 开发模式下的断言
  assert(value > 0, 'Value must be positive');
  assert(() {
    print('Debug: value is $value');
    return true;
  }());
}

// 条件编译
void conditionalCode() {
  // 只在调试模式下执行
  if (kDebugMode) {
    print('Debug information');
  }
  
  // 生产环境代码
  if (kReleaseMode) {
    // 性能优化代码
  }
}

// 性能测试
void performanceTest() {
  final stopwatch = Stopwatch()..start();
  
  // 执行要测试的代码
  for (int i = 0; i < 1000000; i++) {
    // 一些计算
  }
  
  stopwatch.stop();
  print('Execution time: ${stopwatch.elapsedMilliseconds}ms');
}
```

## 📚 库开发与包管理

### 创建可复用库
```dart
// lib/src/calculator.dart
class Calculator {
  /// 加法运算
  /// 
  /// 计算两个数的和
  /// 
  /// 示例:
  /// ```dart
  /// final calc = Calculator();
  /// print(calc.add(2, 3)); // 5
  /// ```
  double add(double a, double b) => a + b;
  
  /// 减法运算
  double subtract(double a, double b) => a - b;
  
  /// 乘法运算
  double multiply(double a, double b) => a * b;
  
  /// 除法运算
  /// 
  /// 如果除数为0，抛出[ArgumentError]
  double divide(double a, double b) {
    if (b == 0) {
      throw ArgumentError('Cannot divide by zero');
    }
    return a / b;
  }
}

// lib/my_math_library.dart (公共API)
library my_math_library;

export 'src/calculator.dart';
export 'src/geometry.dart';

// 隐藏内部实现
// export 'src/internal_helper.dart' hide InternalClass;
```

### 文档与测试
```dart
// test/calculator_test.dart
import 'package:test/test.dart';
import 'package:my_math_library/my_math_library.dart';

void main() {
  group('Calculator', () {
    late Calculator calculator;
    
    setUp(() {
      calculator = Calculator();
    });
    
    test('addition works correctly', () {
      expect(calculator.add(2, 3), equals(5));
      expect(calculator.add(-1, 1), equals(0));
      expect(calculator.add(0.1, 0.2), closeTo(0.3, 0.001));
    });
    
    test('division by zero throws error', () {
      expect(() => calculator.divide(10, 0), throwsArgumentError);
    });
    
    test('all operations with edge cases', () {
      expect(calculator.multiply(0, 100), equals(0));
      expect(calculator.subtract(5, 5), equals(0));
    });
  });
}
```

这份Dart知识体系涵盖了从基础语法到高级特性的完整内容，为Flutter开发提供了坚实的语言基础。 