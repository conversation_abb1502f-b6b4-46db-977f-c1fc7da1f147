// ignore_for_file: prefer_final_fields

import 'package:flutter/material.dart';
import 'app_constants.dart';

/// 响应式布局配置类
class ResponsiveConfig {
  final bool isExtraSmall;
  final bool isSmall;
  final bool isMobile;
  final bool isTablet;
  final bool isDesktop;
  final bool isLarge;
  final bool isExtraLarge;
  final double screenWidth;
  final double screenHeight;
  final EdgeInsets padding;
  final double spacing;
  final int columns;

  const ResponsiveConfig({
    required this.isExtraSmall,
    required this.isSmall,
    required this.isMobile,
    required this.isTablet,
    required this.isDesktop,
    required this.isLarge,
    required this.isExtraLarge,
    required this.screenWidth,
    required this.screenHeight,
    required this.padding,
    required this.spacing,
    required this.columns,
  });

  /// 获取设备类型描述
  String get deviceType {
    if (isExtraSmall) return 'extra-small';
    if (isSmall) return 'small';
    if (isMobile) return 'mobile';
    if (isTablet) return 'tablet';
    if (isLarge) return 'large';
    if (isExtraLarge) return 'extra-large';
    return 'desktop';
  }

  /// 是否为移动设备（包含小屏）
  bool get isMobileDevice => isExtraSmall || isSmall || isMobile;

  /// 是否为大屏设备
  bool get isLargeDevice => isDesktop || isLarge || isExtraLarge;

  /// 获取适配的字体大小
  double get adaptiveFontSize {
    if (isExtraSmall) return 12.0;
    if (isSmall) return 14.0;
    if (isMobile) return 16.0;
    if (isTablet) return 18.0;
    if (isDesktop) return 16.0;
    if (isLarge) return 18.0;
    return 20.0; // isExtraLarge
  }

  /// 获取适配的图标大小
  double get adaptiveIconSize {
    if (isExtraSmall) return 16.0;
    if (isSmall) return 20.0;
    if (isMobile) return 24.0;
    if (isTablet) return 28.0;
    if (isDesktop) return 24.0;
    if (isLarge) return 28.0;
    return 32.0; // isExtraLarge
  }

  /// 获取适配的按钮高度
  double get adaptiveButtonHeight {
    if (isExtraSmall) return 36.0;
    if (isSmall) return 40.0;
    if (isMobile) return 44.0;
    if (isTablet) return 48.0;
    if (isDesktop) return 44.0;
    if (isLarge) return 48.0;
    return 52.0; // isExtraLarge
  }

  /// 获取适配的圆角半径
  double get adaptiveBorderRadius {
    if (isMobileDevice) return 8.0;
    if (isTablet) return 10.0;
    return 12.0; // desktop and larger
  }
}

/// 响应式布局工具 - 统一屏幕断点判断和布局配置
/// 
/// 这个类提供了统一的响应式布局工具，包括屏幕断点判断、
/// 布局配置计算、组件尺寸适配等功能。
class ResponsiveHelper {
  
  /// ========== 基础断点判断方法 ==========

  /// 判断是否为超小屏幕
  static bool isExtraSmall(BuildContext context) {
    return MediaQuery.of(context).size.width < AppConstants.EXTRA_SMALL_BREAKPOINT;
  }

  /// 判断是否为小屏幕
  static bool isSmall(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.EXTRA_SMALL_BREAKPOINT && width < AppConstants.SMALL_BREAKPOINT;
  }

  /// 判断是否为移动端
  static bool isMobile(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.SMALL_BREAKPOINT && width < AppConstants.MOBILE_BREAKPOINT;
  }

  /// 判断是否为平板
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.MOBILE_BREAKPOINT && width < AppConstants.TABLET_BREAKPOINT;
  }

  /// 判断是否为桌面端
  static bool isDesktop(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.TABLET_BREAKPOINT && width < AppConstants.LARGE_BREAKPOINT;
  }

  /// 判断是否为大屏
  static bool isLarge(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.LARGE_BREAKPOINT && width < AppConstants.EXTRA_LARGE_BREAKPOINT;
  }

  /// 判断是否为超大屏
  static bool isExtraLarge(BuildContext context) {
    return MediaQuery.of(context).size.width >= AppConstants.EXTRA_LARGE_BREAKPOINT;
  }

  /// ========== 综合配置获取 ==========

  /// 获取完整的响应式配置
  static ResponsiveConfig getConfig(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final height = size.height;

    final isExtraSmall = width < AppConstants.EXTRA_SMALL_BREAKPOINT;
    final isSmall = width >= AppConstants.EXTRA_SMALL_BREAKPOINT && width < AppConstants.SMALL_BREAKPOINT;
    final isMobile = width >= AppConstants.SMALL_BREAKPOINT && width < AppConstants.MOBILE_BREAKPOINT;
    final isTablet = width >= AppConstants.MOBILE_BREAKPOINT && width < AppConstants.TABLET_BREAKPOINT;
    final isDesktop = width >= AppConstants.TABLET_BREAKPOINT && width < AppConstants.LARGE_BREAKPOINT;
    final isLarge = width >= AppConstants.LARGE_BREAKPOINT && width < AppConstants.EXTRA_LARGE_BREAKPOINT;
    final isExtraLarge = width >= AppConstants.EXTRA_LARGE_BREAKPOINT;

    return ResponsiveConfig(
      isExtraSmall: isExtraSmall,
      isSmall: isSmall,
      isMobile: isMobile,
      isTablet: isTablet,
      isDesktop: isDesktop,
      isLarge: isLarge,
      isExtraLarge: isExtraLarge,
      screenWidth: width,
      screenHeight: height,
      padding: _calculatePadding(width),
      spacing: _calculateSpacing(width),
      columns: _calculateColumns(width),
    );
  }

  /// ========== 布局计算方法 ==========

  /// 计算内边距
  static EdgeInsets _calculatePadding(double width) {
    if (width < AppConstants.EXTRA_SMALL_BREAKPOINT) {
      return const EdgeInsets.all(AppConstants.SPACING_XS);
    } else if (width < AppConstants.SMALL_BREAKPOINT) {
      return const EdgeInsets.all(AppConstants.SPACING_SM);
    } else if (width < AppConstants.MOBILE_BREAKPOINT) {
      return const EdgeInsets.all(AppConstants.SPACING_MD);
    } else if (width < AppConstants.TABLET_BREAKPOINT) {
      return const EdgeInsets.all(AppConstants.SPACING_LG);
    } else {
      return const EdgeInsets.all(AppConstants.SPACING_XL);
    }
  }

  /// 计算间距
  static double _calculateSpacing(double width) {
    if (width < AppConstants.EXTRA_SMALL_BREAKPOINT) {
      return AppConstants.SPACING_XS;
    } else if (width < AppConstants.SMALL_BREAKPOINT) {
      return AppConstants.SPACING_SM;
    } else if (width < AppConstants.MOBILE_BREAKPOINT) {
      return AppConstants.SPACING_MD;
    } else if (width < AppConstants.TABLET_BREAKPOINT) {
      return AppConstants.SPACING_LG;
    } else {
      return AppConstants.SPACING_XL;
    }
  }

  /// 计算列数
  static int _calculateColumns(double width) {
    if (width < AppConstants.EXTRA_SMALL_BREAKPOINT) {
      return 1;
    } else if (width < AppConstants.SMALL_BREAKPOINT) {
      return 2;
    } else if (width < AppConstants.MOBILE_BREAKPOINT) {
      return 2;
    } else if (width < AppConstants.TABLET_BREAKPOINT) {
      return 3;
    } else if (width < AppConstants.LARGE_BREAKPOINT) {
      return 4;
    } else {
      return 5;
    }
  }

  /// ========== 组件尺寸适配 ==========

  /// 获取滑块输入框宽度
  static double getSliderInputWidth(BuildContext context) {
    final config = getConfig(context);
    if (config.isMobileDevice) {
      if (config.isExtraSmall) {
        return AppConstants.SLIDER_INPUT_WIDTH_SMALL;
      }
      return AppConstants.SLIDER_INPUT_WIDTH_MOBILE;
    }
    return AppConstants.SLIDER_INPUT_WIDTH_DESKTOP;
  }

  /// 获取侧边栏宽度
  static double getSidebarWidth(BuildContext context) {
    final config = getConfig(context);
    return config.isMobileDevice 
        ? AppConstants.MOBILE_SIDEBAR_WIDTH 
        : AppConstants.DESKTOP_SIDEBAR_WIDTH;
  }

  /// 获取卡片间距
  static double getCardSpacing(BuildContext context) {
    final config = getConfig(context);
    if (config.isExtraSmall) {
      return AppConstants.SPACING_XS;
    } else if (config.isMobileDevice) {
      return AppConstants.SPACING_SM;
    } else {
      return AppConstants.SPACING_MD;
    }
  }

  /// 获取字体大小
  static double getFontSize(BuildContext context, String sizeType) {
    final config = getConfig(context);
    final scaleFactor = config.isMobileDevice ? 0.9 : 1.0;

    switch (sizeType) {
      case 'xs':
        return AppConstants.FONT_SIZE_XS * scaleFactor;
      case 'sm':
        return AppConstants.FONT_SIZE_SM * scaleFactor;
      case 'md':
        return AppConstants.FONT_SIZE_MD * scaleFactor;
      case 'lg':
        return AppConstants.FONT_SIZE_LG * scaleFactor;
      case 'xl':
        return AppConstants.FONT_SIZE_XL * scaleFactor;
      case 'xxl':
        return AppConstants.FONT_SIZE_XXL * scaleFactor;
      default:
        return AppConstants.FONT_SIZE_MD * scaleFactor;
    }
  }

  /// ========== 布局组件 ==========

  /// 响应式容器
  static Widget responsiveContainer({
    required BuildContext context,
    required Widget child,
    EdgeInsets? padding,
    double? maxWidth,
  }) {
    final config = getConfig(context);
    
    return Container(
      width: double.infinity,
      constraints: maxWidth != null ? BoxConstraints(maxWidth: maxWidth) : null,
      padding: padding ?? config.padding,
      child: child,
    );
  }

  /// 响应式行布局
  static Widget responsiveRow({
    required BuildContext context,
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    bool forceColumn = false,
  }) {
    final config = getConfig(context);
    
    if (forceColumn || config.isMobileDevice) {
      return Column(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        children: children,
      );
    } else {
      return Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        children: children,
      );
    }
  }

  /// 响应式网格布局
  static Widget responsiveGrid({
    required BuildContext context,
    required List<Widget> children,
    int? forceColumns,
    double? spacing,
    double? runSpacing,
  }) {
    final config = getConfig(context);
    final columns = forceColumns ?? config.columns;
    final itemSpacing = spacing ?? config.spacing;
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: itemSpacing,
        mainAxisSpacing: runSpacing ?? itemSpacing,
        childAspectRatio: 1.0,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }

  /// ========== 工具方法 ==========

  /// 获取屏幕信息摘要
  static Map<String, dynamic> getScreenInfo(BuildContext context) {
    final config = getConfig(context);
    final mediaQuery = MediaQuery.of(context);
    
    return {
      'deviceType': config.deviceType,
      'screenWidth': config.screenWidth,
      'screenHeight': config.screenHeight,
      'devicePixelRatio': mediaQuery.devicePixelRatio,
      'textScaleFactor': mediaQuery.textScaleFactor,
      'columns': config.columns,
      'spacing': config.spacing,
      'isMobileDevice': config.isMobileDevice,
      'isLargeDevice': config.isLargeDevice,
    };
  }

  /// 根据屏幕尺寸选择值
  static T selectByScreenSize<T>(
    BuildContext context, {
    T? extraSmall,
    T? small,
    T? mobile,
    T? tablet,
    T? desktop,
    T? large,
    T? extraLarge,
    required T defaultValue,
  }) {
    final config = getConfig(context);
    
    if (config.isExtraSmall && extraSmall != null) return extraSmall;
    if (config.isSmall && small != null) return small;
    if (config.isMobile && mobile != null) return mobile;
    if (config.isTablet && tablet != null) return tablet;
    if (config.isDesktop && desktop != null) return desktop;
    if (config.isLarge && large != null) return large;
    if (config.isExtraLarge && extraLarge != null) return extraLarge;
    
    return defaultValue;
  }

  /// 创建响应式断点监听器
  static Widget responsiveBuilder({
    required BuildContext context,
    required Widget Function(BuildContext context, ResponsiveConfig config) builder,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final config = getConfig(context);
        return builder(context, config);
      },
    );
  }

  /// 创建响应式组件包装器
  static Widget responsiveWrapper({
    required BuildContext context,
    required Widget child,
    double? maxWidth,
    bool centerContent = true,
  }) {
    final config = getConfig(context);

    Widget content = Container(
      width: double.infinity,
      constraints: maxWidth != null
          ? BoxConstraints(maxWidth: maxWidth)
          : null,
      padding: config.padding,
      child: child,
    );

    if (centerContent && config.isLargeDevice) {
      content = Center(child: content);
    }

    return content;
  }

  /// 创建自适应卡片
  static Widget adaptiveCard({
    required BuildContext context,
    required Widget child,
    EdgeInsets? padding,
    double? elevation,
  }) {
    final config = getConfig(context);

    return Card(
      elevation: elevation ?? (config.isMobileDevice ? 2.0 : 4.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(config.adaptiveBorderRadius),
      ),
      child: Padding(
        padding: padding ?? config.padding,
        child: child,
      ),
    );
  }

  /// 创建自适应按钮
  static Widget adaptiveButton({
    required BuildContext context,
    required String text,
    required VoidCallback? onPressed,
    bool isPrimary = true,
    IconData? icon,
  }) {
    final config = getConfig(context);

    Widget buttonChild = Text(
      text,
      style: TextStyle(fontSize: config.adaptiveFontSize),
    );

    if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: config.adaptiveIconSize),
          SizedBox(width: config.spacing * 0.5),
          buttonChild,
        ],
      );
    }

    return SizedBox(
      height: config.adaptiveButtonHeight,
      child: isPrimary
          ? ElevatedButton(
              onPressed: onPressed,
              child: buttonChild,
            )
          : OutlinedButton(
              onPressed: onPressed,
              child: buttonChild,
            ),
    );
  }

  /// 创建性能优化的列表项
  static Widget optimizedListItem({
    required BuildContext context,
    required Widget child,
    bool addRepaintBoundary = true,
    bool addAutomaticKeepAlive = false,
  }) {
    Widget item = child;

    if (addRepaintBoundary) {
      item = RepaintBoundary(child: item);
    }

    if (addAutomaticKeepAlive) {
      item = AutomaticKeepAlive(
        child: item,
      );
    }

    return item;
  }

  /// 创建延迟加载的图片
  static Widget lazyImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ??
            Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            const Icon(Icons.error, color: Colors.grey);
      },
    );
  }

  /// 创建缓存的网络图片
  static Widget cachedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    // 这里可以集成cached_network_image包
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(Icons.image, color: Colors.grey),
    );
  }
}
