// ignore_for_file: avoid_print

import 'package:flutter/foundation.dart';

/// 页头模型 - 管理页头相关状态和数据
class HeaderModel extends ChangeNotifier {
  // 常量配置
  static const String _defaultGameId = 'csgo2';
  static const Duration _refreshCooldown = Duration(seconds: 2);
  
  // 游戏选择状态
  String _selectedGameId = _defaultGameId;
  String _selectedGameLabel = _defaultGameId;
  String _selectedGameIconPath = '';
  
  // 用户信息状态
  String _username = '';
  bool _isPro = false;
  
  // 系统状态
  bool _isRefreshing = false;
  DateTime? _lastRefreshTime;
  bool _isConnected = false;
  String _connectionStatus = '未连接';
  
  // 服务器状态
  bool _dbStatus = false;
  bool _inferenceStatus = false;
  bool _cardKeyStatus = false;
  bool _keyMouseStatus = false;
  DateTime? _lastStatusUpdate;
  
  // 版本信息
  String _currentVersion = '';
  String _latestVersion = '';
  
  // 性能数据
  int _frameRate = 0;
  int _width = 0;
  int _height = 0;
  
  // 心跳状态
  DateTime? _lastHeartbeat;
  bool _heartbeatHealthy = false;
  
  // Getters
  String get selectedGameId => _selectedGameId;
  String get selectedGameLabel => _selectedGameLabel;
  String get selectedGameIconPath => _selectedGameIconPath;
  String get username => _username;
  bool get isPro => _isPro;
  bool get isRefreshing => _isRefreshing;
  DateTime? get lastRefreshTime => _lastRefreshTime;
  bool get isConnected => _isConnected;
  String get connectionStatus => _connectionStatus;
  bool get dbStatus => _dbStatus;
  bool get inferenceStatus => _inferenceStatus;
  bool get cardKeyStatus => _cardKeyStatus;
  bool get keyMouseStatus => _keyMouseStatus;
  DateTime? get lastStatusUpdate => _lastStatusUpdate;
  String get currentVersion => _currentVersion;
  String get latestVersion => _latestVersion;
  int get frameRate => _frameRate;
  int get width => _width;
  int get height => _height;
  DateTime? get lastHeartbeat => _lastHeartbeat;
  bool get heartbeatHealthy => _heartbeatHealthy;
  
  // 检查是否可以刷新
  bool get canRefresh {
    if (_lastRefreshTime == null) return true;
    return DateTime.now().difference(_lastRefreshTime!) > _refreshCooldown;
  }
  
  // 检查是否有新版本
  bool get hasNewVersion {
    if (_currentVersion.isEmpty || _latestVersion.isEmpty) return false;
    return _compareVersions(_currentVersion, _latestVersion) < 0;
  }
  
  /// 比较版本号
  int _compareVersions(String version1, String version2) {
    final v1Parts = version1.split('.').map(int.parse).toList();
    final v2Parts = version2.split('.').map(int.parse).toList();
    
    final maxLength = v1Parts.length > v2Parts.length ? v1Parts.length : v2Parts.length;
    
    for (int i = 0; i < maxLength; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;
      
      if (v1Part < v2Part) return -1;
      if (v1Part > v2Part) return 1;
    }
    
    return 0;
  }
  
  /// 更新游戏选择
  void updateGameSelection({
    required String gameId,
    required String gameLabel,
    required String iconPath,
  }) {
    if (_selectedGameId == gameId) return;
    
    _selectedGameId = gameId;
    _selectedGameLabel = gameLabel;
    _selectedGameIconPath = iconPath;
    notifyListeners();
  }
  
  /// 更新用户信息
  void updateUserInfo({
    required String username,
    required bool isPro,
  }) {
    bool hasChanged = false;
    
    if (_username != username) {
      _username = username;
      hasChanged = true;
    }
    
    if (_isPro != isPro) {
      _isPro = isPro;
      hasChanged = true;
    }
    
    if (hasChanged) {
      notifyListeners();
    }
  }
  
  /// 更新连接状态
  void updateConnectionStatus({
    required bool isConnected,
    required String status,
  }) {
    bool hasChanged = false;
    
    if (_isConnected != isConnected) {
      _isConnected = isConnected;
      hasChanged = true;
    }
    
    if (_connectionStatus != status) {
      _connectionStatus = status;
      hasChanged = true;
    }
    
    if (hasChanged) {
      notifyListeners();
    }
  }
  
  /// 更新服务器状态
  void updateServerStatus({
    required bool dbStatus,
    required bool inferenceStatus,
    required bool cardKeyStatus,
    required bool keyMouseStatus,
  }) {
    bool hasChanged = false;
    
    if (_dbStatus != dbStatus) {
      _dbStatus = dbStatus;
      hasChanged = true;
    }
    
    if (_inferenceStatus != inferenceStatus) {
      _inferenceStatus = inferenceStatus;
      hasChanged = true;
    }
    
    if (_cardKeyStatus != cardKeyStatus) {
      _cardKeyStatus = cardKeyStatus;
      hasChanged = true;
    }
    
    if (_keyMouseStatus != keyMouseStatus) {
      _keyMouseStatus = keyMouseStatus;
      hasChanged = true;
    }
    
    if (hasChanged) {
      _lastStatusUpdate = DateTime.now();
      notifyListeners();
    }
  }
  
  /// 更新心跳状态
  void updateHeartbeat() {
    _lastHeartbeat = DateTime.now();
    _heartbeatHealthy = true;
    notifyListeners();
  }
  
  /// 从JSON数据更新状态（用于同步StatusBar数据）
  void updateFromJson(Map<String, dynamic> json) {
    print('HeaderModel: updateFromJson被调用，数据: $json');
    print('HeaderModel: 当前状态 - db=$_dbStatus, inference=$_inferenceStatus, cardKey=$_cardKeyStatus, keyMouse=$_keyMouseStatus, frameRate=$_frameRate, resolution=${_width}x$_height');
    
    bool hasChanged = false;
    
    // 同步服务器状态
    if (json.containsKey('dbStatus')) {
      final bool newValue = json['dbStatus'] as bool;
      if (_dbStatus != newValue) {
        print('HeaderModel: 数据库状态从 $_dbStatus 变为 $newValue');
        _dbStatus = newValue;
        hasChanged = true;
      } else {
        print('HeaderModel: 数据库状态保持 $_dbStatus');
      }
    }
    
    if (json.containsKey('inferenceStatus')) {
      final bool newValue = json['inferenceStatus'] as bool;
      if (_inferenceStatus != newValue) {
        print('HeaderModel: 推理状态从 $_inferenceStatus 变为 $newValue');
        _inferenceStatus = newValue;
        hasChanged = true;
      } else {
        print('HeaderModel: 推理状态保持 $_inferenceStatus');
      }
    }
    
    if (json.containsKey('cardKeyStatus')) {
      final bool newValue = json['cardKeyStatus'] as bool;
      if (_cardKeyStatus != newValue) {
        print('HeaderModel: 卡密状态从 $_cardKeyStatus 变为 $newValue');
        _cardKeyStatus = newValue;
        hasChanged = true;
      } else {
        print('HeaderModel: 卡密状态保持 $_cardKeyStatus');
      }
    }
    
    if (json.containsKey('keyMouseStatus')) {
      final bool newValue = json['keyMouseStatus'] as bool;
      if (_keyMouseStatus != newValue) {
        print('HeaderModel: 键鼠状态从 $_keyMouseStatus 变为 $newValue');
        _keyMouseStatus = newValue;
        hasChanged = true;
      } else {
        print('HeaderModel: 键鼠状态保持 $_keyMouseStatus');
      }
    }
    
    // 更新版本信息
    if (json.containsKey('currentVersion')) {
      final String newValue = json['currentVersion'] as String;
      if (_currentVersion != newValue) {
        print('HeaderModel: 当前版本从 $_currentVersion 变为 $newValue');
        _currentVersion = newValue;
        hasChanged = true;
      } else {
        print('HeaderModel: 当前版本保持 $_currentVersion');
      }
    }
    
    if (json.containsKey('latestVersion')) {
      final String newValue = json['latestVersion'] as String;
      if (_latestVersion != newValue) {
        print('HeaderModel: 最新版本从 $_latestVersion 变为 $newValue');
        _latestVersion = newValue;
        hasChanged = true;
      } else {
        print('HeaderModel: 最新版本保持 $_latestVersion');
      }
    }
    
    // 更新性能数据
    if (json.containsKey('frameRate')) {
      final int newValue = json['frameRate'] as int;
      if (_frameRate != newValue) {
        print('HeaderModel: 帧率从 $_frameRate 变为 $newValue');
        _frameRate = newValue;
        hasChanged = true;
      } else {
        print('HeaderModel: 帧率保持 $_frameRate');
      }
    }
    
    if (json.containsKey('width')) {
      final int newValue = json['width'] as int;
      if (_width != newValue) {
        print('HeaderModel: 宽度从 $_width 变为 $newValue');
        _width = newValue;
        hasChanged = true;
      } else {
        print('HeaderModel: 宽度保持 $_width');
      }
    }
    
    if (json.containsKey('height')) {
      final int newValue = json['height'] as int;
      if (_height != newValue) {
        print('HeaderModel: 高度从 $_height 变为 $newValue');
        _height = newValue;
        hasChanged = true;
      } else {
        print('HeaderModel: 高度保持 $_height');
      }
    }
    
    // 更新心跳状态
    updateHeartbeat();
    
    // 强制通知监听器，确保UI更新
    // 即使状态值没有变化，也可能需要刷新UI显示
    _lastStatusUpdate = DateTime.now();
    print('HeaderModel: 强制通知监听器以确保UI更新 (hasChanged=$hasChanged)');
    notifyListeners();
  }
  
  /// 检查心跳健康状态
  void checkHeartbeatHealth() {
    if (_lastHeartbeat == null) {
      _heartbeatHealthy = false;
    } else {
      final timeDiff = DateTime.now().difference(_lastHeartbeat!);
      _heartbeatHealthy = timeDiff.inSeconds <= 20;
    }
    notifyListeners();
  }
  
  /// 开始刷新
  void startRefresh() {
    if (!canRefresh) return;
    
    _isRefreshing = true;
    _lastRefreshTime = DateTime.now();
    notifyListeners();
  }
  
  /// 完成刷新
  void completeRefresh() {
    _isRefreshing = false;
    notifyListeners();
  }
  
  /// 重置状态
  void resetStatus() {
    _isConnected = false;
    _connectionStatus = '未连接';
    _dbStatus = false;
    _inferenceStatus = false;
    _cardKeyStatus = false;
    _keyMouseStatus = false;
    _lastStatusUpdate = null;
    _currentVersion = '';
    _latestVersion = '';
    _frameRate = 0;
    _width = 0;
    _height = 0;
    _lastHeartbeat = null;
    _heartbeatHealthy = false;
    notifyListeners();
  }
  
  /// 获取状态摘要
  Map<String, dynamic> getStatusSummary() {
    return {
      'isConnected': _isConnected,
      'connectionStatus': _connectionStatus,
      'dbStatus': _dbStatus,
      'inferenceStatus': _inferenceStatus,
      'cardKeyStatus': _cardKeyStatus,
      'keyMouseStatus': _keyMouseStatus,
      'heartbeatHealthy': _heartbeatHealthy,
      'lastStatusUpdate': _lastStatusUpdate?.toIso8601String(),
      'lastHeartbeat': _lastHeartbeat?.toIso8601String(),
      'frameRate': _frameRate,
      'width': _width,
      'height': _height,
      'currentVersion': _currentVersion,
      'latestVersion': _latestVersion,
      'hasNewVersion': hasNewVersion,
    };
  }
} 