// ignore_for_file: unnecessary_this, unnecessary_brace_in_string_interps, use_build_context_synchronously

import 'dart:convert';
import 'package:flutter/material.dart';
import '../models/sidebar_model.dart'; // 导入侧边栏模型
import '../models/auth_model.dart'; // 导入认证模型
import '../models/game_model.dart'; // 导入游戏模型 
import '../utils/logger.dart'; // 导入日志工具
import '../services/server_service.dart'; // 导入服务器服务
import '../component/message_component.dart'; // 导入消息组件

/// 侧边栏控制器 - 负责管理侧边栏状态和页面切换
/// 
/// 提供页面导航、数据请求、状态同步等功能
class SideController extends ChangeNotifier {
  // 常量配置
  static const String _logTag = 'SideController';
  static const double _defaultSidebarWidth = 250.0;
  static const double _collapsedSidebarWidth = 60.0;

  static const Duration _loadingToastDuration = Duration(seconds: 1);
  static const int _requestDelayMs = 300; // 增加延迟，避免请求过于密集
  
  // 页面ID映射
  static const Map<String, String> _pageActionMap = {
    'home': 'home_read',
    'function': 'function_read',
    'pid': 'pid_read',
    'fov': 'fov_read',
    'aim': 'aim_read',
    'fire': 'fire_read',
    'data_collection': 'data_collection_read',
    // 注意：config_management 不在此映射中，因为它是纯前端功能
  };
  
  static const Map<String, String> _pageNameMap = {
    'home': '首页',
    'function': '功能设置',
    'pid': 'PID设置',
    'fov': '视野设置',
    'aim': '瞄准设置',
    'fire': '射击设置',
    'data_collection': '数据收集',
    'config_management': '配置管理',
  };
  
  // 状态变量
  String _activePage = 'home';
  bool _isExpanded = true;
  double _sidebarWidth = _defaultSidebarWidth;
  bool _isDisposed = false;
  
  // 加载提示管理
  VoidCallback? _currentLoadingDismiss;
  int _pendingRequests = 0;
  int _completedRequests = 0;
  BuildContext? _savedContext;
  
  // 请求状态跟踪
  final Set<String> _processedRequests = <String>{};
  
  // 依赖注入
  final ServerService _serverService;
  final SidebarModel _sidebarModel;
  final AuthModel _authModel;
  final GameModel _gameModel;
  final Logger _logger = Logger();
  
  // Getters
  String get activePage => _activePage;
  bool get isExpanded => _isExpanded;
  double get sidebarWidth => _isExpanded ? _sidebarWidth : _collapsedSidebarWidth;
  List<String> get allPageIds => [..._pageActionMap.keys, 'config_management'];

  /// 获取需要服务器数据的页面ID列表（排除纯前端页面）
  List<String> get _serverDataPageIds => _pageActionMap.keys.toList();
  
  /// 构造函数
  SideController({
    required ServerService serverService,
    required SidebarModel sidebarModel,
    required AuthModel authModel,
    required GameModel gameModel,
  }) : _serverService = serverService,
       _sidebarModel = sidebarModel,
       _authModel = authModel,
       _gameModel = gameModel {
    _initializeController();
  }
  
  /// 初始化控制器
  void _initializeController() {
    _logger.i(_logTag, '创建新的SideController实例 [${identityHashCode(this)}]');
    
    _activePage = _sidebarModel.activeItemId;
    _isExpanded = _sidebarModel.expanded;
    _sidebarWidth = _sidebarModel.expandedWidth;
    
    // 注册WebSocket消息监听器
    _serverService.addMessageListener(_handleServerMessage);
    _logger.i(_logTag, '已注册WebSocket消息监听器');
  }
  
  /// 设置当前激活页面
  void setActivePage(String page, BuildContext? context) {
    if (_activePage == page) {
      // 即使是相同页面，也要确保数据是最新的
      _logger.i(_logTag, '页面未变更但强制刷新数据: $page');
      _requestPageData(page, context);
      return;
    }
    
    _logger.i(_logTag, '切换页面: 从 $_activePage 切换到 $page');
    
    _updatePageState(page);
    _requestPageData(page, context);
  }
  
  /// 更新页面状态
  void _updatePageState(String page) {
    _activePage = page;
    _sidebarModel.setActiveItem(page);
    notifyListeners();
  }
  
  /// 请求页面数据
  void _requestPageData(String pageId, BuildContext? context) {
    _logger.i(_logTag, '请求页面数据: $pageId');

    // 配置管理是纯前端功能，不需要向服务器请求数据
    if (pageId == 'config_management') {
      _logger.i(_logTag, '配置管理页面无需服务器数据，跳过请求');
      return;
    }

    if (!_isServerConnected(context)) {
      _logger.w(_logTag, '服务器未连接，跳过数据请求');
      return;
    }

    final action = _getActionForPage(pageId);
    if (action == null) return;

    final request = _buildPageRequest(action);
    _sendRequest(request);
    _showLoadingMessage(context, pageId);
  }
  
  /// 请求页面数据（带进度跟踪）
  void _requestPageDataWithTracking(String pageId, BuildContext? context) {
    _logger.i(_logTag, '请求页面数据（带跟踪）: $pageId');

    // 配置管理是纯前端功能，不需要向服务器请求数据
    if (pageId == 'config_management') {
      _logger.i(_logTag, '配置管理页面无需服务器数据，跳过请求');
      _handleRequestComplete(context, pageId, true, source: 'frontend_only');
      return;
    }

    if (!_isServerConnected(context)) {
      _logger.w(_logTag, '服务器未连接，跳过数据请求');
      _handleRequestComplete(context, pageId, false, source: 'connection_error');
      return;
    }

    final action = _getActionForPage(pageId);
    if (action == null) {
      _handleRequestComplete(context, pageId, false, source: 'invalid_action');
      return;
    }
    
    final request = _buildPageRequest(action);

    _logger.i(_logTag, '📤 准备发送请求', {
      'pageId': pageId,
      'action': action,
      'request': request,
      'serverConnected': _serverService.isConnected,
    });

    _sendRequest(request);

    // 设置单个请求的超时处理 - 避免与全局超时冲突
    Future.delayed(const Duration(seconds: 8), () {
      if (!_isDisposed && !_processedRequests.contains(pageId)) {
        _logger.w(_logTag, '⏰ 单个请求超时', {
          'pageId': pageId,
          'action': action,
          'processedRequests': _processedRequests.toList(),
          'isDisposed': _isDisposed,
        });
        _handleRequestComplete(context, pageId, false, source: 'timeout');
      }
    });

    _logger.i(_logTag, '✅ 已发送请求: $pageId, 等待响应...');
  }
  
  /// 检查服务器连接状态
  bool _isServerConnected(BuildContext? context) {
    if (_serverService.isConnected) return true;

    // 如果用户主动断开，不尝试自动重连
    if (_serverService.isUserDisconnected) {
      _logger.i(_logTag, '用户主动断开连接，跳过自动重连');
      _showWarningMessage(context, '服务器已断开连接，请点击连接按钮重新连接');
      return false;
    }

    _logger.w(_logTag, 'WebSocket未连接，尝试自动重连', {
      'serverAddress': _serverService.serverAddress,
      'serverPort': _serverService.serverPort,
    });

    // 如果有服务器信息，尝试自动重连
    if (_serverService.serverAddress.isNotEmpty && _serverService.serverPort.isNotEmpty) {
      _logger.i(_logTag, '检测到连接断开，尝试自动重连');
      _attemptAutoReconnect(context);
      _showWarningMessage(context, '连接已断开，正在尝试重连...');
    } else {
      _showWarningMessage(context, '服务器未连接，请先在登录页面连接服务器');
    }

    return false;
  }

  /// 尝试自动重连
  void _attemptAutoReconnect(BuildContext? context) {
    if (context == null) return;

    // 异步重连，避免阻塞UI
    Future.delayed(const Duration(milliseconds: 100), () async {
      try {
        await _serverService.handleConnectService(
          context,
          _serverService.serverAddress,
          _serverService.serverPort,
          '', // 使用空token，因为已经登录过了
        );

        if (_serverService.isConnected) {
          _logger.i(_logTag, '自动重连成功');
          // 使用现有的消息显示方法
          MessageComponent.showIconToast(
            context: context,
            message: '连接已恢复',
            type: MessageType.success,
            duration: const Duration(seconds: 2),
          );
        } else {
          _logger.w(_logTag, '自动重连失败');
        }
      } catch (e) {
        _logger.e(_logTag, '自动重连异常', e.toString());
      }
    });
  }
  
  /// 获取页面对应的动作
  String? _getActionForPage(String pageId) {
    final action = _pageActionMap[pageId];
    if (action == null) {
      _logger.w(_logTag, '未知页面ID: $pageId，无法发送配置请求');
    }
    return action;
  }
  
  /// 构建页面请求
  Map<String, dynamic> _buildPageRequest(String action) {
    return {
      'action': action,
      'content': _buildRequestContent(),
    };
  }
  
  /// 构建请求内容
  Map<String, dynamic> _buildRequestContent() {
    final content = {
      'username': _authModel.username,
      'gameName': _gameModel.currentGame,
      'cardKey': _gameModel.cardKey,
      'token': _authModel.token, // 添加token字段用于身份验证
      'updatedAt': DateTime.now().toIso8601String(),
    };
    
    final cardKeyDisplay = content['cardKey']?.toString();
    final cardKeyLog = cardKeyDisplay != null && cardKeyDisplay.length > 8 
        ? '${cardKeyDisplay.substring(0, 8)}...' 
        : cardKeyDisplay ?? 'null';
    
    _logger.i(_logTag, '🔍 构建请求内容详情', {
      'gameName': content['gameName'],
      'username': content['username'],
      'cardKey': cardKeyLog,
      'GameModel实例': identityHashCode(_gameModel),
      '调用来源': StackTrace.current.toString().split('\n').take(3).join(' -> ')
    });
    
    return content;
  }
  
  /// 发送请求
  void _sendRequest(Map<String, dynamic> request) {
    if (_isDisposed) {
      _logger.w(_logTag, '控制器已销毁，忽略请求');
      return;
    }
    
    try {
      final jsonMessage = jsonEncode(request);
      _serverService.sendMessage(jsonMessage);
      final action = request['action'];
      _logger.i(_logTag, '已发送请求: $action');
    } catch (e) {
      _logger.e(_logTag, '发送请求失败', e.toString());
    }
  }
  
  /// 显示加载消息
  void _showLoadingMessage(BuildContext? context, String pageId) {
    if (context == null) return;
    
    final pageName = _getPageName(pageId);
    
    // 为功能页面提供更详细的提示
    if (pageId == 'function') {
      // 功能页面的加载提示由FunctionController内部管理
      // 这里只显示一个简短的切换提示
      MessageComponent.showIconToast(
        context: context,
        message: '切换到${pageName}',
        type: MessageType.info,
        duration: const Duration(milliseconds: 800),
      );
    } else {
      MessageComponent.showIconToast(
        context: context,
        message: '正在加载${pageName}数据...',
        type: MessageType.info,
        duration: _loadingToastDuration,
      );
    }
  }
  
  /// 显示警告消息
  void _showWarningMessage(BuildContext? context, String message) {
    if (context == null) return;
    
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.warning,
    );
  }
  

  
  /// 获取页面名称
  String _getPageName(String pageId) {
    return _pageNameMap[pageId] ?? '页面';
  }
  
  /// 显示持续的加载提示
  void _showPersistentLoadingMessage(BuildContext? context, String message) {
    if (context == null) return;
    
    // 先关闭之前的提示
    _dismissCurrentLoadingMessage();
    
    // 显示新的持续提示
    _currentLoadingDismiss = MessageComponent.showPersistentLoading(
      context: context,
      message: message,
      position: MessagePosition.center,
    );
    
    _logger.i(_logTag, '显示持续加载提示: $message');
  }
  
  /// 关闭当前的加载提示
  void _dismissCurrentLoadingMessage() {
    if (_currentLoadingDismiss != null) {
      _currentLoadingDismiss!();
      _currentLoadingDismiss = null;
      _logger.i(_logTag, '关闭当前加载提示');
    }
  }
  
  /// 显示成功消息并关闭加载提示
  void _showSuccessAndDismissLoading(BuildContext? context, String message) {
    _dismissCurrentLoadingMessage();
    
    if (context != null) {
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.success,
        duration: const Duration(seconds: 2),
      );
      _logger.i(_logTag, '显示成功消息: $message');
    }
  }
  
  /// 显示错误消息并关闭加载提示
  void _showErrorAndDismissLoading(BuildContext? context, String message) {
    _dismissCurrentLoadingMessage();
    
    if (context != null) {
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.error,
        duration: const Duration(seconds: 3),
      );
      _logger.i(_logTag, '显示错误消息: $message');
    }
  }
  
  /// 处理请求完成
  void _handleRequestComplete(BuildContext? context, String pageId, bool success, {String source = 'unknown'}) {
    // 检查是否已经处理过这个请求
    if (_processedRequests.contains(pageId)) {
      _logger.i(_logTag, '请求已处理，跳过重复处理: $pageId (来源: $source)');
      return;
    }
    
    // 检查是否还在刷新过程中
    if (_currentLoadingDismiss == null) {
      _logger.i(_logTag, '刷新已结束，忽略延迟响应: $pageId (来源: $source)');
      return;
    }
    
    // 标记为已处理
    _processedRequests.add(pageId);
    _completedRequests++;
    
    final pageName = _getPageName(pageId);
    _logger.i(_logTag, '页面请求完成: $pageName (${success ? '成功' : '失败'}), 进度: $_completedRequests/$_pendingRequests (来源: $source)');
    
    // 更新加载提示内容
    if (_currentLoadingDismiss != null && context != null) {
      final remaining = _pendingRequests - _completedRequests;
      if (remaining > 0) {
        // 更新进度提示
        _showPersistentLoadingMessage(context, '数据刷新中... 已完成 $_completedRequests/$_pendingRequests 个页面');
      } else {
        // 所有请求完成
        final successCount = _completedRequests;
        _showSuccessAndDismissLoading(context, '✅ 数据刷新完成！共处理 $successCount 个页面');
      }
    }
  }
  
  /// 处理服务器消息
  void _handleServerMessage(dynamic message) {
    if (_isDisposed) return;

    try {
      final Map<String, dynamic> responseData = jsonDecode(message.toString());
      final String action = responseData['action'] ?? '';

      _logger.i(_logTag, '收到服务器响应: $action');

      // 只在刷新过程中处理页面数据响应
      if (_currentLoadingDismiss == null) {
        _logger.d(_logTag, '当前不在刷新过程中，忽略消息: $action');
        return;
      }

      // 检查是否是页面数据响应
      for (final entry in _pageActionMap.entries) {
        final pageId = entry.key;
        final expectedAction = '${entry.value}_response';

        if (action == expectedAction) {
          _logger.i(_logTag, '📥 匹配到页面响应', {
            'pageId': pageId,
            'action': action,
            'expectedAction': expectedAction,
            'responseData': responseData,
          });

          final status = responseData['status'];
          final success = (status == 'success' || status == 'ok');

          _logger.i(_logTag, '📊 响应状态分析', {
            'pageId': pageId,
            'status': status,
            'success': success,
            'hasData': responseData.containsKey('data'),
          });

          // 这里需要context，但我们在消息处理中没有context
          // 所以我们需要在刷新时保存context
          _handleRequestComplete(_savedContext, pageId, success, source: 'server_response');
          return;
        }
      }

    } catch (e) {
      _logger.e(_logTag, '处理服务器消息失败: $e');
    }
  }
  
  /// 切换侧边栏展开状态
  void toggleExpanded() {
    _isExpanded = !_isExpanded;
    _sidebarModel.setExpandedState(_isExpanded);
    notifyListeners();
  }
  
  /// 检查页面是否为活动页面
  bool isPageActive(String page) => _activePage == page;
  
  /// 请求页面数据 - 公共接口，保持向后兼容性
  void requestPageData(String pageId, BuildContext? context) {
    _requestPageData(pageId, context);
  }
  
  /// 刷新所有页面数据
  void refreshData(BuildContext? context) {
    _logger.i(_logTag, '开始刷新所有页面数据');
    
    // 检查是否已经在刷新中
    if (_currentLoadingDismiss != null) {
      _logger.w(_logTag, '已有刷新任务在进行中，跳过重复请求');
      return;
    }
    
    // 保存context用于后续响应处理
    _savedContext = context;
    
    if (!_isServerConnected(context)) {
      _logger.w(_logTag, '服务器未连接，无法刷新数据');
      _showErrorAndDismissLoading(context, '服务器未连接，无法刷新数据');
      return;
    }
    
    // 初始化请求计数和状态（只计算需要服务器数据的页面）
    _pendingRequests = _serverDataPageIds.length;
    _completedRequests = 0;
    _processedRequests.clear(); // 清空已处理的请求记录

    // 显示持续加载提示
    _showPersistentLoadingMessage(context, '正在向服务器发送 $_pendingRequests 个页面的数据请求...');

    // 发送各个页面的单独请求
    _sendIndividualPageRequestsWithTracking(context);
    
    // 设置超时处理 - 改进版本，避免过早超时
    Future.delayed(const Duration(seconds: 20), () {
      if (_pendingRequests > _completedRequests && !_isDisposed) {
        final completedCount = _completedRequests;
        final totalCount = _pendingRequests;
        
        _logger.w(_logTag, '刷新请求超时，已完成 $completedCount/$totalCount 个请求');
        
        // 如果有部分完成，显示部分成功的消息
        if (completedCount > 0) {
          _showSuccessAndDismissLoading(_savedContext, '✅ 部分数据刷新完成！已处理 $completedCount/$totalCount 个页面');
        } else {
          _showErrorAndDismissLoading(_savedContext, '❌ 数据刷新超时，请检查网络连接');
        }
      }
    });
    
    notifyListeners();
  }
  

  
  /// 发送单独页面请求（带进度跟踪）
  void _sendIndividualPageRequestsWithTracking(BuildContext? context) {
    _logger.i(_logTag, '开始发送各页面单独请求，共${_serverDataPageIds.length}个页面（排除纯前端页面）');

    for (int i = 0; i < _serverDataPageIds.length; i++) {
      final pageId = _serverDataPageIds[i];
      final delay = Duration(milliseconds: _requestDelayMs * i);

      Future.delayed(delay, () {
        if (!_isDisposed) {
          _logger.i(_logTag, '发送页面请求: $pageId (延迟${delay.inMilliseconds}ms)');
          _requestPageDataWithTracking(pageId, context);
        }
      });
    }
  }
  

  
  /// 刷新特定页面数据
  void refreshPageData(String pageId, BuildContext? context) {
    _logger.i(_logTag, '刷新特定页面数据: $pageId');
    
    if (!_isServerConnected(context)) return;
    
    _requestPageData(pageId, context);
    
    // 如果是当前活动页面，更新状态
    if (pageId == _activePage) {
      notifyListeners();
    }
  }
  
  /// 批量刷新指定页面
  void refreshMultiplePages(List<String> pageIds, BuildContext? context) {
    _logger.i(_logTag, '批量刷新页面: $pageIds');
    
    if (!_isServerConnected(context)) return;
    
    for (int i = 0; i < pageIds.length; i++) {
      final pageId = pageIds[i];
      final delay = Duration(milliseconds: _requestDelayMs * i);
      
      Future.delayed(delay, () {
        if (!_isDisposed) {
          _requestPageData(pageId, null);
        }
      });
    }
    
    notifyListeners();
  }
  
  /// 获取页面状态摘要
  Map<String, dynamic> getPageStatusSummary() {
    return {
      'activePage': _activePage,
      'isExpanded': _isExpanded,
      'sidebarWidth': sidebarWidth,
      'isServerConnected': _serverService.isConnected,
      'allPages': allPageIds,
      'pageCount': allPageIds.length,
      'isDisposed': _isDisposed,
    };
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    
    // 关闭任何正在显示的加载提示
    _dismissCurrentLoadingMessage();
    
    // 移除WebSocket消息监听器
    _serverService.removeMessageListener(_handleServerMessage);
    
    _logger.i(_logTag, '释放SideController资源 [${identityHashCode(this)}]');
    super.dispose();
  }
} 