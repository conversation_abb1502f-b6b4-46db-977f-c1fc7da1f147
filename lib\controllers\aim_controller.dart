// ignore_for_file: use_build_context_synchronously, unused_import, unnecessary_null_comparison, unnecessary_brace_in_string_interps

import 'package:flutter/material.dart';
import 'dart:convert';
import '../models/aim_model.dart';
import '../models/auth_model.dart';
import '../models/game_model.dart';
import '../services/server_service.dart';
import '../component/message_component.dart';

// 新工具类导入
import '../utils/api_request_builder.dart';
import '../utils/error_handler.dart';
import '../utils/storage_manager.dart';
import '../utils/app_constants.dart';
import '../utils/logger.dart';

/// 瞄准控制器，负责管理瞄准参数和相关的业务逻辑
///
/// 重构后使用统一的工具类：
/// - ApiRequestBuilder: 统一请求构建
/// - ErrorHandler: 统一错误处理
/// - StorageManager: 统一数据持久化
/// - AppConstants: 统一常量管理
class AimController extends ChangeNotifier {
  // 使用AppConstants中的日志标签
  static const String _logTag = AppConstants.LOG_TAG_AIM;

  // 依赖的服务和模型
  ServerService? _serverService;
  AuthModel? _authModel;
  AimModel? _aimModel;
  GameModel? _gameModel;

  // API请求构建器
  ApiRequestBuilder? _apiRequestBuilder;

  // 参数是否已修改标记
  bool _isDirty = false;

  // 控制器是否已销毁
  bool _isDisposed = false;

  // WebSocket监听器是否已注册
  bool _isWebSocketListenerRegistered = false;

  // GameModel监听器是否已注册
  bool _isGameModelListenerRegistered = false;

  // 上次游戏变更时间，用于防抖
  DateTime? _lastGameChangeTime;

  // 日志工具
  final Logger _logger = Logger();

  // Getter
  bool get isDirty => _isDirty;
  bool get isInitialized => _serverService != null && _authModel != null && _aimModel != null && _gameModel != null;
  
  // 构造函数 - 使用新的初始化模式
  AimController();

  /// 初始化控制器 - 使用依赖注入
  void initialize(
    ServerService serverService,
    AuthModel authModel,
    AimModel aimModel,
    GameModel gameModel,
  ) {
    // 检查是否已经初始化，避免重复初始化
    if (isInitialized) {
      _logger.w(_logTag, 'AimController已经初始化，跳过重复初始化');
      return;
    }

    _serverService = serverService;
    _authModel = authModel;
    _aimModel = aimModel;
    _gameModel = gameModel;

    // 创建API请求构建器
    _apiRequestBuilder = ApiRequestBuilder.create(_authModel!, _gameModel!);

    // 注册WebSocket消息回调
    _registerWebSocketCallback();

    // 添加GameModel变更监听
    if (!_isGameModelListenerRegistered) {
      _gameModel!.addListener(_handleGameModelChanged);
      _isGameModelListenerRegistered = true;
      _logger.i(_logTag, '已注册GameModel变更监听器');
    }

    // 初始化 - 请求最新的瞄准设置
    _init();

    _logger.i(_logTag, 'AimController初始化完成');
  }
  
  /// 初始化控制器
  void _init() {
    if (!isInitialized) {
      _logger.w(_logTag, '控制器未完全初始化，跳过初始化操作');
      return;
    }

    _logger.i(_logTag, '正在初始化瞄准控制器');

    // 请求最新的瞄准配置
    _requestAimConfig();
  }

  /// 注册WebSocket消息回调
  void _registerWebSocketCallback() {
    if (_serverService == null) {
      _logger.w(_logTag, 'ServerService未初始化，无法注册WebSocket监听器');
      return;
    }

    // 检查是否已经注册，避免重复注册
    if (_isWebSocketListenerRegistered) {
      _logger.w(_logTag, 'WebSocket监听器已注册，跳过重复注册');
      return;
    }

    // 订阅相关的WebSocket事件
    _serverService!.addMessageListener(_handleWebSocketMessage);
    _isWebSocketListenerRegistered = true;
    _logger.i(_logTag, '已注册WebSocket消息监听器');
  }
  
  /// 处理WebSocket消息 - 使用ErrorHandler统一错误处理
  void _handleWebSocketMessage(dynamic message) {
    // 检查控制器是否已被销毁
    if (_isDisposed || !isInitialized) {
      return;
    }

    try {
      // 只处理字符串消息
      if (message is! String) return;

      // 检查空消息
      if (message.isEmpty) {
        _logger.d(_logTag, '收到空消息（可能是心跳回应）');
        return;
      }

      _logger.d(_logTag, '收到WebSocket消息', {'length': message.length});

      // 解析JSON消息
      final Map<String, dynamic> data = jsonDecode(message);

      // 获取消息类型
      final String action = data['action'] ?? '';

      // 只处理与瞄准相关的消息，忽略其他类型
      if (!action.startsWith('aim_')) {
        return;
      }

      // 处理瞄准配置响应
      if (action == 'aim_read_response' || action == 'aim_modify_response' || action == 'aim_read') {
        _logger.i(_logTag, '收到瞄准参数响应', {'action': action});

        _handleAimResponse(data);
      }
    } catch (e, stackTrace) {
      ErrorHandler.handleParseError(
        tag: _logTag,
        operation: '处理WebSocket消息',
        error: e,
        rawData: message.toString(),
      );
      _logger.e(_logTag, '处理WebSocket消息异常', e.toString(), stackTrace);
    }
  }

  /// 处理瞄准响应数据
  void _handleAimResponse(Map<String, dynamic> data) {
    try {
      // 检查响应状态
      final status = data['status'] ?? '';
      if (status == 'success' || status == 'ok') {
        // 提取数据，优先从data字段获取
        Map<String, dynamic>? content;

        // 尝试先从data字段获取完整配置
        if (data['data'] != null && data['data'] is Map) {
          content = Map<String, dynamic>.from(data['data'] as Map);
          _logger.d(_logTag, '从data字段获取数据');
        }
        // 如果data字段不存在，尝试从content字段获取
        else if (data['content'] != null) {
          content = Map<String, dynamic>.from(data['content'] as Map);
          _logger.d(_logTag, '从content字段获取数据');
        }

        if (content != null) {
          // 检查游戏名称
          if (content['gameName'] != null && _gameModel != null) {
            final serverGameName = content['gameName'].toString();
            final currentGameName = _gameModel!.currentGame;

            if (serverGameName != currentGameName) {
              _logger.i(_logTag, '游戏名称不一致', {
                'server': serverGameName,
                'current': currentGameName,
              });
            }
          }

          // 直接调用更新方法
          _updateFromServerResponse(content);
        } else {
          _logger.w(_logTag, '瞄准响应未包含有效内容');
        }
      } else {
        // 处理错误状态
        final errorMessage = data['message'] ?? '未知错误';
        _logger.w(_logTag, '瞄准参数响应状态错误', {
          'status': status,
          'message': errorMessage,
        });
      }
    } catch (e, stackTrace) {
      ErrorHandler.handleError(
        tag: _logTag,
        operation: '处理瞄准响应数据',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }
  
  /// 根据服务器响应更新本地参数
  void _updateFromServerResponse(Map<String, dynamic> content) {
    try {
      // 临时对象保存所有更新值，避免多次通知
      Map<String, dynamic> updatedValues = {};
      
      // 提取所有需要更新的字段
      if (content.containsKey('aimRange')) updatedValues['aimRange'] = _parseDouble(content['aimRange']);
      if (content.containsKey('trackRange')) updatedValues['trackRange'] = _parseDouble(content['trackRange']);
      if (content.containsKey('headHeight')) updatedValues['headHeight'] = _parseDouble(content['headHeight']);
      if (content.containsKey('neckHeight')) updatedValues['neckHeight'] = _parseDouble(content['neckHeight']);
      if (content.containsKey('chestHeight')) updatedValues['chestHeight'] = _parseDouble(content['chestHeight']);
      if (content.containsKey('headRangeX')) updatedValues['headRangeX'] = _parseDouble(content['headRangeX']);
      if (content.containsKey('headRangeY')) updatedValues['headRangeY'] = _parseDouble(content['headRangeY']);
      if (content.containsKey('neckRangeX')) updatedValues['neckRangeX'] = _parseDouble(content['neckRangeX']);
      if (content.containsKey('neckRangeY')) updatedValues['neckRangeY'] = _parseDouble(content['neckRangeY']);
      if (content.containsKey('chestRangeX')) updatedValues['chestRangeX'] = _parseDouble(content['chestRangeX']);
      if (content.containsKey('chestRangeY')) updatedValues['chestRangeY'] = _parseDouble(content['chestRangeY']);
      
      // 记录更新前的值，用于调试 - 使用安全访问
      final beforeValues = {
        'aimRange': _aimModel?.aimRange ?? 0.0,
        'trackRange': _aimModel?.trackRange ?? 0.0,
        'headHeight': _aimModel?.headHeight ?? 0.0,
        'neckHeight': _aimModel?.neckHeight ?? 0.0,
        'chestHeight': _aimModel?.chestHeight ?? 0.0,
        'headRangeX': _aimModel?.headRangeX ?? 0.0,
        'headRangeY': _aimModel?.headRangeY ?? 0.0,
        'neckRangeX': _aimModel?.neckRangeX ?? 0.0,
        'neckRangeY': _aimModel?.neckRangeY ?? 0.0,
        'chestRangeX': _aimModel?.chestRangeX ?? 0.0,
        'chestRangeY': _aimModel?.chestRangeY ?? 0.0,
      };
      
      // 提取用户和游戏信息 - 使用安全访问
      String username = content.containsKey('username') ? content['username'].toString() : (_aimModel?.username ?? '');
      String gameName = content.containsKey('gameName') ? content['gameName'].toString() : (_aimModel?.gameName ?? '');
      
      // 使用调用fromJson来批量更新，这是最直接的方法
      Map<String, dynamic> jsonData = {
        'content': {
          ...updatedValues,
          'username': username,
          'gameName': gameName,
          'createdAt': content['createdAt'] ?? (_aimModel?.createdAt ?? DateTime.now().toIso8601String()),
          'updatedAt': content['updatedAt'] ?? DateTime.now().toIso8601String(),
        }
      };

      // 使用AimModel的fromJson方法一次性更新所有参数并触发通知
      _aimModel?.fromJson(jsonData);
      
      // 记录更新后的值 - 使用安全访问
      final afterValues = {
        'aimRange': _aimModel?.aimRange ?? 0.0,
        'trackRange': _aimModel?.trackRange ?? 0.0,
        'headHeight': _aimModel?.headHeight ?? 0.0,
        'neckHeight': _aimModel?.neckHeight ?? 0.0,
        'chestHeight': _aimModel?.chestHeight ?? 0.0,
        'headRangeX': _aimModel?.headRangeX ?? 0.0,
        'headRangeY': _aimModel?.headRangeY ?? 0.0,
        'neckRangeX': _aimModel?.neckRangeX ?? 0.0,
        'neckRangeY': _aimModel?.neckRangeY ?? 0.0,
        'chestRangeX': _aimModel?.chestRangeX ?? 0.0,
        'chestRangeY': _aimModel?.chestRangeY ?? 0.0,
      };
      
      // 记录哪些值发生了变化
      final changedValues = <String, dynamic>{};
      beforeValues.forEach((key, value) {
        if (value != afterValues[key]) {
          changedValues[key] = '${value} -> ${afterValues[key]}';
        }
      });
      
      // 重置脏标记，表示这些更改来自服务器
      _isDirty = false;
      
      if (changedValues.isNotEmpty) {
        _logger.i(_logTag, '从服务器响应更新瞄准参数成功，以下参数已更新', changedValues);
      } else {
        _logger.i(_logTag, '从服务器响应更新瞄准参数完成，但无参数变化');
      }

      // 确保UI更新，在模型更新之后再通知控制器的监听器
      if (!_isDisposed) {
        // 直接调用notifyListeners() 不再包装在microtask中
        notifyListeners();
        _logger.d(_logTag, 'AimController通知已发送 - 确保UI更新');
        
        // 额外使用一个延迟通知，以确保UI能够更新
        Future.delayed(Duration(milliseconds: 100), () {
          if (!_isDisposed) {
            notifyListeners();
            log.d(_logTag, 'AimController延迟通知已发送 - 确保UI能完全刷新');
          }
        });
      }
    } catch (e) {
      log.e(_logTag, '从服务器响应更新参数出错', e.toString());
    }
  }

  /// 安全解析Double值
  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }
  
  /// 处理游戏模型变更
  void _handleGameModelChanged() {
    if (_isDisposed || !isInitialized) return;

    // 防抖逻辑：如果距离上次变更不足1秒，则忽略
    final now = DateTime.now();
    if (_lastGameChangeTime != null &&
        now.difference(_lastGameChangeTime!).inMilliseconds < 1000) {
      _logger.d(_logTag, '游戏变更过于频繁，忽略此次变更');
      return;
    }
    _lastGameChangeTime = now;

    // 记录游戏变更
    _logger.i(_logTag, '游戏模型已变更', {'currentGame': _gameModel!.currentGame});

    // 请求最新的瞄准配置
    _requestAimConfig();
  }

  /// 请求瞄准参数 - 供UI调用
  Future<void> requestAimParams() async {
    _logger.i(_logTag, '手动请求瞄准参数');
    await _requestAimConfig();
  }

  /// 请求最新的瞄准配置 - 使用ApiRequestBuilder
  Future<void> _requestAimConfig() async {
    if (!isInitialized) {
      _logger.w(_logTag, '控制器未初始化，无法请求配置');
      return;
    }

    if (!_serverService!.isConnected) {
      _logger.w(_logTag, '服务器未连接，无法请求配置');
      return;
    }

    if (!_authModel!.isAuthenticated) {
      _logger.w(_logTag, '用户未认证，无法请求配置');
      return;
    }

    if (_gameModel!.currentGame.isEmpty) {
      _logger.w(_logTag, '游戏名称为空，无法请求配置');
      return;
    }

    await ErrorHandler.wrapWebSocketOperation(
      tag: _logTag,
      operation: '请求瞄准配置',
      webSocketOperation: () async {
        // 使用ApiRequestBuilder构建请求
        final request = _apiRequestBuilder!.buildAimRequest('read', {
          'cardKey': _gameModel!.cardKey,
        });

        // 发送请求
        final jsonStr = jsonEncode(request);
        _serverService!.sendMessage(jsonStr);

        _logger.i(_logTag, '已请求瞄准配置', {
          'username': _authModel!.username,
          'gameName': _gameModel!.currentGame,
          'cardKey': _gameModel!.cardKey.isNotEmpty ? '已设置' : '未设置',
        });

        return true;
      },
    );
  }
  
  // Getters - 转发到AimModel，使用安全访问
  double get aimRange => _aimModel?.aimRange ?? 0.0;
  double get trackRange => _aimModel?.trackRange ?? 0.0;
  double get headHeight => _aimModel?.headHeight ?? 0.0;
  double get neckHeight => _aimModel?.neckHeight ?? 0.0;
  double get chestHeight => _aimModel?.chestHeight ?? 0.0;
  double get headRangeX => _aimModel?.headRangeX ?? 0.0;
  double get headRangeY => _aimModel?.headRangeY ?? 0.0;
  double get neckRangeX => _aimModel?.neckRangeX ?? 0.0;
  double get neckRangeY => _aimModel?.neckRangeY ?? 0.0;
  double get chestRangeX => _aimModel?.chestRangeX ?? 0.0;
  double get chestRangeY => _aimModel?.chestRangeY ?? 0.0;
  
  // Setters - 转发到AimModel并标记为已修改，使用安全访问
  set aimRange(double value) {
    if (_aimModel != null && _aimModel!.aimRange != value) {
      _aimModel!.aimRange = value;
      _isDirty = true;
      _logger.i(_logTag, '瞄准范围被修改为: $value');
      notifyListeners();
    }
  }

  set trackRange(double value) {
    if (_aimModel != null && _aimModel!.trackRange != value) {
      _aimModel!.trackRange = value;
      _isDirty = true;
      _logger.i(_logTag, '跟踪范围被修改为: $value');
      notifyListeners();
    }
  }

  set headHeight(double value) {
    if (_aimModel != null && _aimModel!.headHeight != value) {
      _aimModel!.headHeight = value;
      _isDirty = true;
      _logger.i(_logTag, '头部高度被修改为: $value');
      notifyListeners();
    }
  }
  
  set neckHeight(double value) {
    if (_aimModel != null && _aimModel!.neckHeight != value) {
      _aimModel!.neckHeight = value;
      _isDirty = true;
      _logger.i(_logTag, '颈部高度被修改为: $value');
      notifyListeners();
    }
  }

  set chestHeight(double value) {
    if (_aimModel != null && _aimModel!.chestHeight != value) {
      _aimModel!.chestHeight = value;
      _isDirty = true;
      _logger.i(_logTag, '胸部高度被修改为: $value');
      notifyListeners();
    }
  }

  set headRangeX(double value) {
    if (_aimModel != null && _aimModel!.headRangeX != value) {
      _aimModel!.headRangeX = value;
      _isDirty = true;
      _logger.i(_logTag, '头部X范围被修改为: $value');
      notifyListeners();
    }
  }

  set headRangeY(double value) {
    if (_aimModel != null && _aimModel!.headRangeY != value) {
      _aimModel!.headRangeY = value;
      _isDirty = true;
      _logger.i(_logTag, '头部Y范围被修改为: $value');
      notifyListeners();
    }
  }

  set neckRangeX(double value) {
    if (_aimModel != null && _aimModel!.neckRangeX != value) {
      _aimModel!.neckRangeX = value;
      _isDirty = true;
      _logger.i(_logTag, '颈部X范围被修改为: $value');
      notifyListeners();
    }
  }

  set neckRangeY(double value) {
    if (_aimModel != null && _aimModel!.neckRangeY != value) {
      _aimModel!.neckRangeY = value;
      _isDirty = true;
      _logger.i(_logTag, '颈部Y范围被修改为: $value');
      notifyListeners();
    }
  }

  set chestRangeX(double value) {
    if (_aimModel != null && _aimModel!.chestRangeX != value) {
      _aimModel!.chestRangeX = value;
      _isDirty = true;
      _logger.i(_logTag, '胸部X范围被修改为: $value');
      notifyListeners();
    }
  }

  set chestRangeY(double value) {
    if (_aimModel != null && _aimModel!.chestRangeY != value) {
      _aimModel!.chestRangeY = value;
      _isDirty = true;
      _logger.i(_logTag, '胸部Y范围被修改为: $value');
      notifyListeners();
    }
  }
  
  /// 当参数发生变化时，处理参数变更并自动保存 - 使用ErrorHandler
  void handleParameterChanged(String paramName, double value, BuildContext context) {
    if (!isInitialized) {
      _logger.w(_logTag, '控制器未初始化，无法处理参数变更');
      return;
    }

    ErrorHandler.wrapAsyncOperation(
      tag: _logTag,
      operation: '处理参数变更',
      asyncOperation: () async {
        // 记录日志
        _logger.i(_logTag, '参数变更: $paramName = $value');

        // 如果没有变更，不进行保存
        if (!_isDirty) {
          _logger.i(_logTag, '无参数变更，无需保存');
          return;
        }

        // 检查WebSocket连接
        if (!_serverService!.isConnected) {
          _logger.w(_logTag, '服务器未连接，无法保存参数变更');
          ErrorHandler.showWarning(
            context: context,
            message: '服务器未连接，无法自动保存',
            tag: _logTag,
          );
          return;
        }

        // 更新用户和游戏信息
        _aimModel!.updateUserGameInfo(
          _authModel!.username,
          _gameModel!.currentGame
        );

        // 使用ApiRequestBuilder构建请求
        await _sendModifyRequest();

        // 标记为已保存
        _isDirty = false;

        // 保存到本地
        await _aimModel!.saveSettings();

        _logger.i(_logTag, '参数自动保存完成', {'paramName': paramName, 'value': value});
      },
      context: context,
      showToast: false, // 不显示错误提示，避免过多打扰
    );
  }

  /// 发送修改请求 - 使用ApiRequestBuilder
  Future<void> _sendModifyRequest() async {
    if (!isInitialized) {
      _logger.w(_logTag, '控制器未初始化，无法发送修改请求');
      return;
    }

    try {
      // 获取瞄准配置数据
      final aimJson = _aimModel!.toJson();
      final aimData = aimJson['content'] as Map<String, dynamic>;

      // 使用ApiRequestBuilder构建请求
      final request = _apiRequestBuilder!.buildAimRequest('modify', aimData);

      // 发送请求
      final jsonStr = jsonEncode(request);
      _serverService!.sendMessage(jsonStr);

      _logger.i(_logTag, '已发送瞄准配置修改请求', {
        'username': _authModel!.username,
        'gameName': _gameModel!.currentGame,
      });
    } catch (e, stackTrace) {
      _logger.e(_logTag, '发送修改请求失败', e.toString(), stackTrace);
      rethrow;
    }
  }

  /// 手动保存瞄准配置 - 使用ErrorHandler
  Future<void> saveAimConfig(BuildContext context) async {
    if (!isInitialized) {
      ErrorHandler.showWarning(
        context: context,
        message: '控制器未初始化，无法保存配置',
        tag: _logTag,
      );
      return;
    }

    // 如果没有变更，不进行保存
    if (!_isDirty) {
      ErrorHandler.showInfo(
        context: context,
        message: '无变更，无需保存',
        tag: _logTag,
      );
      return;
    }

    // 检查WebSocket连接
    if (!_serverService!.isConnected) {
      ErrorHandler.handleNetworkError(
        tag: _logTag,
        operation: '保存瞄准配置',
        error: '服务器未连接',
        context: context,
        customMessage: '服务器未连接，无法保存',
      );
      return;
    }

    await ErrorHandler.wrapAsyncOperation(
      tag: _logTag,
      operation: '手动保存瞄准配置',
      asyncOperation: () async {
        // 更新用户和游戏信息
        _aimModel!.updateUserGameInfo(
          _authModel!.username,
          _gameModel!.currentGame
        );

        // 使用ApiRequestBuilder发送请求
        await _sendModifyRequest();

        _isDirty = false;
        await _aimModel!.saveSettings();

        _logger.i(_logTag, '手动保存瞄准配置完成');
        return true;
      },
      context: context,
      successMessage: AppConstants.SUCCESS_SAVE,
    );
  }
  
  /// 重置为默认值 - 使用ErrorHandler
  void resetToDefaults(BuildContext context) {
    if (!isInitialized) {
      ErrorHandler.showWarning(
        context: context,
        message: '控制器未初始化，无法重置',
        tag: _logTag,
      );
      return;
    }

    ErrorHandler.wrapAsyncOperation(
      tag: _logTag,
      operation: '重置瞄准参数',
      asyncOperation: () async {
        // 使用AimModel的重置方法
        _aimModel!.resetToDefaults();

        _isDirty = true;

        _logger.i(_logTag, '瞄准参数已重置为默认值');

        notifyListeners();
        return true;
      },
      context: context,
      successMessage: '已重置为默认值',
    );
  }
  
  @override
  void dispose() {
    _isDisposed = true; // 设置已销毁标记

    // 移除WebSocket回调
    if (_isWebSocketListenerRegistered) {
      _serverService?.removeMessageListener(_handleWebSocketMessage);
      _isWebSocketListenerRegistered = false;
    }

    // 移除GameModel变更监听
    if (_isGameModelListenerRegistered) {
      _gameModel?.removeListener(_handleGameModelChanged);
      _isGameModelListenerRegistered = false;
    }

    _logger.i(_logTag, '控制器已释放');
    super.dispose();
  }
}
