// ignore_for_file: unused_import, use_build_context_synchronously

import 'package:flutter/material.dart';
import '../models/pid_model.dart';
import '../models/auth_model.dart';
import '../models/game_model.dart';
import '../services/server_service.dart';
import '../utils/logger.dart';
import '../component/message_component.dart';
import 'dart:convert';

/// PID控制器 - 负责近端瞄准辅助的参数管理和业务逻辑
class PidController extends ChangeNotifier {
  // 常量配置
  static const String logTag = 'PidController';
  static const Duration toastDuration = Duration(seconds: 2);
  static const Duration sliderEndDelay = Duration(milliseconds: 100);
  
  // 依赖引用
  final ServerService serverService;
  final AuthModel authModel;
  final GameModel gameModel;
  final PidModel pidModel;
  
  // 日志记录器
  final Logger logger = Logger();
  
  /// 构造函数
  PidController({
    required this.serverService,
    required this.authModel,
    required this.gameModel,
    required this.pidModel,
  }) {
    initializeController();
    setupWebSocketListener();
  }
  
  /// 初始化控制器
  Future<void> initializeController() async {
    if (authModel.isAuthenticated && gameModel.currentGame.isNotEmpty) {
      await requestPidParams();
    }
  }

  /// 设置WebSocket消息监听
  void setupWebSocketListener() {
    serverService.addMessageListener(handleWebSocketMessage);
  }
  
  /// 处理接收到的WebSocket消息
  void handleWebSocketMessage(dynamic message) {
    if (message is! String) return;
    
    try {
      final Map<String, dynamic> data = json.decode(message);
      
      if (data['action'] == 'pid_read_response' && data['status'] == 'success') {
        logger.i(logTag, '接收到PID参数读取响应');
        if (data['data'] != null) {
          pidModel.fromJson({'content': data['data']});
          notifyListeners();
        }
      } else if (data['action'] == 'pid_modify_response' && data['status'] == 'success') {
        logger.i(logTag, 'PID参数修改成功');
        if (data['data'] != null) {
          pidModel.fromJson({'content': data['data']});
          notifyListeners();
        }
      }
    } catch (e) {
      logger.e(logTag, '处理WebSocket消息失败', e);
    }
  }

  // PID参数 getters
  double get nearMoveFactor => pidModel.nearMoveFactor;
  double get nearStabilizer => pidModel.nearStabilizer; 
  double get nearResponseRate => pidModel.nearResponseRate;
  double get nearAssistZone => pidModel.nearAssistZone;
  double get nearResponseDelay => pidModel.nearResponseDelay;
  double get nearMaxAdjustment => pidModel.nearMaxAdjustment;
  double get farFactor => pidModel.farFactor;
  double get yAxisFactor => pidModel.yAxisFactor;
  double get pidRandomFactor => pidModel.pidRandomFactor;
  
  // 状态管理 getters
  bool get hasUnsavedChanges => pidModel.hasUnsavedChanges;
  bool get isSliderDragging => pidModel.isSliderDragging;
  bool get saveButtonEnabled => pidModel.saveButtonEnabled;
  bool get saveButtonLocked => pidModel.saveButtonLocked;
  
  // PID参数 setters
  set nearMoveFactor(double value) {
    pidModel.nearMoveFactor = value;
    notifyListeners();
  }
  
  set nearStabilizer(double value) {
    pidModel.nearStabilizer = value;
    notifyListeners();
  }
  
  set nearResponseRate(double value) {
    pidModel.nearResponseRate = value;
    notifyListeners();
  }
  
  set nearAssistZone(double value) {
    pidModel.nearAssistZone = value;
    notifyListeners();
  }
  
  set nearResponseDelay(double value) {
    pidModel.nearResponseDelay = value;
    notifyListeners();
  }
  
  set nearMaxAdjustment(double value) {
    pidModel.nearMaxAdjustment = value;
    notifyListeners();
  }
  
  set farFactor(double value) {
    pidModel.farFactor = value;
    notifyListeners();
  }
  
  set yAxisFactor(double value) {
    pidModel.yAxisFactor = value;
    notifyListeners();
  }
  
  set pidRandomFactor(double value) {
    pidModel.pidRandomFactor = value;
    notifyListeners();
  }
  
  /// 处理滑块拖动结束（滚轮松开）
  void handleSliderDragEnd(BuildContext context) {
    pidModel.setSliderDragging(false);
    
    Future.delayed(sliderEndDelay, () {
      sendPidModifyRequest();
      pidModel.lockSaveButton();
      showSliderDragEndMessage(context);
      notifyListeners();
    });
  }
  
  /// 处理加减按钮操作
  void handleButtonOperation() {
    pidModel.unlockSaveButton();
    notifyListeners();
  }
  
  /// 显示滑块拖动结束提示
  void showSliderDragEndMessage(BuildContext context) {
    MessageComponent.showIconToast(
      context: context,
      message: '参数已更新并发送到服务器',
      type: MessageType.success,
      duration: toastDuration,
    );
  }
  
  /// 构建PID修改请求
  Map<String, dynamic> buildPidModifyRequest() {
    return {
      'action': 'pid_modify',
      'content': {
        'username': authModel.username,
        'gameName': gameModel.currentGame,
        'nearMoveFactor': nearMoveFactor,
        'nearStabilizer': nearStabilizer,
        'nearResponseRate': nearResponseRate,
        'nearAssistZone': nearAssistZone,
        'nearResponseDelay': nearResponseDelay,
        'nearMaxAdjustment': nearMaxAdjustment,
        'farFactor': farFactor,
        'yAxisFactor': yAxisFactor,
        'pidRandomFactor': pidRandomFactor,
        'updatedAt': DateTime.now().toIso8601String(),
      }
    };
  }
  
  /// 发送PID修改请求到服务器
  void sendPidModifyRequest() {
    if (serverService.isConnected) {
      serverService.sendMessage(json.encode(buildPidModifyRequest()));
      logger.i(logTag, '已发送PID参数修改请求');
    } else {
      logger.w(logTag, 'WebSocket未连接，参数变更未发送到服务器');
    }
  }
  
  /// 请求PID参数
  Future<void> requestPidParams() async {
    try {
      pidModel.username = authModel.username;
      pidModel.gameName = gameModel.currentGame;
      
      await pidModel.loadSettings();
      
      if (serverService.isConnected) {
        final requestData = buildPidReadRequest();
        serverService.sendMessage(json.encode(requestData));
        logger.i(logTag, '已发送PID参数获取请求');
      } else {
        logger.w(logTag, 'WebSocket未连接，使用本地PID参数');
      }
      
      notifyListeners();
    } catch (e) {
      logger.e(logTag, '获取PID参数失败', e);
    }
  }
  
  /// 构建PID读取请求
  Map<String, dynamic> buildPidReadRequest() {
    return {
      'action': 'pid_read',
      'content': {
        'username': authModel.username,
        'token': authModel.token,
        'gameName': gameModel.currentGame
      }
    };
  }
  
  /// 重置为默认值
  void resetToDefaults(BuildContext context) {
    try {
      pidModel.resetToDefaults();
      notifyListeners();
      
      MessageComponent.showIconToast(
        context: context,
        message: '已重置为默认设置',
        type: MessageType.success,
        duration: toastDuration,
      );
      
      logger.i(logTag, '已重置PID参数为默认值');
    } catch (e) {
      logger.e(logTag, '重置PID参数失败', e);
      
      MessageComponent.showIconToast(
        context: context,
        message: '重置设置失败: $e',
        type: MessageType.error,
        duration: toastDuration,
      );
    }
  }
  
  /// 保存PID配置
  Future<void> savePidConfig(BuildContext context) async {
    try {
      pidModel.username = authModel.username;
      pidModel.gameName = gameModel.currentGame;
      
      await pidModel.saveSettings();
      sendPidModifyRequest();
      pidModel.clearUnsavedChanges();
      
      MessageComponent.showIconToast(
        context: context,
        message: '设置已保存',
        type: MessageType.success,
        duration: toastDuration,
      );
      
      logger.i(logTag, '已保存PID配置');
    } catch (e) {
      logger.e(logTag, '保存PID配置失败', e);
      
      MessageComponent.showIconToast(
        context: context,
        message: '保存配置失败: $e',
        type: MessageType.error,
        duration: toastDuration,
      );
    }
  }
  
  /// 处理参数变更
  void handleParameterChanged(String paramName, double value, BuildContext context) {
    try {
      logger.d(logTag, 'PID参数已变更: $paramName = $value');
      sendPidModifyRequest();
      logger.d(logTag, '参数变更后发送PID配置修改请求');
    } catch (e) {
      logger.e(logTag, '处理参数变更失败', e);
    }
  }
  
  @override
  void dispose() {
    serverService.removeMessageListener(handleWebSocketMessage);
    super.dispose();
  }
} 