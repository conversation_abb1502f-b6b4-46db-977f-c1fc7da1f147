// ignore_for_file: unused_import, unnecessary_brace_in_string_interps

import 'package:flutter/material.dart';
import '../component/card_component.dart';
import '../utils/logger.dart';
import 'header_controller.dart';
import 'dart:convert';
import '../services/server_service.dart';
import '../models/auth_model.dart';
import '../models/game_model.dart';
import '../component/message_component.dart';
import '../models/login_model.dart';
import 'dart:async';

/// 首页控制器 - 负责处理首页配置的业务逻辑
/// 
/// 使用Provider模式管理状态，包括游戏选择和卡密管理
/// 与HeaderController进行双向同步
class HomeController extends ChangeNotifier {
  /// 常量配置
  static const String _logTag = 'HomeController';
  static const Duration _toastDuration = Duration(seconds: 2);
  
  /// 选中的游戏
  CardItemConfig? _selectedGame;
  
  /// 卡密输入控制器
  final TextEditingController cardKeyController = TextEditingController();
  
  /// 心跳频率输入控制器
  final TextEditingController heartbeatIntervalController = TextEditingController();
  
  /// 页头控制器，用于同步游戏选择
  HeaderController? _headerController;
  
  /// 依赖的服务和模型
  final ServerService _serverService;
  final AuthModel _authModel;
  final GameModel _gameModel;
  final LoginModel _loginModel;
  
  /// 控制器是否已被销毁
  bool _isDisposed = false;
  
  /// 保存操作状态管理
  bool _isSaving = false;
  bool _loginRequestSent = false;
  bool _waitingForHomeResponse = false;
  
  /// 状态刷新等待管理
  bool _waitingForStatusRefresh = false;
  Timer? _statusRefreshTimeout;
  static const Duration _statusRefreshTimeoutDuration = Duration(seconds: 5);
  
  /// 获取当前选中的游戏
  CardItemConfig? get selectedGame => _selectedGame;
  
  /// 获取保存状态
  bool get isSaving => _isSaving;
  
  /// 日志
  final Logger _logger = Logger();
  
  /// 构造函数 - 注入所需的服务和模型
  HomeController({
    required ServerService serverService,
    required AuthModel authModel,
    required GameModel gameModel,
    required LoginModel loginModel,
  }) : _serverService = serverService,
       _authModel = authModel,
       _gameModel = gameModel,
       _loginModel = loginModel {
    _initializeController();
  }
  
  /// 初始化控制器
  void _initializeController() {
    _initFromGameModel();
    cardKeyController.text = _gameModel.cardKey;
    heartbeatIntervalController.text = _gameModel.heartbeatInterval.toString();
    
    // 添加心跳频率变更监听器
    _gameModel.addHeartbeatChangeListener(_onHeartbeatIntervalChanged);
    
    _serverService.addMessageListener(_handleServerMessage);
    _logger.i(_logTag, '初始化HomeController');
  }
  
  /// 设置页头控制器引用 - 实现双向同步
  void setHeaderController(HeaderController controller) {
    _headerController = controller;
    _headerController!.addListener(_syncFromHeader);
    _syncFromHeader();
  }
  
  /// 从页头控制器同步游戏选择
  void _syncFromHeader() {
    if (_headerController == null) return;
    
    final headerGameId = _headerController!.selectedGame.id;
    final matchedGame = _findGameById(headerGameId);
    
    if (matchedGame != null && _selectedGame?.id != matchedGame.id) {
      _selectedGame = matchedGame;
      _logger.i(_logTag, '从头部控制器同步游戏选择', {'gameId': matchedGame.id});
      notifyListeners();
    }
  }
  
  /// 从游戏模型初始化数据
  void _initFromGameModel() {
    final currentGameName = _gameModel.currentGame;
    final gameNameFormat = _extractFileNameFormat(currentGameName);
    
    final matchedGame = _findGameByNameFormat(gameNameFormat);
    if (matchedGame != null) {
      _selectedGame = matchedGame;
      _logger.i(_logTag, '匹配到游戏', {'id': matchedGame.id, 'title': matchedGame.title});
      return;
    }
    
    _setDefaultGame();
  }
  
  /// 设置默认游戏
  void _setDefaultGame() {
    final gameList = getGameCards();
    if (gameList.isNotEmpty) {
      _selectedGame = gameList.first;
      _gameModel.updateCurrentGame(_selectedGame!.id);
      _logger.w(_logTag, '未找到匹配游戏，使用首个游戏', {'id': _selectedGame!.id});
    }
  }
  
  /// 提取文件名格式
  String _extractFileNameFormat(String name) {
    if (!name.contains('/') && !name.contains('.')) {
      return name.toLowerCase();
    }
    final fileName = name.split('/').last;
    return fileName.split('.').first.toLowerCase();
  }
  
  /// 根据ID查找游戏
  CardItemConfig? _findGameById(String gameId) {
    final gameList = getGameCards();
    for (final game in gameList) {
      if (game.id.toLowerCase() == gameId.toLowerCase()) {
        return game;
      }
    }
    return null;
  }
  
  /// 根据名称格式查找游戏
  CardItemConfig? _findGameByNameFormat(String nameFormat) {
    final gameList = getGameCards();
    for (final game in gameList) {
      if (game.id.toLowerCase() == nameFormat || 
          game.title.toLowerCase() == nameFormat) {
        return game;
      }
    }
    return null;
  }
  
  /// 选择游戏 - 由UI调用
  void selectGame(CardItemConfig game) {
    if (_selectedGame?.id == game.id) return;
    
    final oldGame = _selectedGame?.id ?? 'none';
    _selectedGame = game;
    
    _logger.i(_logTag, '🎮 开始选择游戏', {
      'oldGame': oldGame,
      'newGame': game.id,
      'GameModel.currentGame(更新前)': _gameModel.currentGame,
    });
    
    // 调试：打印GameModel更新前状态
    _gameModel.debugPrintCurrentState('selectGame-更新前');
    
    // 立即更新GameModel，但不依赖其防抖机制
    _gameModel.updateCurrentGame(game.id);
    
    // 调试：打印GameModel更新后状态
    _gameModel.debugPrintCurrentState('selectGame-更新后');
    
    _logger.i(_logTag, '🎮 游戏选择已更新', {
      'oldGame': oldGame,
      'newGame': game.id, 
      'username': _authModel.username,
      'selectedGameId': _selectedGame?.id,
      'GameModel.currentGame(更新后)': _gameModel.currentGame,
    });
    
    if (_headerController != null) {
      _headerController!.handleGameSelected(game.id);
    } else {
      _notifyServerModelChanged('游戏');
    }
    
    notifyListeners();
  }
  
  /// 向服务器发送模型变更通知
  void _notifyServerModelChanged(String changeType) {
    if (!_serverService.isConnected) {
      _logger.w(_logTag, '无法发送${changeType}更改消息，服务器未连接');
      return;
    }
    
    try {
      _sendRequest('home_modify', '已发送${changeType}更改消息');
    } catch (e) {
      _logger.e(_logTag, '发送${changeType}更改消息失败', e.toString());
    }
  }
  
  /// 处理卡密输入变化 - 由UI调用
  void onCardKeyChanged(String value) {
    _gameModel.updateCardKey(value);
    _notifyServerModelChanged('卡密');
    
    // 触发页头刷新状态，等待响应后更新UI
    _triggerHeaderRefreshStatusAsync();
  }
  
  /// 处理心跳频率输入变化 - 由UI调用
  void onHeartbeatIntervalChanged(String value) {
    final interval = int.tryParse(value);
    if (interval != null && interval >= 1 && interval <= 300) {
      _gameModel.updateHeartbeatInterval(interval);
      _logger.i(_logTag, '心跳频率输入已更新', {'interval': '${interval}s'});
    } else {
      _logger.w(_logTag, '无效的心跳频率输入', {'value': value});
    }
  }
  
  /// 获取所有游戏卡片 - 由UI调用
  List<CardItemConfig> getGameCards() {
    final gameLabels = _gameModel.getAllGameLabels();
    
    return gameLabels.map((gameLabel) {
      return CardItemConfig.image(
        gameLabel.id,
        gameLabel.label,
        gameLabel.iconPath,
        backgroundColor: Colors.white,
      );
    }).toList();
  }
  
  /// 获取初始选中的游戏ID - 由UI调用
  String? getInitialSelectedId() => _selectedGame?.id;
  
  /// 刷新首页配置 - 由UI调用
  void refreshHomeConfig() {
    if (!_serverService.isConnected) {
      _logger.w(_logTag, '无法刷新首页配置，服务器未连接');
      return;
    }
    
    _sendRequest('home_read', '已请求首页配置');
    
    // 触发页头刷新状态，等待响应后更新UI
    _triggerHeaderRefreshStatusAsync();
  }
  
  /// 保存首页配置 - 由UI调用
  void saveHomeConfig(BuildContext context) {
    if (!_serverService.isConnected) {
      _logger.w(_logTag, '无法保存首页配置，服务器未连接');
      _showErrorMessage(context, '服务器未连接，无法保存配置');
      return;
    }
    
    if (_isSaving) {
      _logger.w(_logTag, '保存操作正在进行中，忽略重复请求');
      return;
    }
    
    try {
      _startSaveProcess(context);
      _sendLoginRequest();
    } catch (e) {
      _logger.e(_logTag, '启动保存流程失败', e.toString());
      _showErrorMessage(context, '保存失败: ${e.toString()}');
      _resetSaveState();
    }
  }
  
  /// 开始保存流程
  void _startSaveProcess(BuildContext context) {
    _isSaving = true;
    _loginRequestSent = false;
    _waitingForHomeResponse = false;
    notifyListeners();
    
    _showInfoMessage(context, '正在验证登录状态...');
    _logger.i(_logTag, '开始保存首页配置流程');
  }
  
  /// 发送登录请求
  void _sendLoginRequest() {
    try {
      final loginRequest = _buildLoginRequest();
      _serverService.sendMessage(jsonEncode(loginRequest));
      _loginRequestSent = true;
      _logger.i(_logTag, '已发送登录验证请求');
    } catch (e) {
      _logger.e(_logTag, '发送登录请求失败', e.toString());
      rethrow;
    }
  }
  
  /// 构建登录请求数据
  Map<String, dynamic> _buildLoginRequest() {
    // 使用当前认证用户的信息，而不是混合使用不同模型的数据
    return {
      'action': 'login_read',
      'content': {
        'username': _authModel.username,
        'token': _authModel.token, // 使用AuthModel中的token，确保一致性
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
    };
  }
  
  /// 发送首页配置请求
  void _sendHomeConfigRequest(BuildContext context) {
    try {
      _waitingForHomeResponse = true;
      _sendRequest('home_modify', '已发送首页配置保存请求');
      _showInfoMessage(context, '正在保存首页配置...');
      _logger.i(_logTag, '已发送首页配置保存请求');
    } catch (e) {
      _logger.e(_logTag, '发送首页配置请求失败', e.toString());
      _showErrorMessage(context, '保存失败: ${e.toString()}');
      _resetSaveState();
    }
  }
  
  /// 重置保存状态
  void _resetSaveState() {
    _isSaving = false;
    _loginRequestSent = false;
    _waitingForHomeResponse = false;
    notifyListeners();
  }
  
  /// 显示信息消息
  void _showInfoMessage(BuildContext context, String message) {
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.info,
      duration: _toastDuration,
    );
  }
  
  /// 发送请求到服务器
  void _sendRequest(String action, String logMessage) {
    try {
      final requestData = _buildRequestData(action);
      _serverService.sendMessage(jsonEncode(requestData));
      _logger.i(_logTag, logMessage);
    } catch (e) {
      _logger.e(_logTag, '发送请求失败', e.toString());
      rethrow;
    }
  }
  
  /// 构建请求数据
  Map<String, dynamic> _buildRequestData(String action) {
    // 使用当前选中的游戏，如果没有选中则使用GameModel中的游戏
    final currentGameName = _selectedGame?.id ?? _gameModel.currentGame;
    
    return {
      'action': action,
      'content': {
        'username': _authModel.username,
        'gameName': currentGameName,
        'cardKey': _gameModel.cardKey,
        'updatedAt': DateTime.now().toIso8601String(),
      }
    };
  }
  
  /// 获取Pro状态
  bool _getProStatus() {
    try {
      return _loginModel.isPro;
    } catch (e) {
      _logger.w(_logTag, '获取Pro状态失败', e.toString());
      return false;
    }
  }
  
  /// 显示成功消息
  void _showSuccessMessage(BuildContext context, String message) {
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.success,
      duration: _toastDuration,
    );
  }
  
  /// 显示错误消息
  void _showErrorMessage(BuildContext context, String message) {
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.error,
      duration: _toastDuration,
    );
  }
  
  /// 处理服务器消息
  void _handleServerMessage(dynamic message) {
    if (_isDisposed) return;
    
    try {
      final responseData = jsonDecode(message.toString());
      final action = responseData['action'];
      
      switch (action) {
        case 'home_read':
          _handleReadResponse(responseData);
          break;
        case 'home_modify_response':
          _handleModifyResponse(responseData);
          break;
        case 'login_read':
        case 'login_read_response':
          _handleLoginResponse(responseData);
          break;
        case 'status_bar_response':
          _handleStatusBarResponse(responseData);
          break;
      }
    } catch (e) {
      _logger.e(_logTag, '处理服务器消息失败', e.toString());
    }
  }
  
  /// 处理读取配置响应
  void _handleReadResponse(Map<String, dynamic> responseData) {
    _logger.i(_logTag, '收到首页配置响应', {
      'status': responseData['status'],
      'data': responseData['data']
    });
    
    if (responseData['status'] != 'ok') return;
    
    final data = responseData['data'];
    if (data == null) return;
    
    // 更新卡密（这个可以安全更新）
    _updateCardKey(data['cardKey']);

    // 更新卡密到期时间（仅用于显示）
    _updateCardKeyExpireTime(data['cardKeyExpireTime']);

    // 对于游戏选择，只在没有当前选择时才更新，避免覆盖用户的选择
    final serverGameName = data['gameName'];
    _logger.i(_logTag, '服务器返回的游戏信息', {
      'serverGameName': serverGameName,
      'currentSelectedGame': _selectedGame?.id,
      'gameModelCurrentGame': _gameModel.currentGame
    });
    
    // 只有在没有当前选择的游戏时，才使用服务器返回的游戏
    if (_selectedGame == null && serverGameName != null) {
      _logger.i(_logTag, '没有当前选择，使用服务器返回的游戏', {'gameName': serverGameName});
      _updateGameSelection(serverGameName);
    } else {
      _logger.i(_logTag, '保持当前游戏选择，不使用服务器返回的游戏');
    }
  }
  
  /// 处理登录响应
  void _handleLoginResponse(Map<String, dynamic> responseData) {
    if (!_isSaving || !_loginRequestSent) {
      // 如果不是在保存流程中，忽略登录响应
      return;
    }
    
    _logger.i(_logTag, '收到登录验证响应', {
      'status': responseData['status'],
      'isSaving': _isSaving
    });
    
    final context = WidgetsBinding.instance.focusManager.primaryFocus?.context;
    if (context == null) {
      _logger.w(_logTag, '无法获取上下文，取消保存流程');
      _resetSaveState();
      return;
    }
    
    final success = responseData['status'] == 'success';
    
    if (success) {
      _logger.i(_logTag, '登录验证成功，继续发送首页配置请求');
      _sendHomeConfigRequest(context);
    } else {
      final message = responseData['message'] ?? '登录验证失败';
      _logger.w(_logTag, '登录验证失败', {'message': message});
      _showErrorMessage(context, '登录验证失败: $message');
      _resetSaveState();
    }
  }
  
  /// 处理修改配置响应
  void _handleModifyResponse(Map<String, dynamic> responseData) {
    _logger.i(_logTag, '收到保存配置响应', {
      'status': responseData['status'],
      'isSaving': _isSaving,
      'waitingForHomeResponse': _waitingForHomeResponse
    });
    
    final context = WidgetsBinding.instance.focusManager.primaryFocus?.context;
    
    if (_isSaving && _waitingForHomeResponse) {
      // 这是保存流程中的首页配置响应
      if (responseData['status'] == 'ok') {
        final data = responseData['data'];
        if (data != null) {
          _logger.i(_logTag, '首页配置保存成功', {
            'username': data['username'], 
            'gameName': data['gameName']
          });
        }
        
        if (context != null) {
          _showSuccessMessage(context, '首页配置保存成功');
        }
        
        // 保存成功后触发页头刷新状态，等待响应后完成
        _triggerHeaderRefreshStatusAsync();
      } else {
        _logger.w(_logTag, '首页配置保存失败', {'status': responseData['status']});
        if (context != null) {
          _showErrorMessage(context, '首页配置保存失败');
        }
        _resetSaveState();
      }
    } else {
      // 这是普通的配置修改响应（非保存流程）
      if (responseData['status'] != 'ok') {
        _logger.w(_logTag, '配置修改响应状态异常', {'status': responseData['status']});
        return;
      }
      
      final data = responseData['data'];
      if (data != null) {
        _logger.i(_logTag, '配置修改成功', {
          'username': data['username'],
          'gameName': data['gameName']
        });

        // 更新卡密到期时间（如果服务器返回了）
        _updateCardKeyExpireTime(data['cardKeyExpireTime']);
      }
      
      // 普通配置修改成功后也触发页头刷新状态，等待响应后完成
      _triggerHeaderRefreshStatusAsync();
    }
  }
  
  /// 更新卡密
  void _updateCardKey(dynamic cardKey) {
    if (cardKey != null && cardKey.toString().isNotEmpty) {
      cardKeyController.text = cardKey.toString();
      _gameModel.updateCardKey(cardKey.toString());
    }
  }

  /// 更新卡密到期时间
  void _updateCardKeyExpireTime(dynamic expireTime) {
    if (expireTime != null && expireTime.toString().isNotEmpty) {
      _gameModel.updateCardKeyExpireTime(expireTime.toString());
    }
  }
  
  /// 更新游戏选择
  void _updateGameSelection(dynamic gameName) {
    if (gameName == null || gameName.toString().isEmpty) return;
    
    _gameModel.updateCurrentGame(gameName.toString());
    
    final matchedGame = _findGameById(gameName.toString());
    if (matchedGame != null) {
      _selectedGame = matchedGame;
      notifyListeners();
    }
  }
  
  /// 异步触发页头刷新状态（等待服务器响应）
  void _triggerHeaderRefreshStatusAsync() {
    if (!_serverService.isConnected) {
      _logger.w(_logTag, '无法触发页头刷新状态，服务器未连接');
      _completeStatusRefresh();
      return;
    }
    
    if (_waitingForStatusRefresh) {
      _logger.w(_logTag, '状态刷新请求正在进行中，忽略重复请求');
      return;
    }
    
    try {
      _waitingForStatusRefresh = true;
      
      // 设置超时定时器
      _statusRefreshTimeout?.cancel();
      _statusRefreshTimeout = Timer(_statusRefreshTimeoutDuration, () {
        if (_waitingForStatusRefresh) {
          _logger.w(_logTag, '状态刷新请求超时');
          _completeStatusRefresh();
        }
      });
      
      // 发送状态栏查询请求
      final requestData = {
        'action': 'status_bar_query',
        'token': _serverService.token,
      };
      _logger.i(_logTag, '准备发送状态刷新请求: $requestData');
      _serverService.sendMessage(requestData);
      
      _logger.i(_logTag, '已发送状态刷新请求，等待服务器响应...');
    } catch (e) {
      _logger.e(_logTag, '发送状态刷新请求失败', e.toString());
      _completeStatusRefresh();
    }
  }
  
  /// 处理状态栏响应
  void _handleStatusBarResponse(Map<String, dynamic> responseData) {
    if (!_waitingForStatusRefresh) {
      // 如果不是我们触发的状态刷新，直接返回
      return;
    }
    
    _logger.i(_logTag, '收到状态刷新响应', {
      'action': responseData['action'],
      'waitingForStatusRefresh': _waitingForStatusRefresh
    });
    
    // 状态数据已经通过ServerService自动更新到HeaderModel
    // 这里我们只需要完成刷新流程
    _completeStatusRefresh();
  }
  
  /// 完成状态刷新流程
  void _completeStatusRefresh() {
    _statusRefreshTimeout?.cancel();
    _statusRefreshTimeout = null;
    _waitingForStatusRefresh = false;
    
    // 如果是保存流程，现在才重置保存状态
    if (_isSaving && _waitingForHomeResponse) {
      _resetSaveState();
      _logger.i(_logTag, '保存流程完全完成，包括状态刷新');
    }
    
    _logger.i(_logTag, '状态刷新流程已完成，UI应该已更新最新状态');
  }
  
  /// 处理心跳频率变更
  void _onHeartbeatIntervalChanged(int newInterval) {
    if (!_isDisposed) {
      // 更新ServerService的心跳频率
      _serverService.setHeartbeatInterval(newInterval);
      
      // 同步更新输入框
      if (heartbeatIntervalController.text != newInterval.toString()) {
        heartbeatIntervalController.text = newInterval.toString();
      }
      
      _logger.i(_logTag, '心跳频率已同步更新', {'interval': '${newInterval}s'});
    }
  }
  
  /// 销毁资源
  @override
  void dispose() {
    _isDisposed = true;
    _saveCurrentState();
    _cleanupResources();
    super.dispose();
  }
  
  /// 保存当前状态
  void _saveCurrentState() {
    if (_selectedGame != null) {
      _gameModel.updateCurrentGame(_selectedGame!.id);
    }
    _gameModel.updateCardKey(cardKeyController.text);
  }
  
  /// 清理资源
  void _cleanupResources() {
    _serverService.removeMessageListener(_handleServerMessage);
    _gameModel.removeHeartbeatChangeListener(_onHeartbeatIntervalChanged);
    if (_headerController != null) {
      _headerController!.removeListener(_syncFromHeader);
    }
    _statusRefreshTimeout?.cancel();
    cardKeyController.dispose();
    heartbeatIntervalController.dispose();
  }
} 