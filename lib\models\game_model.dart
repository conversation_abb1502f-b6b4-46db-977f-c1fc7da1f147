// ignore_for_file: avoid_print, unnecessary_brace_in_string_interps

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 游戏标签项
class GameLabelItem {
  final String id;
  final String label;
  final String iconPath;

  GameLabelItem({
    required this.id,
    required this.label,
    required this.iconPath,
  });
}

/// 游戏图标资源路径常量
class GameIconPaths {
  static const String basePath = 'assets/GameImgIco/';
  static const String apex = '${basePath}apex.png';
  static const String cf = '${basePath}cf.png';
  static const String cfhd = '${basePath}cfhd.jpg';
  static const String csgo2 = '${basePath}csgo2.png';
  static const String defaultIcon = '${basePath}default.png';
  static const String hpjy = '${basePath}hpjy.jpg';
  static const String pubg = '${basePath}pubg.png';
  static const String sjz = '${basePath}sjz.png';
  static const String ssjj2 = '${basePath}ssjj2.png';
  static const String wwqy = '${basePath}wwqy.png';
}

/// 游戏模型 - 管理游戏相关数据
class GameModel extends ChangeNotifier {
  // 默认游戏ID
  static const String _defaultGameId = 'csgo2';
  
  // 默认心跳频率（秒）
  static const int _defaultHeartbeatInterval = 15;
  
  // 游戏列表 - 确保包含sjz而不是sj2，并添加pubg和hpjy
  final List<String> _gameList = [
    'apex',
    'cf',
    'cfhd',
    'csgo2',
    'hpjy',  // 添加hpjy游戏
    'pubg',  // 添加pubg游戏
    'sjz',   // 确保是sjz而不是sj2
    'ssjj2',
    'wwqy'
  ];
  
  // 当前选中的游戏
  String _currentGame = _defaultGameId;
  
  // 卡密信息
  String _cardKey = '';

  // 卡密到期时间
  String _cardKeyExpireTime = '';

  // 心跳频率（秒）
  int _heartbeatInterval = _defaultHeartbeatInterval;
  
  // 游戏切换监听器
  final List<Function(String)> _gameChangeListeners = [];
  
  // 心跳频率变更监听器
  final List<Function(int)> _heartbeatChangeListeners = [];
  
  // 添加上次游戏切换时间记录
  DateTime _lastGameChangeTime = DateTime.now().subtract(const Duration(minutes: 1));
  
  // Getters
  List<String> get gameList => _gameList;
  String get currentGame {
    // 强制打印当前状态用于调试
    print('🔍 GameModel.currentGame被访问: $_currentGame (实例ID: ${identityHashCode(this)})');
    return _currentGame;
  }
  String get cardKey => _cardKey;
  String get cardKeyExpireTime => _cardKeyExpireTime;
  int get heartbeatInterval => _heartbeatInterval;
  
  // 添加游戏切换监听器
  void addGameChangeListener(Function(String) listener) {
    if (!_gameChangeListeners.contains(listener)) {
      _gameChangeListeners.add(listener);
    }
  }
  
  // 移除游戏切换监听器
  void removeGameChangeListener(Function(String) listener) {
    _gameChangeListeners.remove(listener);
  }
  
  // 添加心跳频率变更监听器
  void addHeartbeatChangeListener(Function(int) listener) {
    if (!_heartbeatChangeListeners.contains(listener)) {
      _heartbeatChangeListeners.add(listener);
    }
  }
  
  // 移除心跳频率变更监听器
  void removeHeartbeatChangeListener(Function(int) listener) {
    _heartbeatChangeListeners.remove(listener);
  }
  
  // 触发游戏变更事件
  void _notifyGameChanged(String newGame) {
    for (final listener in _gameChangeListeners) {
      try {
        listener(newGame);
      } catch (e) {
        print('游戏变更监听器错误: $e');
      }
    }
  }
  
  // 触发心跳频率变更事件
  void _notifyHeartbeatChanged(int newInterval) {
    for (final listener in _heartbeatChangeListeners) {
      try {
        listener(newInterval);
      } catch (e) {
        print('心跳频率变更监听器错误: $e');
      }
    }
  }
  
  // 图标路径获取
  String getGameIconPath(String gameName) {
    final gameIcons = {
      'apex': GameIconPaths.apex,
      'cf': GameIconPaths.cf,
      'cfhd': GameIconPaths.cfhd,
      'csgo2': GameIconPaths.csgo2,
      'hpjy': GameIconPaths.hpjy,
      'pubg': GameIconPaths.pubg,
      'sjz': GameIconPaths.sjz,
      'ssjj2': GameIconPaths.ssjj2,
      'wwqy': GameIconPaths.wwqy,
    };

    return gameIcons[gameName] ?? GameIconPaths.defaultIcon;
  }
  
  /// 获取全部游戏标签项列表
  List<GameLabelItem> getAllGameLabels() {
    return _gameList.map((gameId) {
      return GameLabelItem(
        id: gameId,
        label: gameId,
        iconPath: getGameIconPath(gameId),
      );
    }).toList();
  }
  
  /// 获取默认游戏标签
  GameLabelItem getDefaultGameLabel() {
    return GameLabelItem(
      id: _defaultGameId,
      label: _defaultGameId,
      iconPath: getGameIconPath(_defaultGameId),
    );
  }
  
  /// 根据ID查找游戏标签项
  GameLabelItem? findGameLabelById(String gameId) {
    if (_gameList.contains(gameId)) {
      return GameLabelItem(
        id: gameId,
        label: gameId,
        iconPath: getGameIconPath(gameId),
      );
    }
    return null;
  }
  
  // 初始化 - 从持久化存储加载设置
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 定义最新的游戏列表（确保包含sjz而不是sj2，并添加pubg和hpjy）
    final latestGameList = [
      'apex',
      'cf',
      'cfhd',
      'csgo2',
      'hpjy',  // 添加hpjy游戏
      'pubg',  // 添加pubg游戏
      'sjz',
      'ssjj2',
      'wwqy'
    ];
    
    // 检查存储的游戏列表
    final savedGameList = prefs.getStringList('gameList');
    if (savedGameList != null && savedGameList.isNotEmpty) {
      print('从存储加载的游戏列表: $savedGameList');
      
      // 检查是否需要更新游戏列表
      bool needsUpdate = false;
      
      // 检查是否包含旧的游戏名称sj2
      if (savedGameList.contains('sj2') && !savedGameList.contains('sjz')) {
        print('检测到旧的游戏名称sj2，需要更新为sjz');
        needsUpdate = true;
      }
      
      // 检查是否缺少pubg游戏
      if (!savedGameList.contains('pubg')) {
        print('检测到缺少pubg游戏，需要添加');
        needsUpdate = true;
      }

      // 检查是否缺少hpjy游戏
      if (!savedGameList.contains('hpjy')) {
        print('检测到缺少hpjy游戏，需要添加');
        needsUpdate = true;
      }
      
      // 如果需要更新，使用最新的游戏列表
      if (needsUpdate) {
        print('更新游戏列表为最新版本');
        _gameList.clear();
        _gameList.addAll(latestGameList);
        // 立即保存更新后的列表
        await prefs.setStringList('gameList', _gameList);
      } else {
        _gameList.clear();
        _gameList.addAll(savedGameList);
      }
    } else {
      print('使用默认游戏列表: $latestGameList');
      _gameList.clear();
      _gameList.addAll(latestGameList);
      // 保存默认列表到SharedPreferences
      await prefs.setStringList('gameList', _gameList);
    }
    
    print('最终游戏列表: $_gameList');
    
    // 保存旧值以检测变化
    final oldGame = _currentGame;
    
    final savedCurrentGame = prefs.getString('currentGame') ?? _defaultGameId;
    
    // 如果当前游戏是旧的sj2，则更新为sjz
    if (savedCurrentGame == 'sj2') {
      print('检测到旧的当前游戏sj2，更新为sjz');
      _currentGame = 'sjz';
      await prefs.setString('currentGame', 'sjz');
    } else {
      _currentGame = savedCurrentGame;
    }
    
    _cardKey = prefs.getString('cardKey') ?? '';
    
    // 加载心跳频率设置
    _heartbeatInterval = prefs.getInt('heartbeatInterval') ?? _defaultHeartbeatInterval;
    
    print('GameModel加载设置: oldGame=$oldGame, newGame=$_currentGame, cardKey=${_getCardKeyDisplay()}, heartbeatInterval=${_heartbeatInterval}s');
    
    // 检测游戏是否变更
    if (oldGame != _currentGame) {
      print('检测到游戏变更，触发监听器: $oldGame -> $_currentGame');
      _notifyGameChanged(_currentGame);
    }
    
    notifyListeners();
  }
  
  // 更新当前游戏
  Future<void> updateCurrentGame(String gameName) async {
    print('updateCurrentGame被调用: 请求游戏=$gameName, 当前游戏=$_currentGame');
    print('调用栈: ${StackTrace.current.toString().split('\n').take(5).join('\n')}');
    
    // 检查游戏是否存在于列表中，且与当前游戏不同
    if (_gameList.contains(gameName) && _currentGame != gameName) {
      // 防抖动：检查距离上次切换时间是否足够
      final now = DateTime.now();
      if (now.difference(_lastGameChangeTime).inMilliseconds < 500) {
        print('忽略过快的游戏切换请求，上次切换时间: ${_lastGameChangeTime.toIso8601String()}');
        return;
      }
      
      // 更新上次切换时间
      _lastGameChangeTime = now;
      
      // 保存旧值以用于输出日志
      final oldGame = _currentGame;
      
      _currentGame = gameName;
      await _saveSettings();
      
      // 触发游戏变更事件
      _notifyGameChanged(gameName);
      
      notifyListeners();
      
      print('✅ 游戏已成功切换: $oldGame -> $gameName');
      print('✅ 当前GameModel.currentGame = $_currentGame');
    } else if (!_gameList.contains(gameName)) {
      print('❌ 警告: 游戏 $gameName 不在游戏列表中: $_gameList');
    } else {
      print('⚠️ 游戏未变更: $gameName (当前已是此游戏)');
    }
  }
  
  // 更新卡密
  Future<void> updateCardKey(String cardKey) async {
    _cardKey = cardKey;
    await _saveSettings();
    notifyListeners();
  }

  // 更新卡密到期时间（仅用于显示，不保存到本地）
  void updateCardKeyExpireTime(String expireTime) {
    _cardKeyExpireTime = expireTime;
    notifyListeners();
  }
  
  // 更新心跳频率
  Future<void> updateHeartbeatInterval(int interval) async {
    // 验证心跳频率范围（1-300秒）
    if (interval < 1 || interval > 300) {
      print('❌ 心跳频率超出有效范围(1-300秒): $interval');
      return;
    }
    
    if (_heartbeatInterval != interval) {
      final oldInterval = _heartbeatInterval;
      _heartbeatInterval = interval;
      await _saveSettings();
      
      // 触发心跳频率变更事件
      _notifyHeartbeatChanged(interval);
      
      notifyListeners();
      
      print('✅ 心跳频率已更新: ${oldInterval}s -> ${interval}s');
    }
  }
  
  // 添加游戏
  Future<void> addGame(String gameName) async {
    if (!_gameList.contains(gameName)) {
      _gameList.add(gameName);
      await _saveSettings();
      notifyListeners();
    }
  }
  
  // 移除游戏
  Future<void> removeGame(String gameName) async {
    if (_gameList.contains(gameName)) {
      _gameList.remove(gameName);
      
      // 如果移除的是当前游戏，则切换到默认游戏
      if (_currentGame == gameName && _gameList.isNotEmpty) {
        final newGame = _gameList.first;
        _currentGame = newGame;
        
        // 触发游戏变更事件
        _notifyGameChanged(newGame);
      }
      
      await _saveSettings();
      notifyListeners();
    }
  }
  
  // 调试方法：打印当前状态
  void debugPrintCurrentState(String caller) {
    print('🎮 GameModel状态检查 [$caller]:');
    print('   - 当前游戏: $_currentGame');
    print('   - 游戏列表: $_gameList');
    print('   - 卡密: ${_getCardKeyDisplay()}');
    print('   - 心跳频率: ${_heartbeatInterval}秒');
    print('   - 实例ID: ${identityHashCode(this)}');
    print('   - 上次切换时间: ${_lastGameChangeTime.toIso8601String()}');
  }
  
  // 安全地获取卡密的显示格式（前8位+...）
  String _getCardKeyDisplay() {
    if (_cardKey.isEmpty) return 'empty';
    final maxLength = _cardKey.length < 8 ? _cardKey.length : 8;
    return '${_cardKey.substring(0, maxLength)}...';
  }

  // 保存设置到持久化存储
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setStringList('gameList', _gameList);
    await prefs.setString('currentGame', _currentGame);
    await prefs.setString('cardKey', _cardKey);
    await prefs.setInt('heartbeatInterval', _heartbeatInterval);
  }
} 