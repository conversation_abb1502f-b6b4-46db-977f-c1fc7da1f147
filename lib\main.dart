// ignore_for_file: use_super_parameters, library_private_types_in_public_api, unused_import, sort_child_properties_last, unnecessary_null_comparison

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:ui';

// 视图导入
import 'views/login_screen.dart';
import 'views/register_screen.dart';
import 'views/layout_screen.dart';
import 'views/home_screen.dart';
import 'views/function/function_screen.dart';
import 'views/pid_screen.dart';
import 'views/fov_screen.dart';
import 'views/aim_screen.dart';
import 'views/fire_screen.dart';
import 'views/data_collection_screen.dart';

// 核心模型导入（需要在main中初始化）
import 'models/auth_model.dart';
import 'models/app_settings_model.dart';
import 'models/game_model.dart';
import 'models/login_model.dart';
import 'models/ui_config_model.dart';

// 这些模型现在通过ProviderManager注册，不需要在main.dart中导入

// 服务导入
import 'services/server_service.dart';
// AuthService现在通过ProviderManager注册

// 工具类导入
import 'utils/provider_manager.dart';
import 'utils/app_constants.dart';
import 'utils/logger.dart';
import 'utils/keyboard_handler.dart';

// 调试文件已清理

// 全局服务访问助手函数
ServerService getServerService(BuildContext context) => Provider.of<ServerService>(context, listen: false);

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  final Logger logger = Logger();

  // 设置全局错误处理
  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    logger.e(AppConstants.LOG_TAG_APP, '🚨 Flutter Error', details.exception.toString());

    // 特殊处理Navigator相关错误
    if (details.exception.toString().contains('Navigator operation requested with a context that does not include a Navigator')) {
      logger.w(AppConstants.LOG_TAG_APP, '🔧 Navigator错误被捕获并忽略，这通常发生在页面跳转过程中');
      return; // 不再抛出异常
    }

    // 特殊处理widget生命周期错误
    if (details.exception.toString().contains('Looking up a deactivated widget\'s ancestor is unsafe')) {
      logger.w(AppConstants.LOG_TAG_APP, '🔧 Widget生命周期错误被捕获，这通常发生在组件销毁过程中');
      return; // 不再抛出异常
    }
  };

  // 设置未捕获异常处理
  PlatformDispatcher.instance.onError = (error, stack) {
    logger.e(AppConstants.LOG_TAG_APP, '🚨 Uncaught Error', error.toString());

    // 特殊处理空值检查错误
    if (error.toString().contains('Null check operator used on a null value')) {
      logger.w(AppConstants.LOG_TAG_APP, '🔧 空值检查错误被捕获，可能是组件销毁时的时机问题');
      return true; // 标记为已处理
    }

    // 特殊处理widget生命周期错误
    if (error.toString().contains('Looking up a deactivated widget\'s ancestor is unsafe')) {
      logger.w(AppConstants.LOG_TAG_APP, '🔧 Widget ancestor访问错误被捕获，组件已销毁');
      return true; // 标记为已处理
    }

    return true;
  };

  logger.i(AppConstants.LOG_TAG_APP, '应用启动', {'version': '1.0.0'});

  try {
    // 初始化共享偏好设置
    final sharedPreferences = await SharedPreferences.getInstance();
    logger.d(AppConstants.LOG_TAG_APP, 'SharedPreferences初始化完成');

    // 创建AuthModel并加载认证状态
    final authModel = AuthModel();
    await authModel.loadAuthState();
    logger.d(AppConstants.LOG_TAG_APP, 'AuthModel加载完成', {'isAuthenticated': authModel.isAuthenticated});

    // 创建GameModel并加载游戏设置
    final gameModel = GameModel();
    await gameModel.loadSettings();
    logger.d(AppConstants.LOG_TAG_APP, 'GameModel加载完成', {'currentGame': gameModel.currentGame});

    // 创建LoginModel并加载登录设置
    final loginModel = LoginModel();
    await loginModel.loadLoginSettings();
    logger.d(AppConstants.LOG_TAG_APP, 'LoginModel加载完成');

    // 创建UIConfigModel并加载UI配置
    final uiConfigModel = UIConfigModel();
    await uiConfigModel.loadSettings();
    logger.d(AppConstants.LOG_TAG_APP, 'UIConfigModel加载完成');

    // 创建AppSettingsModel并加载应用设置
    final appSettingsModel = AppSettingsModel();
    await appSettingsModel.loadSettings();
    logger.d(AppConstants.LOG_TAG_APP, 'AppSettingsModel加载完成');

    // 验证Provider依赖关系
    if (ProviderManager.validateProviderDependencies()) {
      logger.i(AppConstants.LOG_TAG_APP, 'Provider依赖验证通过');
    }

    // 输出Provider统计信息
    final providerStats = ProviderManager.getProviderStats();
    logger.i(AppConstants.LOG_TAG_APP, 'Provider统计信息', providerStats);

    // Provider注册完成

    runApp(MyApp(
      sharedPreferences: sharedPreferences,
      authModel: authModel,
      gameModel: gameModel,
      loginModel: loginModel,
      uiConfigModel: uiConfigModel,
      appSettingsModel: appSettingsModel,
    ));

  } catch (e, stackTrace) {
    logger.e(AppConstants.LOG_TAG_APP, '应用启动失败', e.toString(), stackTrace);
    // 这里可以显示错误页面或者重试逻辑
    rethrow;
  }
}

class MyApp extends StatelessWidget {
  final SharedPreferences sharedPreferences;
  final AuthModel authModel;
  final GameModel gameModel;
  final LoginModel loginModel;
  final UIConfigModel uiConfigModel;
  final AppSettingsModel appSettingsModel;

  const MyApp({
    Key? key,
    required this.sharedPreferences,
    required this.authModel,
    required this.gameModel,
    required this.loginModel,
    required this.uiConfigModel,
    required this.appSettingsModel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // 核心服务（仍需要手动注册）
        Provider<SharedPreferences>.value(value: sharedPreferences),

        // 预初始化的模型（使用value方式注册）
        ChangeNotifierProvider.value(value: authModel),
        ChangeNotifierProvider.value(value: gameModel),
        ChangeNotifierProvider.value(value: loginModel),
        ChangeNotifierProvider.value(value: uiConfigModel),
        ChangeNotifierProvider.value(value: appSettingsModel),

        // 使用ProviderManager注册所有Provider
        ...ProviderManager.getAllProviders(),
      ],
      child: Consumer2<UIConfigModel, AppSettingsModel>(
        builder: (context, uiConfig, appSettings, child) {
          return KeyboardEventWrapper(
            child: MaterialApp(
              title: 'BlWeb',
              debugShowCheckedModeBanner: false,
              // 使用UIConfigModel中的字体配置
              theme: _buildAppTheme(uiConfig),
              darkTheme: _buildDarkTheme(uiConfig),
              themeMode: ThemeMode.system,
              // 使用AppSettingsModel中定义的路由
              initialRoute: authModel.isAuthenticated ? '/' : '/login',
              routes: appSettings.getRoutes(context),
            ),
          );
        },
      ),
    );
  }

  /// 构建应用主题
  ThemeData _buildAppTheme(UIConfigModel uiConfig) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF2196F3),
        brightness: Brightness.light,
      ),

      // 字体配置
      textTheme: uiConfig.useGoogleFonts
          ? uiConfig.getTextTheme(ThemeData.light().textTheme)
          : ThemeData.light().textTheme,

      // 组件主题
      appBarTheme: const AppBarTheme(
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.black87,
        centerTitle: true,
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 2,
        ),
      ),

      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(8),
      ),

      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),

      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }

  /// 构建暗色主题
  ThemeData _buildDarkTheme(UIConfigModel uiConfig) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF2196F3),
        brightness: Brightness.dark,
      ),

      // 字体配置
      textTheme: uiConfig.useGoogleFonts
          ? uiConfig.getTextTheme(ThemeData.dark().textTheme)
          : ThemeData.dark().textTheme,

      // 组件主题
      appBarTheme: const AppBarTheme(
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        centerTitle: true,
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 2,
        ),
      ),

      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(8),
      ),

      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        filled: true,
      ),

      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }
}
