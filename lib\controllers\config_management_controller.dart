import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'dart:convert';
import '../utils/web_environment_config.dart';
import '../models/config_management_model.dart';
import '../models/auth_model.dart';
import '../models/game_model.dart';
import '../models/home_model.dart';
import '../models/function_model.dart';
import '../models/pid_model.dart';
import '../models/fov_model.dart';
import '../models/aim_model.dart';
import '../models/fire_model.dart';
import '../models/data_collection_model.dart';
import '../component/message_component.dart';
import '../utils/logger.dart';
import '../services/server_service.dart';
import 'header_controller.dart';

/// 配置管理控制器 - 负责配置的导入导出逻辑
class ConfigManagementController extends ChangeNotifier {
  final Logger _logger = Logger();
  static const String _tag = 'ConfigManagementController';

  final ConfigManagementModel _configModel;
  final AuthModel _authModel;
  final GameModel _gameModel;
  final ServerService _serverService;
  final HeaderController? _headerController;

  // 文件名输入控制器
  final TextEditingController _fileNameController = TextEditingController();

  // Getters
  TextEditingController get fileNameController => _fileNameController;

  // 构造函数
  ConfigManagementController({
    required ConfigManagementModel configModel,
    required AuthModel authModel,
    required GameModel gameModel,
    required ServerService serverService,
    HeaderController? headerController,
  }) : _configModel = configModel,
       _authModel = authModel,
       _gameModel = gameModel,
       _serverService = serverService,
       _headerController = headerController {
    _logger.i(_tag, 'ConfigManagementController初始化完成');
  }
  
  // Getters
  bool get isExporting => _configModel.isExporting;
  bool get isImporting => _configModel.isImporting;
  String get lastExportPath => _configModel.lastExportPath;
  String get lastImportPath => _configModel.lastImportPath;
  
  /// 导出指定类型的配置
  Future<void> exportConfig(BuildContext context, String configType) async {
    try {
      _logger.i(_tag, '开始导出配置', {'configType': configType});
      
      // 收集配置数据
      final configData = await _collectConfigData(context, configType);
      if (configData.isEmpty) {
        MessageComponent.showToast(
          context: context,
          message: '没有可导出的配置数据',
          type: MessageType.error,
        );
        return;
      }

      // 获取自定义文件名
      final customFileName = _fileNameController.text.trim().isEmpty
          ? null
          : _fileNameController.text.trim();

      // 执行导出
      final success = await _configModel.exportConfig(
        configData: configData,
        configType: configType,
        customFileName: customFileName,
      );

      if (success) {
        MessageComponent.showToast(
          context: context,
          message: '配置导出成功！',
          type: MessageType.success,
        );
        _logger.i(_tag, '配置导出成功', {
          'configType': configType,
          'filePath': _configModel.lastExportPath,
        });
      } else {
        MessageComponent.showToast(
          context: context,
          message: '配置导出失败',
          type: MessageType.error,
        );
      }
    } catch (e, stackTrace) {
      _logger.e(_tag, '导出配置时出错', e, stackTrace);
      MessageComponent.showToast(
        context: context,
        message: '导出配置时出错: ${e.toString()}',
        type: MessageType.error,
      );
    }
  }
  
  /// 导入配置 - 新的简化方法
  Future<void> importConfig(BuildContext context) async {
    try {
      _logger.i(_tag, '🚀 开始导入配置流程');
      print('🚀 ConfigManagementController.importConfig 被调用');

      // 检查服务器连接状态
      if (!_serverService.isConnected) {
        _logger.e(_tag, '❌ 服务器未连接，无法导入配置');
        MessageComponent.showToast(
          context: context,
          message: '服务器未连接，请先连接服务器后再导入配置',
          type: MessageType.error,
        );
        return;
      }

      // 执行导入
      _logger.i(_tag, '📁 开始选择文件');
      final importData = await _configModel.importConfig();
      if (importData == null) {
        _logger.w(_tag, '❌ 导入失败或用户取消');

        // 检查是否有具体的错误信息
        if (_configModel.lastError != null) {
          if (context.mounted) {
            // 使用多种方式确保消息能够显示
            _showErrorMessage(context, '导入失败: ${_configModel.lastError}');
          }
        }
        // 如果没有错误信息，说明用户取消了操作，不显示消息
        return;
      }

      _logger.i(_tag, '✅ 文件选择成功，准备显示预览对话框', {
        'configType': importData['configType'],
        'version': importData['version'],
        'hasConfigData': importData['configData'] != null
      });

      // 显示导入预览对话框
      if (!context.mounted) return;
      final confirmed = await _showImportPreviewDialog(context, importData);
      if (!confirmed) {
        _logger.w(_tag, '❌ 用户取消导入确认');
        return;
      }

      _logger.i(_tag, '✅ 用户确认导入，开始应用配置');

      // 使用新的简化方法应用配置
      if (!context.mounted) return;
      final success = await _applyConfigDirectly(context, importData);

      _logger.i(_tag, '📊 配置应用结果', {'success': success});

      if (!context.mounted) return;

      if (success) {
        _logger.i(_tag, '🎉 配置导入成功，显示成功消息');
        _showSuccessMessage(context, '配置导入成功！UI已更新。');
        _logger.i(_tag, '配置导入成功', {
          'configType': importData['configType'],
          'filePath': _configModel.lastImportPath,
        });
      } else {
        _logger.e(_tag, '❌ 配置导入失败，显示失败消息');
        _showErrorMessage(context, '配置导入失败，请检查配置文件格式和服务器连接');
      }
    } catch (e, stackTrace) {
      _logger.e(_tag, '💥 导入配置时出错', e, stackTrace);
      if (context.mounted) {
        MessageComponent.showToast(
          context: context,
          message: '导入配置时出错: ${e.toString()}',
          type: MessageType.error,
        );
      }
    }
  }

  /// 应用配置 - 正确的逻辑：先调用home_modify API，再触发UI更新
  Future<bool> _applyConfigDirectly(BuildContext context, Map<String, dynamic> importData) async {
    try {
      final configData = importData['configData'] as Map<String, dynamic>;
      final configType = importData['configType'] as String;

      _logger.i(_tag, '开始应用导入的配置', {
        'configType': configType,
        'availableConfigs': configData.keys.toList(),
      });

      int successCount = 0;
      int totalCount = 0;

      // 1. 首先处理首页配置 - 通过API写入服务器
      if ((configType == 'all' || configType == 'home') && configData.containsKey('home')) {
        totalCount++;
        _logger.i(_tag, '开始处理首页配置导入');

        final homeData = configData['home'];
        final content = homeData['content'] ?? homeData;

        // 调用home_modify API写入服务器
        if (await _writeHomeConfigToServer(homeData)) {
          successCount++;
          _logger.i(_tag, '首页配置已写入服务器');

          // 2. 然后触发左上角头像的游戏选择函数来更新整个UI
          if (content['gameName'] != null) {
            await _triggerGameSelectionUpdate(content['gameName'].toString());
            _logger.i(_tag, '已触发游戏选择UI更新: ${content['gameName']}');
          }
        } else {
          _logger.e(_tag, '首页配置写入服务器失败');
        }
      }

      // 处理其他配置类型（功能、PID、瞄准等）
      if ((configType == 'all' || configType == 'function') && configData.containsKey('function')) {
        totalCount++;
        if (await _writeFunctionConfigToServer(configData['function'])) {
          successCount++;
        }
      }

      if ((configType == 'all' || configType == 'pid') && configData.containsKey('pid')) {
        totalCount++;
        if (await _writePidConfigToServer(configData['pid'])) {
          successCount++;
        }
      }

      if ((configType == 'all' || configType == 'aim') && configData.containsKey('aim')) {
        totalCount++;
        if (await _writeAimConfigToServer(configData['aim'])) {
          successCount++;
        }
      }

      if ((configType == 'all' || configType == 'fov') && configData.containsKey('fov')) {
        totalCount++;
        if (await _writeFovConfigToServer(configData['fov'])) {
          successCount++;
        }
      }

      if ((configType == 'all' || configType == 'fire') && configData.containsKey('fire')) {
        totalCount++;
        if (await _writeFireConfigToServer(configData['fire'])) {
          successCount++;
        }
      }

      if ((configType == 'all' || configType == 'data_collection') && configData.containsKey('data_collection')) {
        totalCount++;
        if (await _writeDataCollectionConfigToServer(configData['data_collection'])) {
          successCount++;
        }
      }

      _logger.i(_tag, '配置应用完成', {
        'successCount': successCount,
        'totalCount': totalCount,
        'successRate': totalCount > 0 ? '${(successCount / totalCount * 100).toStringAsFixed(1)}%' : '0%'
      });

      return successCount > 0;
    } catch (e, stackTrace) {
      _logger.e(_tag, '应用配置时出错', e, stackTrace);
      return false;
    }
  }

  /// 触发游戏选择更新 - 调用左上角头像中的游戏选择函数
  Future<void> _triggerGameSelectionUpdate(String gameName) async {
    try {
      _logger.i(_tag, '开始触发游戏选择UI更新', {'gameName': gameName});

      // 1. 确保GameModel已更新
      await _gameModel.updateCurrentGame(gameName);
      _logger.i(_tag, 'GameModel已更新为: ${_gameModel.currentGame}');

      // 2. 通过HeaderController触发游戏选择，这会更新整个UI
      if (_headerController != null) {
        _headerController.handleGameSelected(gameName);
        _logger.i(_tag, '已通过HeaderController触发游戏选择更新');
      } else {
        _logger.w(_tag, 'HeaderController为null，无法触发UI更新');
      }

      // 3. 等待一小段时间让UI更新完成
      await Future.delayed(const Duration(milliseconds: 500));

      _logger.i(_tag, '游戏选择UI更新完成', {
        'targetGame': gameName,
        'currentGameModel': _gameModel.currentGame,
      });

    } catch (e, stackTrace) {
      _logger.e(_tag, '触发游戏选择UI更新失败', e, stackTrace);
    }
  }
  
  /// 收集配置数据
  Future<Map<String, dynamic>> _collectConfigData(BuildContext context, String configType) async {
    final result = <String, dynamic>{};

    try {
      if (configType == 'all' || configType == 'home') {
        final homeModel = Provider.of<HomeModel>(context, listen: false);
        // 确保HomeModel已初始化
        homeModel.initialize(context);

        // 获取完整的home配置，但排除敏感信息
        final homeData = homeModel.toJson();
        final filteredHomeData = _filterSensitiveHomeData(homeData);
        result['home'] = filteredHomeData;
      }

      if (configType == 'all' || configType == 'function') {
        final functionModel = Provider.of<FunctionModel>(context, listen: false);
        // FunctionModel的toJson返回String，需要解析为Map
        final functionJsonString = functionModel.toJson();
        result['function'] = jsonDecode(functionJsonString);
      }

      if (configType == 'all' || configType == 'pid') {
        final pidModel = Provider.of<PidModel>(context, listen: false);
        result['pid'] = pidModel.toJson();
      }

      if (configType == 'all' || configType == 'fov') {
        final fovModel = Provider.of<FovModel>(context, listen: false);
        result['fov'] = fovModel.toJson();
      }

      if (configType == 'all' || configType == 'aim') {
        final aimModel = Provider.of<AimModel>(context, listen: false);
        result['aim'] = aimModel.toJson();
      }

      if (configType == 'all' || configType == 'fire') {
        final fireModel = Provider.of<FireModel>(context, listen: false);
        result['fire'] = fireModel.toJson();
      }

      if (configType == 'all' || configType == 'data_collection') {
        final dataCollectionModel = Provider.of<DataCollectionModel>(context, listen: false);
        result['data_collection'] = dataCollectionModel.toJson();
      }

      // 添加游戏信息（不包含敏感的用户信息）
      result['metadata'] = {
        'username': '[FILTERED]', // 过滤用户名
        'currentGame': _gameModel.currentGame,
        'exportTime': DateTime.now().toIso8601String(),
      };

    } catch (e, stackTrace) {
      _logger.e(_tag, '收集配置数据时出错', e, stackTrace);
    }

    // 最后一步：对整个结果进行敏感信息过滤
    _filterSensitiveDataInAllModules(result);
    _logger.i(_tag, '已完成所有配置数据的敏感信息过滤');

    return result;
  }
  
  /// 显示导入预览对话框
  Future<bool> _showImportPreviewDialog(BuildContext context, Map<String, dynamic> importData) async {
    final configInfo = _configModel.getConfigFileInfo(importData);
    
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导入配置预览'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('配置类型: ${configInfo['configTypeName']}'),
            Text('版本: ${configInfo['version']}'),
            Text('导出时间: ${configInfo['exportTime']}'),
            const SizedBox(height: 16),
            const Text(
              '确定要导入此配置吗？这将覆盖当前的相关设置。',
              style: TextStyle(color: Colors.orange),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定导入'),
          ),
        ],
      ),
    ) ?? false;
  }

  
  /// 清除历史记录
  void clearHistory() {
    _configModel.clearHistory();
    _logger.i(_tag, '已清除配置管理历史记录');
  }

  /// 显示错误消息 - 使用多种方式确保在不同平台都能显示
  void _showErrorMessage(BuildContext context, String message) {
    try {
      // 方式1：尝试使用标准Toast
      MessageComponent.showToast(
        context: context,
        message: message,
        type: MessageType.error,
        duration: const Duration(seconds: 5),
      );
    } catch (e) {
      _logger.w(_tag, 'Toast显示失败，尝试SnackBar', e);
      try {
        // 方式2：使用SnackBar作为备用
        MessageComponent.showSnackBar(
          context: context,
          message: message,
          type: MessageType.error,
          duration: const Duration(seconds: 5),
        );
      } catch (e2) {
        _logger.e(_tag, '所有消息显示方式都失败', e2);
        // 方式3：最后的备用方案 - 直接使用ScaffoldMessenger
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    }
  }

  /// 显示成功消息 - 使用多种方式确保在不同平台都能显示
  void _showSuccessMessage(BuildContext context, String message) {
    try {
      // 方式1：尝试使用标准Toast
      MessageComponent.showToast(
        context: context,
        message: message,
        type: MessageType.success,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      _logger.w(_tag, 'Toast显示失败，尝试SnackBar', e);
      try {
        // 方式2：使用SnackBar作为备用
        MessageComponent.showSnackBar(
          context: context,
          message: message,
          type: MessageType.success,
          duration: const Duration(seconds: 3),
        );
      } catch (e2) {
        _logger.e(_tag, '所有消息显示方式都失败', e2);
        // 方式3：最后的备用方案 - 直接使用ScaffoldMessenger
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  /// 写入首页配置到服务器 - 更新本地Model并发送到服务器
  Future<bool> _writeHomeConfigToServer(Map<String, dynamic> homeData) async {
    try {
      _logger.i(_tag, '开始应用首页配置');

      if (!_serverService.isConnected) {
        _logger.e(_tag, '服务器未连接，无法写入首页配置');
        return false;
      }

      // 从导入的数据中提取内容
      // homeData的结构是: {"action": "home_modify", "content": {...}}
      _logger.i(_tag, '原始首页数据结构', homeData);

      final content = homeData['content'] ?? homeData;

      _logger.i(_tag, '提取的首页配置内容', {
        'gameName': content['gameName'],
        'heartbeatInterval': content['heartbeatInterval'],
        'content_keys': content.keys.toList(),
        'note': '已过滤敏感信息(username, cardKey)',
      });

      // 1. 先更新本地GameModel，立即触发UI更新
      String? updatedGameName;
      if (content['gameName'] != null) {
        final newGame = content['gameName'].toString();
        _logger.i(_tag, '更新本地游戏选择: ${_gameModel.currentGame} -> $newGame');
        await _gameModel.updateCurrentGame(newGame);
        updatedGameName = newGame;
      }

      // 注意：不导入cardKey，保持当前用户的卡密不变
      _logger.i(_tag, '跳过卡密导入，保持当前用户卡密: ${_gameModel.cardKey}');

      if (content['heartbeatInterval'] != null) {
        final newInterval = _parseIntValue(content['heartbeatInterval']);
        _logger.i(_tag, '更新本地心跳间隔: ${_gameModel.heartbeatInterval} -> $newInterval');
        await _gameModel.updateHeartbeatInterval(newInterval);
      }

      // 2. 构建首页修改请求发送到服务器，更新数据库
      // 注意：始终使用当前用户的身份信息，不使用导入数据中的username和cardKey
      final request = {
        'action': 'home_modify',
        'content': {
          'username': _authModel.username, // 使用当前用户名
          'gameName': content['gameName'] ?? _gameModel.currentGame,
          'cardKey': _gameModel.cardKey, // 使用当前用户的卡密
          'heartbeatInterval': content['heartbeatInterval'] ?? _gameModel.heartbeatInterval,
          'updatedAt': DateTime.now().toIso8601String(),
        }
      };

      _logger.i(_tag, '发送首页配置到服务器');
      _serverService.sendMessage(jsonEncode(request));

      await Future.delayed(const Duration(milliseconds: 200));

      // 3. 如果游戏名称有更新，触发UI更新
      if (updatedGameName != null) {
        _logger.i(_tag, '触发游戏选择UI更新: $updatedGameName');
        await _triggerGameSelectionUpdate(updatedGameName);
      }

      _logger.i(_tag, '首页配置已发送到服务器并更新本地Model');
      return true;
    } catch (e, stackTrace) {
      _logger.e(_tag, '应用首页配置时出错', e, stackTrace);
      return false;
    }
  }

  /// 解析整数值
  int _parseIntValue(dynamic value) {
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// 写入功能配置到服务器 - 使用与FunctionController相同的格式
  Future<bool> _writeFunctionConfigToServer(Map<String, dynamic> functionData) async {
    try {
      _logger.i(_tag, '开始写入功能配置到服务器');

      if (!_serverService.isConnected) {
        _logger.e(_tag, '服务器未连接，无法写入功能配置');
        return false;
      }

      // 构建与FunctionController._sendFunctionConfigToServer()相同格式的请求
      final configs = functionData['configs'] ?? [];
      final request = {
        'action': 'function_modify',
        'content': {
          'username': _authModel.username,
          'gameName': _gameModel.currentGame,
          'cardKey': _gameModel.cardKey,
          'configs': configs,
          'createdAt': functionData['createdAt'] ?? DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        }
      };

      _logger.i(_tag, '发送功能配置，配置数量: ${configs.length}');
      _serverService.sendMessage(jsonEncode(request));

      await Future.delayed(const Duration(milliseconds: 200));

      _logger.i(_tag, '功能配置已发送到服务器');
      return true;
    } catch (e, stackTrace) {
      _logger.e(_tag, '写入功能配置到服务器时出错', e, stackTrace);
      return false;
    }
  }



  /// 写入PID配置到服务器 - 使用与PidController相同的格式
  Future<bool> _writePidConfigToServer(Map<String, dynamic> pidData) async {
    try {
      _logger.i(_tag, '开始写入PID配置到服务器');

      if (!_serverService.isConnected) {
        _logger.e(_tag, '服务器未连接，无法写入PID配置');
        return false;
      }

      // 构建与PidController.buildPidModifyRequest()相同格式的请求
      final content = pidData['content'] ?? pidData;
      final request = {
        'action': 'pid_modify',
        'content': {
          'username': _authModel.username,
          'gameName': _gameModel.currentGame,
          'nearMoveFactor': content['nearMoveFactor'] ?? 5.0,
          'nearStabilizer': content['nearStabilizer'] ?? 5.0,
          'nearResponseRate': content['nearResponseRate'] ?? 5.0,
          'nearAssistZone': content['nearAssistZone'] ?? 5.0,
          'nearResponseDelay': content['nearResponseDelay'] ?? 5.0,
          'nearMaxAdjustment': content['nearMaxAdjustment'] ?? 5.0,
          'farFactor': content['farFactor'] ?? 5.0,
          'yAxisFactor': content['yAxisFactor'] ?? 5.0,
          'pidRandomFactor': content['pidRandomFactor'] ?? 5.0,
          'updatedAt': DateTime.now().toIso8601String(),
        }
      };

      _serverService.sendMessage(jsonEncode(request));
      await Future.delayed(const Duration(milliseconds: 200));

      _logger.i(_tag, 'PID配置已发送到服务器');
      return true;
    } catch (e, stackTrace) {
      _logger.e(_tag, '写入PID配置到服务器时出错', e, stackTrace);
      return false;
    }
  }

  /// 写入视野配置到服务器 - 使用与FovController相同的格式
  Future<bool> _writeFovConfigToServer(Map<String, dynamic> fovData) async {
    try {
      _logger.i(_tag, '开始写入视野配置到服务器');

      if (!_serverService.isConnected) {
        _logger.e(_tag, '服务器未连接，无法写入视野配置');
        return false;
      }

      // 构建与FovModel.toJson()相同格式的请求
      final content = fovData['content'] ?? fovData;
      final request = {
        'action': 'fov_modify',
        'content': {
          'username': _authModel.username,
          'gameName': _gameModel.currentGame,
          'fov': content['fov'] ?? 90.0,
          'fovTime': content['fovTime'] ?? 100,
          'createdAt': content['createdAt'] ?? DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        }
      };

      _serverService.sendMessage(jsonEncode(request));
      await Future.delayed(const Duration(milliseconds: 200));

      _logger.i(_tag, '视野配置已发送到服务器');
      return true;
    } catch (e, stackTrace) {
      _logger.e(_tag, '写入视野配置到服务器时出错', e, stackTrace);
      return false;
    }
  }

  /// 写入瞄准配置到服务器 - 使用与AimController相同的格式
  Future<bool> _writeAimConfigToServer(Map<String, dynamic> aimData) async {
    try {
      _logger.i(_tag, '开始写入瞄准配置到服务器');

      if (!_serverService.isConnected) {
        _logger.e(_tag, '服务器未连接，无法写入瞄准配置');
        return false;
      }

      // 构建与AimModel.toJson()相同格式的请求
      final content = aimData['content'] ?? aimData;
      final request = {
        'action': 'aim_modify',
        'content': {
          'username': _authModel.username,
          'gameName': _gameModel.currentGame,
          'aimRange': content['aimRange'] ?? 100,
          'trackRange': content['trackRange'] ?? 100,
          'headHeight': content['headHeight'] ?? 100,
          'neckHeight': content['neckHeight'] ?? 100,
          'chestHeight': content['chestHeight'] ?? 100,
          'headRangeX': content['headRangeX'] ?? 100,
          'headRangeY': content['headRangeY'] ?? 100,
          'neckRangeX': content['neckRangeX'] ?? 100,
          'neckRangeY': content['neckRangeY'] ?? 100,
          'chestRangeX': content['chestRangeX'] ?? 100,
          'chestRangeY': content['chestRangeY'] ?? 100,
          'createdAt': content['createdAt'] ?? DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        }
      };

      _serverService.sendMessage(jsonEncode(request));
      await Future.delayed(const Duration(milliseconds: 200));

      _logger.i(_tag, '瞄准配置已发送到服务器');
      return true;
    } catch (e, stackTrace) {
      _logger.e(_tag, '写入瞄准配置到服务器时出错', e, stackTrace);
      return false;
    }
  }

  /// 写入射击配置到服务器 - 使用与FireController相同的格式
  Future<bool> _writeFireConfigToServer(Map<String, dynamic> fireData) async {
    try {
      _logger.i(_tag, '开始写入射击配置到服务器');

      if (!_serverService.isConnected) {
        _logger.e(_tag, '服务器未连接，无法写入射击配置');
        return false;
      }

      // 构建与FireController.saveFireConfig()相同格式的请求
      final content = fireData['content'] ?? fireData;
      final request = {
        'action': 'fire_modify',
        'content': {
          'username': _authModel.username,
          'gameName': _gameModel.currentGame,
          'rifleSleep': content['rifleSleep'] ?? 100,
          'rifleInterval': content['rifleInterval'] ?? 200,
          'sniperSleep': content['sniperSleep'] ?? 300,
          'sniperInterval': content['sniperInterval'] ?? 500,
          'weaponSwitchSleep': content['weaponSwitchSleep'] ?? 50,
          'updatedAt': DateTime.now().toIso8601String(),
        }
      };

      _serverService.sendMessage(jsonEncode(request));
      await Future.delayed(const Duration(milliseconds: 200));

      _logger.i(_tag, '射击配置已发送到服务器');
      return true;
    } catch (e, stackTrace) {
      _logger.e(_tag, '写入射击配置到服务器时出错', e, stackTrace);
      return false;
    }
  }

  /// 写入数据收集配置到服务器 - 使用与DataCollectionController相同的格式
  Future<bool> _writeDataCollectionConfigToServer(Map<String, dynamic> dataData) async {
    try {
      _logger.i(_tag, '开始写入数据收集配置到服务器');

      if (!_serverService.isConnected) {
        _logger.e(_tag, '服务器未连接，无法写入数据收集配置');
        return false;
      }

      // 数据收集配置直接发送，与DataCollectionController._sendSettingsToServer()相同
      _serverService.sendMessage(jsonEncode(dataData));
      await Future.delayed(const Duration(milliseconds: 200));

      _logger.i(_tag, '数据收集配置已发送到服务器');
      return true;
    } catch (e, stackTrace) {
      _logger.e(_tag, '写入数据收集配置到服务器时出错', e, stackTrace);
      return false;
    }
  }

  /// 清空文件名输入框
  void clearFileName() {
    _fileNameController.clear();
    notifyListeners();
  }

  /// 设置文件名
  void setFileName(String fileName) {
    _fileNameController.text = fileName;
    notifyListeners();
  }

  /// 处理文件名输入变化
  void onFileNameChanged(String value) {
    notifyListeners();
  }

  /// 生成默认文件名
  String generateDefaultFileName(String configType) {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final timeStr = '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';

    final typeName = ConfigManagementModel.configTypes[configType] ?? configType;
    return 'BlWeb_${typeName}_${dateStr}_$timeStr';
  }

  /// 导出配置到剪切板
  Future<void> exportToClipboard(BuildContext context, String configType) async {
    try {
      _logger.i(_tag, '开始导出配置到剪切板', {'configType': configType});

      // 收集配置数据
      final configData = await _collectConfigData(context, configType);
      if (configData.isEmpty) {
        if (context.mounted) {
          MessageComponent.showToast(
            context: context,
            message: '没有可导出的配置数据',
            type: MessageType.error,
          );
        }
        return;
      }

      // 转换为JSON字符串
      final jsonString = jsonEncode(configData);

      // 复制到剪切板 - 智能环境检测
      bool copySuccess = false;

      if (WebEnvironmentConfig.shouldUseManualClipboard) {
        _logger.i(_tag, '检测到${WebEnvironmentConfig.environmentDescription}，使用手动复制方式');
        copySuccess = await _setClipboardDataWeb(context, jsonString);
      } else {
        try {
          await Clipboard.setData(ClipboardData(text: jsonString));
          copySuccess = true;
          _logger.i(_tag, '使用${WebEnvironmentConfig.clipboardSupportDescription}复制成功');
        } catch (e) {
          _logger.e(_tag, '自动复制失败，降级到手动方式', e);
          copySuccess = await _setClipboardDataWeb(context, jsonString);
        }
      }

      if (context.mounted) {
        if (copySuccess) {
          _showSuccessMessage(context, '配置已复制到剪切板！');
        } else {
          _showErrorMessage(context, '复制到剪切板失败，请手动复制');
        }
      }

      _logger.i(_tag, '配置导出到剪切板成功');

    } catch (e, stackTrace) {
      _logger.e(_tag, '导出配置到剪切板时出错', e, stackTrace);
      if (context.mounted) {
        MessageComponent.showToast(
          context: context,
          message: '导出到剪切板失败: ${e.toString()}',
          type: MessageType.error,
        );
      }
    }
  }

  /// 从剪切板导入配置
  Future<void> importFromClipboard(BuildContext context) async {
    try {
      _logger.i(_tag, '开始从剪切板导入配置');

      // 智能环境检测和剪切板访问
      String? clipboardText;

      if (WebEnvironmentConfig.shouldUseManualClipboard) {
        _logger.i(_tag, '检测到${WebEnvironmentConfig.environmentDescription}，使用手动粘贴方式');
        clipboardText = await _getClipboardDataWeb(context);
      } else {
        try {
          // 尝试使用自动方式
          final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
          clipboardText = clipboardData?.text;
          _logger.i(_tag, '使用${WebEnvironmentConfig.clipboardSupportDescription}读取成功');
        } catch (e) {
          _logger.e(_tag, '自动读取失败，降级到手动方式', e);
          clipboardText = await _getClipboardDataWeb(context);
        }
      }

      if (clipboardText == null || clipboardText.isEmpty) {
        if (context.mounted) {
          _showErrorMessage(context, '剪切板为空或无有效数据');
        }
        return;
      }

      // 解析JSON数据
      Map<String, dynamic> configData;
      try {
        configData = jsonDecode(clipboardText) as Map<String, dynamic>;
      } catch (e) {
        if (context.mounted) {
          _showErrorMessage(context, '剪切板数据格式无效，请确保是有效的JSON配置');
        }
        return;
      }

      // 验证配置数据格式
      if (!_isValidConfigData(configData)) {
        if (context.mounted) {
          MessageComponent.showToast(
            context: context,
            message: '配置数据格式不正确，请检查数据来源',
            type: MessageType.error,
          );
        }
        return;
      }

      // 应用配置数据
      final success = await _applyClipboardConfigData(context, configData);

      if (success) {
        if (context.mounted) {
          MessageComponent.showToast(
            context: context,
            message: '从剪切板导入配置成功！',
            type: MessageType.success,
          );
        }
        _logger.i(_tag, '从剪切板导入配置成功');
      } else {
        if (context.mounted) {
          MessageComponent.showToast(
            context: context,
            message: '导入配置失败，请检查配置数据',
            type: MessageType.error,
          );
        }
      }

    } catch (e, stackTrace) {
      _logger.e(_tag, '从剪切板导入配置时出错', e, stackTrace);
      if (context.mounted) {
        MessageComponent.showToast(
          context: context,
          message: '从剪切板导入失败: ${e.toString()}',
          type: MessageType.error,
        );
      }
    }
  }

  /// 应用剪切板配置数据
  Future<bool> _applyClipboardConfigData(BuildContext context, Map<String, dynamic> configData) async {
    try {
      bool hasSuccess = false;

      // 应用各个模块的配置
      for (final entry in configData.entries) {
        final moduleType = entry.key;
        final moduleData = entry.value as Map<String, dynamic>?;

        if (moduleData == null) continue;

        switch (moduleType) {
          case 'home':
            if (await _writeHomeConfigToServer(moduleData)) {
              hasSuccess = true;
            }
            break;
          case 'function':
            if (await _writeFunctionConfigToServer(moduleData)) {
              hasSuccess = true;
            }
            break;
          case 'pid':
            if (await _writePidConfigToServer(moduleData)) {
              hasSuccess = true;
            }
            break;
          case 'fov':
            if (await _writeFovConfigToServer(moduleData)) {
              hasSuccess = true;
            }
            break;
          case 'aim':
            if (await _writeAimConfigToServer(moduleData)) {
              hasSuccess = true;
            }
            break;
          case 'fire':
            if (await _writeFireConfigToServer(moduleData)) {
              hasSuccess = true;
            }
            break;
          case 'data_collection':
            if (await _writeDataCollectionConfigToServer(moduleData)) {
              hasSuccess = true;
            }
            break;
        }
      }

      return hasSuccess;
    } catch (e, stackTrace) {
      _logger.e(_tag, '应用剪切板配置数据时出错', e, stackTrace);
      return false;
    }
  }

  /// 过滤敏感的home配置数据
  Map<String, dynamic> _filterSensitiveHomeData(Map<String, dynamic> homeData) {
    // 创建副本以避免修改原始数据
    final filteredData = Map<String, dynamic>.from(homeData);

    // 处理顶层的敏感信息
    filteredData.remove('username');
    filteredData.remove('cardKey');

    // 处理嵌套在content中的敏感信息
    if (filteredData.containsKey('content') && filteredData['content'] is Map<String, dynamic>) {
      final content = Map<String, dynamic>.from(filteredData['content'] as Map<String, dynamic>);

      // 移除或替换敏感信息
      if (content.containsKey('username')) {
        content['username'] = '[FILTERED]'; // 用占位符替换
        _logger.i(_tag, '已将username替换为占位符');
      }

      if (content.containsKey('cardKey')) {
        content['cardKey'] = '[FILTERED]'; // 用占位符替换
        _logger.i(_tag, '已将cardKey替换为占位符');
      }

      filteredData['content'] = content;
    }

    // 处理其他可能包含敏感信息的模块
    _filterSensitiveDataInAllModules(filteredData);

    _logger.i(_tag, '已过滤所有敏感信息：username, cardKey');
    return filteredData;
  }

  /// 过滤所有模块中的敏感信息
  void _filterSensitiveDataInAllModules(Map<String, dynamic> data) {
    final sensitiveFields = ['username', 'cardKey'];

    for (final entry in data.entries) {
      if (entry.value is Map<String, dynamic>) {
        final moduleData = entry.value as Map<String, dynamic>;

        // 检查模块顶层
        for (final field in sensitiveFields) {
          if (moduleData.containsKey(field)) {
            moduleData[field] = '[FILTERED]';
            _logger.i(_tag, '已过滤模块 ${entry.key} 中的 $field');
          }
        }

        // 检查content子对象
        if (moduleData.containsKey('content') && moduleData['content'] is Map<String, dynamic>) {
          final content = moduleData['content'] as Map<String, dynamic>;
          for (final field in sensitiveFields) {
            if (content.containsKey(field)) {
              content[field] = '[FILTERED]';
              _logger.i(_tag, '已过滤模块 ${entry.key}.content 中的 $field');
            }
          }
        }
      }
    }
  }



  /// 验证配置数据格式
  bool _isValidConfigData(Map<String, dynamic> data) {
    // 检查是否包含基本的配置字段
    final validKeys = ['home', 'function', 'pid', 'fov', 'aim', 'fire', 'data_collection'];
    return validKeys.any((key) => data.containsKey(key));
  }

  /// Web环境下设置剪切板数据 - 内网环境优化版本
  Future<bool> _setClipboardDataWeb(BuildContext context, String text) async {
    try {
      _logger.i(_tag, '${WebEnvironmentConfig.environmentDescription}：使用手动复制对话框');

      if (context.mounted) {
        return await _showCopyDialog(context, text);
      }

      return false;
    } catch (e) {
      _logger.e(_tag, 'Web环境：设置剪切板数据失败', e);
      return false;
    }
  }

  /// 显示手动复制对话框 - 内网环境优化版本
  Future<bool> _showCopyDialog(BuildContext context, String text) async {
    final TextEditingController copyController = TextEditingController(text: text);

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.content_copy, color: Colors.blue),
              SizedBox(width: 8),
              Text('复制配置数据'),
            ],
          ),
          content: SizedBox(
            width: WebClipboardConfig.copyDialogWidth,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '内网环境下需要手动复制配置数据。请选中下方文本框中的所有内容，然后按 Ctrl+C 复制。',
                          style: TextStyle(fontSize: 14, color: Colors.blue),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: copyController,
                  maxLines: WebClipboardConfig.textFieldMaxLines,
                  readOnly: true,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.all(12),
                    labelText: '配置数据（点击选中全部内容）',
                  ),
                  onTap: () {
                    // 点击时自动选中所有文本
                    copyController.selection = TextSelection(
                      baseOffset: 0,
                      extentOffset: copyController.text.length,
                    );
                  },
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.lightbulb_outline, color: Colors.orange, size: 16),
                      SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          '操作步骤：1. 点击文本框 → 2. 按 Ctrl+A 全选 → 3. 按 Ctrl+C 复制 → 4. 点击"已复制"',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('已复制'),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// Web环境下获取剪切板数据 - 内网环境优化版本
  Future<String?> _getClipboardDataWeb(BuildContext context) async {
    try {
      _logger.i(_tag, '${WebEnvironmentConfig.environmentDescription}：使用手动粘贴对话框');

      if (context.mounted) {
        return await _showPasteDialog(context);
      }

      return null;
    } catch (e) {
      _logger.e(_tag, 'Web环境：获取剪切板数据失败', e);
      return null;
    }
  }

  /// 显示手动粘贴对话框 - 内网环境优化版本
  Future<String?> _showPasteDialog(BuildContext context) async {
    final TextEditingController pasteController = TextEditingController();

    return await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Row(
                children: [
                  Icon(Icons.content_paste, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('粘贴配置数据'),
                ],
              ),
              content: SizedBox(
                width: WebClipboardConfig.pasteDialogWidth,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue, size: 20),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '内网环境下需要手动粘贴配置数据。请先复制配置数据，然后在下方文本框中按 Ctrl+V 粘贴。',
                              style: TextStyle(fontSize: 14, color: Colors.blue),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: pasteController,
                      maxLines: WebClipboardConfig.textFieldMaxLines,
                      decoration: const InputDecoration(
                        hintText: '请在此处粘贴配置数据（Ctrl+V）',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.all(12),
                        labelText: '配置数据',
                      ),
                      autofocus: true,
                      onChanged: (value) {
                        setState(() {}); // 更新按钮状态
                      },
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.lightbulb_outline, color: Colors.orange, size: 16),
                          SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              '操作步骤：1. 点击文本框 → 2. 按 Ctrl+V 粘贴 → 3. 检查数据完整性 → 4. 点击"确认导入"',
                              style: TextStyle(fontSize: 12, color: Colors.grey),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (pasteController.text.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.check_circle_outline, color: Colors.green, size: 16),
                            const SizedBox(width: 6),
                            Text(
                              '已检测到 ${pasteController.text.length} 个字符的配置数据',
                              style: const TextStyle(fontSize: 12, color: Colors.green),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(null),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: pasteController.text.trim().isEmpty
                      ? null
                      : () {
                          final text = pasteController.text.trim();
                          Navigator.of(context).pop(text);
                        },
                  child: const Text('确认导入'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _fileNameController.dispose();
    super.dispose();
  }

}
