// ignore_for_file: prefer_final_fields

import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'logger.dart';
import 'app_constants.dart';

/// 统一数据持久化管理器 - 标准化本地存储操作
/// 
/// 这个类提供了统一的SharedPreferences操作接口，包括类型安全的
/// 数据存储、批量操作、错误处理和日志记录功能。
class StorageManager {
  static final Logger _logger = Logger();
  static const String _logTag = AppConstants.LOG_TAG_APP;

  /// ========== 基础数据类型操作 ==========

  /// 保存字符串值
  static Future<bool> saveString(String key, String value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString(key, value);
      _logger.d(_logTag, '保存字符串', {'key': key, 'length': value.length});
      return result;
    } catch (e) {
      _logger.e(_logTag, '保存字符串失败', {'key': key, 'error': e.toString()});
      return false;
    }
  }

  /// 获取字符串值
  static Future<String?> getString(String key, {String? defaultValue}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getString(key) ?? defaultValue;
      _logger.d(_logTag, '获取字符串', {'key': key, 'hasValue': value != null});
      return value;
    } catch (e) {
      _logger.e(_logTag, '获取字符串失败', {'key': key, 'error': e.toString()});
      return defaultValue;
    }
  }

  /// 保存整数值
  static Future<bool> saveInt(String key, int value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setInt(key, value);
      _logger.d(_logTag, '保存整数', {'key': key, 'value': value});
      return result;
    } catch (e) {
      _logger.e(_logTag, '保存整数失败', {'key': key, 'error': e.toString()});
      return false;
    }
  }

  /// 获取整数值
  static Future<int?> getInt(String key, {int? defaultValue}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getInt(key) ?? defaultValue;
      _logger.d(_logTag, '获取整数', {'key': key, 'value': value});
      return value;
    } catch (e) {
      _logger.e(_logTag, '获取整数失败', {'key': key, 'error': e.toString()});
      return defaultValue;
    }
  }

  /// 保存双精度浮点数值
  static Future<bool> saveDouble(String key, double value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setDouble(key, value);
      _logger.d(_logTag, '保存浮点数', {'key': key, 'value': value});
      return result;
    } catch (e) {
      _logger.e(_logTag, '保存浮点数失败', {'key': key, 'error': e.toString()});
      return false;
    }
  }

  /// 获取双精度浮点数值
  static Future<double?> getDouble(String key, {double? defaultValue}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getDouble(key) ?? defaultValue;
      _logger.d(_logTag, '获取浮点数', {'key': key, 'value': value});
      return value;
    } catch (e) {
      _logger.e(_logTag, '获取浮点数失败', {'key': key, 'error': e.toString()});
      return defaultValue;
    }
  }

  /// 保存布尔值
  static Future<bool> saveBool(String key, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(key, value);
      _logger.d(_logTag, '保存布尔值', {'key': key, 'value': value});
      return result;
    } catch (e) {
      _logger.e(_logTag, '保存布尔值失败', {'key': key, 'error': e.toString()});
      return false;
    }
  }

  /// 获取布尔值
  static Future<bool?> getBool(String key, {bool? defaultValue}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getBool(key) ?? defaultValue;
      _logger.d(_logTag, '获取布尔值', {'key': key, 'value': value});
      return value;
    } catch (e) {
      _logger.e(_logTag, '获取布尔值失败', {'key': key, 'error': e.toString()});
      return defaultValue;
    }
  }

  /// 保存字符串列表
  static Future<bool> saveStringList(String key, List<String> value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setStringList(key, value);
      _logger.d(_logTag, '保存字符串列表', {'key': key, 'count': value.length});
      return result;
    } catch (e) {
      _logger.e(_logTag, '保存字符串列表失败', {'key': key, 'error': e.toString()});
      return false;
    }
  }

  /// 获取字符串列表
  static Future<List<String>?> getStringList(String key, {List<String>? defaultValue}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getStringList(key) ?? defaultValue;
      _logger.d(_logTag, '获取字符串列表', {'key': key, 'count': value?.length ?? 0});
      return value;
    } catch (e) {
      _logger.e(_logTag, '获取字符串列表失败', {'key': key, 'error': e.toString()});
      return defaultValue;
    }
  }

  /// ========== 复杂数据类型操作 ==========

  /// 保存JSON对象
  static Future<bool> saveJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await saveString(key, jsonString);
    } catch (e) {
      _logger.e(_logTag, '保存JSON失败', {'key': key, 'error': e.toString()});
      return false;
    }
  }

  /// 获取JSON对象
  static Future<Map<String, dynamic>?> getJson(String key) async {
    try {
      final jsonString = await getString(key);
      if (jsonString == null || jsonString.isEmpty) return null;
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      _logger.e(_logTag, '获取JSON失败', {'key': key, 'error': e.toString()});
      return null;
    }
  }

  /// 保存对象列表（转换为JSON）
  static Future<bool> saveObjectList(String key, List<Map<String, dynamic>> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await saveString(key, jsonString);
    } catch (e) {
      _logger.e(_logTag, '保存对象列表失败', {'key': key, 'error': e.toString()});
      return false;
    }
  }

  /// 获取对象列表
  static Future<List<Map<String, dynamic>>?> getObjectList(String key) async {
    try {
      final jsonString = await getString(key);
      if (jsonString == null || jsonString.isEmpty) return null;
      final List<dynamic> decoded = jsonDecode(jsonString);
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      _logger.e(_logTag, '获取对象列表失败', {'key': key, 'error': e.toString()});
      return null;
    }
  }

  /// ========== 批量操作 ==========

  /// 批量保存数据
  static Future<bool> saveBatch(Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      bool allSuccess = true;

      for (final entry in data.entries) {
        final key = entry.key;
        final value = entry.value;

        bool success = false;
        if (value is String) {
          success = await prefs.setString(key, value);
        } else if (value is int) {
          success = await prefs.setInt(key, value);
        } else if (value is double) {
          success = await prefs.setDouble(key, value);
        } else if (value is bool) {
          success = await prefs.setBool(key, value);
        } else if (value is List<String>) {
          success = await prefs.setStringList(key, value);
        } else {
          // 其他类型转为JSON字符串
          success = await prefs.setString(key, jsonEncode(value));
        }

        if (!success) {
          allSuccess = false;
          _logger.w(_logTag, '批量保存部分失败', {'key': key});
        }
      }

      _logger.i(_logTag, '批量保存完成', {'total': data.length, 'allSuccess': allSuccess});
      return allSuccess;
    } catch (e) {
      _logger.e(_logTag, '批量保存失败', {'error': e.toString()});
      return false;
    }
  }

  /// 批量获取数据
  static Future<Map<String, dynamic>> getBatch(List<String> keys) async {
    final result = <String, dynamic>{};
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      for (final key in keys) {
        if (prefs.containsKey(key)) {
          final value = prefs.get(key);
          result[key] = value;
        }
      }
      
      _logger.d(_logTag, '批量获取完成', {'requested': keys.length, 'found': result.length});
    } catch (e) {
      _logger.e(_logTag, '批量获取失败', {'error': e.toString()});
    }
    
    return result;
  }

  /// ========== 管理操作 ==========

  /// 删除指定键的值
  static Future<bool> remove(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.remove(key);
      _logger.d(_logTag, '删除键值', {'key': key, 'success': result});
      return result;
    } catch (e) {
      _logger.e(_logTag, '删除键值失败', {'key': key, 'error': e.toString()});
      return false;
    }
  }

  /// 批量删除
  static Future<bool> removeBatch(List<String> keys) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      bool allSuccess = true;
      
      for (final key in keys) {
        final success = await prefs.remove(key);
        if (!success) {
          allSuccess = false;
          _logger.w(_logTag, '批量删除部分失败', {'key': key});
        }
      }
      
      _logger.i(_logTag, '批量删除完成', {'total': keys.length, 'allSuccess': allSuccess});
      return allSuccess;
    } catch (e) {
      _logger.e(_logTag, '批量删除失败', {'error': e.toString()});
      return false;
    }
  }

  /// 清除所有数据
  static Future<bool> clear() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.clear();
      _logger.i(_logTag, '清除所有数据', {'success': result});
      return result;
    } catch (e) {
      _logger.e(_logTag, '清除所有数据失败', {'error': e.toString()});
      return false;
    }
  }

  /// 检查键是否存在
  static Future<bool> containsKey(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final exists = prefs.containsKey(key);
      _logger.d(_logTag, '检查键存在', {'key': key, 'exists': exists});
      return exists;
    } catch (e) {
      _logger.e(_logTag, '检查键存在失败', {'key': key, 'error': e.toString()});
      return false;
    }
  }

  /// 获取所有键
  static Future<Set<String>> getAllKeys() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      _logger.d(_logTag, '获取所有键', {'count': keys.length});
      return keys;
    } catch (e) {
      _logger.e(_logTag, '获取所有键失败', {'error': e.toString()});
      return <String>{};
    }
  }

  /// ========== 便捷方法 ==========

  /// 保存用户配置
  static Future<bool> saveUserConfig(String username, Map<String, dynamic> config) async {
    final key = '${AppConstants.STORAGE_PREFIX}user_config_$username';
    return await saveJson(key, config);
  }

  /// 获取用户配置
  static Future<Map<String, dynamic>?> getUserConfig(String username) async {
    final key = '${AppConstants.STORAGE_PREFIX}user_config_$username';
    return await getJson(key);
  }

  /// 保存游戏配置
  static Future<bool> saveGameConfig(String gameName, String configType, Map<String, dynamic> config) async {
    final key = '${AppConstants.STORAGE_PREFIX}${gameName}_${configType}_config';
    return await saveJson(key, config);
  }

  /// 获取游戏配置
  static Future<Map<String, dynamic>?> getGameConfig(String gameName, String configType) async {
    final key = '${AppConstants.STORAGE_PREFIX}${gameName}_${configType}_config';
    return await getJson(key);
  }

  /// 获取存储使用情况统计
  static Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final keys = await getAllKeys();
      final blwebKeys = keys.where((key) => key.startsWith(AppConstants.STORAGE_PREFIX)).toList();
      
      return {
        'totalKeys': keys.length,
        'blwebKeys': blwebKeys.length,
        'otherKeys': keys.length - blwebKeys.length,
        'blwebKeysList': blwebKeys,
      };
    } catch (e) {
      _logger.e(_logTag, '获取存储统计失败', {'error': e.toString()});
      return {};
    }
  }
}
