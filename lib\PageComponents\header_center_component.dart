// ignore_for_file: unnecessary_import, sized_box_for_whitespace

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/header_model.dart';

/// 页头中间组件常量配置
class HeaderCenterConstants {
  // 响应式断点
  static const int extraSmallBreakpoint = 360;
  static const int smallBreakpoint = 480;
  static const int mediumBreakpoint = 768;
  static const int largeBreakpoint = 1024;
  
  // 状态容器配置
  static const double statusContainerHeight = 42.0;
  
  // 间距配置
  static const double arrowSpacing = 6.0;
  static const double indicatorSpacing = 8.0;
  
  // 按钮配置
  static const double arrowButtonSize = 16.0;
  static const double arrowIconSize = 12.0;
  static const double refreshButtonSize = 28.0;
  static const double refreshIconSize = 20.0;
  
  // 页面指示器配置
  static const double indicatorDotSize = 4.0;
  static const double indicatorDotSpacing = 6.0;
  
  // 分页配置
  static const int statusItemsPerPage = 4;
  static const int performanceItemsPerPage = 2;
}

/// 状态项数据模型
class StatusItemData {
  final String label;
  final bool isNormal;
  final String? value;
  
  const StatusItemData({
    required this.label,
    required this.isNormal,
    this.value,
  });
}

/// 状态项容器配置模型
class StatusContainerConfig {
  final double iconSize;
  final double itemSpacing;
  final double padding;
  final EdgeInsets containerPadding;
  
  const StatusContainerConfig({
    required this.iconSize,
    required this.itemSpacing,
    required this.padding,
    required this.containerPadding,
  });
}

/// 状态容器配置预设
class StatusContainerPresets {
  static const StatusContainerConfig extraSmall = StatusContainerConfig(
    iconSize: 20.0,
    itemSpacing: 6.0,
    padding: 4.0,
    containerPadding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
  );
  
  static const StatusContainerConfig small = StatusContainerConfig(
    iconSize: 22.0,
    itemSpacing: 8.0,
    padding: 4.5,
    containerPadding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.5),
  );
  
  static const StatusContainerConfig medium = StatusContainerConfig(
    iconSize: 24.0,
    itemSpacing: 10.0,
    padding: 5.0,
    containerPadding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 7.0),
  );
  
  static const StatusContainerConfig large = StatusContainerConfig(
    iconSize: 26.0,
    itemSpacing: 12.0,
    padding: 5.5,
    containerPadding: EdgeInsets.symmetric(horizontal: 14.0, vertical: 7.5),
  );
  
  static const StatusContainerConfig extraLarge = StatusContainerConfig(
    iconSize: 28.0,
    itemSpacing: 14.0,
    padding: 6.0,
    containerPadding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
  );
}

/// 页头中间组件 - 主入口组件
class HeaderCenterComponent extends StatelessWidget {
  final bool showStatusInfo;
  final VoidCallback? onRefreshStatus;
  
  const HeaderCenterComponent({
    super.key,
    required this.showStatusInfo,
    this.onRefreshStatus,
  });
  
  @override
  Widget build(BuildContext context) {
    if (!showStatusInfo) {
      return const SizedBox.shrink();
    }
    
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          height: HeaderCenterConstants.statusContainerHeight,
          width: constraints.maxWidth,
          child: StatusDisplayContainer(
            onRefreshStatus: onRefreshStatus,
          ),
        );
      },
    );
  }
}

/// 状态显示容器 - 负责状态项的显示和翻页逻辑
class StatusDisplayContainer extends StatefulWidget {
  final VoidCallback? onRefreshStatus;
  
  const StatusDisplayContainer({
    super.key,
    this.onRefreshStatus,
  });
  
  @override
  State<StatusDisplayContainer> createState() => _StatusDisplayContainerState();
}

class _StatusDisplayContainerState extends State<StatusDisplayContainer> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  static const int _totalPages = 2;
  StatusContainerConfig? _cachedConfig;
  double? _lastScreenWidth;
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer<HeaderModel>(
      builder: (context, headerModel, child) {
        final statusPages = StatusItemFactory.createStatusPages(headerModel);
        final config = _getConfigForScreenSize(context);
        
        return StatusLayoutBuilder.buildLayout(
          statusPages: statusPages,
          config: config,
          currentPage: _currentPage,
          totalPages: _totalPages,
          pageController: _pageController,
          onPageChanged: _handlePageChanged,
          onPreviousPage: _handlePreviousPage,
          onNextPage: _handleNextPage,
          onRefreshStatus: widget.onRefreshStatus,
        );
      },
    );
  }
  
  /// 获取屏幕尺寸对应的配置（带缓存优化）
  StatusContainerConfig _getConfigForScreenSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (_cachedConfig != null && _lastScreenWidth == screenWidth) {
      return _cachedConfig!;
    }
    
    StatusContainerConfig config;
    if (screenWidth < HeaderCenterConstants.extraSmallBreakpoint) {
      config = StatusContainerPresets.extraSmall;
    } else if (screenWidth < HeaderCenterConstants.smallBreakpoint) {
      config = StatusContainerPresets.small;
    } else if (screenWidth < HeaderCenterConstants.mediumBreakpoint) {
      config = StatusContainerPresets.medium;
    } else if (screenWidth < HeaderCenterConstants.largeBreakpoint) {
      config = StatusContainerPresets.large;
    } else {
      config = StatusContainerPresets.extraLarge;
    }
    
    _cachedConfig = config;
    _lastScreenWidth = screenWidth;
    
    return config;
  }
  
  void _handlePageChanged(int page) {
                    setState(() {
                      _currentPage = page;
                    });
  }
  
  void _handlePreviousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
  
  void _handleNextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
}

/// 状态项工厂 - 负责创建分页的状态项数据
class StatusItemFactory {
  static List<List<StatusItemData>> createStatusPages(HeaderModel headerModel) {
    // 第一页：4个状态图标
    final statusPage = [
      StatusItemData(
        label: '数据库',
        isNormal: headerModel.dbStatus,
      ),
      StatusItemData(
        label: '推理服务',
        isNormal: headerModel.inferenceStatus,
      ),
      StatusItemData(
        label: '卡密',
        isNormal: headerModel.cardKeyStatus,
      ),
      StatusItemData(
        label: '键鼠',
        isNormal: headerModel.keyMouseStatus,
      ),
    ];
    
    // 第二页：2个性能指标
    final performancePage = [
      StatusItemData(
        label: '帧率',
        isNormal: headerModel.frameRate > 0,
        value: headerModel.frameRate > 0 ? '${headerModel.frameRate}fps' : '未知',
      ),
      StatusItemData(
        label: '分辨率',
        isNormal: headerModel.width > 0 && headerModel.height > 0,
        value: headerModel.width > 0 && headerModel.height > 0 
            ? '${headerModel.width}x${headerModel.height}' 
            : '未知',
      ),
    ];
    
    return [statusPage, performancePage];
  }
}

/// 状态布局构建器 - 负责构建UI布局
class StatusLayoutBuilder {
  static Widget buildLayout({
    required List<List<StatusItemData>> statusPages,
    required StatusContainerConfig config,
    required int currentPage,
    required int totalPages,
    required PageController pageController,
    required ValueChanged<int> onPageChanged,
    required VoidCallback onPreviousPage,
    required VoidCallback onNextPage,
    VoidCallback? onRefreshStatus,
  }) {
    return Container(
      decoration: _buildContainerDecoration(),
      child: Padding(
        padding: config.containerPadding,
        child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 左箭头
                ArrowButtonWidget(
                  icon: Icons.chevron_left,
                  onPressed: currentPage > 0 ? onPreviousPage : null,
                ),
                
                SizedBox(width: HeaderCenterConstants.arrowSpacing),
                
                // 状态项内容区域
                Expanded(
                  child: StatusContentArea(
                    statusPages: statusPages,
                    config: config,
                    totalPages: totalPages,
                    pageController: pageController,
                    onPageChanged: onPageChanged,
                  ),
                ),
                
                SizedBox(width: HeaderCenterConstants.arrowSpacing),
                
                // 右箭头
                ArrowButtonWidget(
                  icon: Icons.chevron_right,
                  onPressed: currentPage < totalPages - 1 ? onNextPage : null,
                ),
                
            // 刷新状态按钮
                if (onRefreshStatus != null) ...[
                  SizedBox(width: HeaderCenterConstants.arrowSpacing),
              RefreshStatusButtonWidget(onPressed: onRefreshStatus),
                ],
                
                // 页面指示器
                if (totalPages > 1) ...[
                  SizedBox(width: HeaderCenterConstants.indicatorSpacing),
                  PageIndicatorWidget(
                    currentPage: currentPage,
                    totalPages: totalPages,
                  ),
                ],
              ],
        ),
      ),
    );
  }
  
  static BoxDecoration _buildContainerDecoration() {
    return BoxDecoration(
      border: Border.all(
        color: Colors.grey.withValues(alpha: 0.3),
        width: 1.0,
      ),
      borderRadius: BorderRadius.circular(4.0),
      color: Colors.grey.withValues(alpha: 0.05),
    );
  }
}

/// 箭头按钮组件
class ArrowButtonWidget extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  
  const ArrowButtonWidget({
    super.key,
    required this.icon,
    required this.onPressed,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: HeaderCenterConstants.arrowButtonSize,
      height: HeaderCenterConstants.arrowButtonSize,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(6),
          child: Icon(
            icon,
            size: HeaderCenterConstants.arrowIconSize,
            color: onPressed != null ? Colors.grey.shade600 : Colors.grey.shade300,
          ),
        ),
      ),
    );
  }
}

/// 状态内容区域组件
class StatusContentArea extends StatelessWidget {
  final List<List<StatusItemData>> statusPages;
  final StatusContainerConfig config;
  final int totalPages;
  final PageController pageController;
  final ValueChanged<int> onPageChanged;
  
  const StatusContentArea({
    super.key,
    required this.statusPages,
    required this.config,
    required this.totalPages,
    required this.pageController,
    required this.onPageChanged,
  });
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: HeaderCenterConstants.statusContainerHeight - 4.0,
      child: PageView.builder(
        controller: pageController,
        onPageChanged: onPageChanged,
        itemCount: totalPages,
        itemBuilder: (context, pageIndex) {
          return StatusPageContent(
            statusItems: statusPages[pageIndex],
            config: config,
            pageIndex: pageIndex,
          );
        },
      ),
    );
  }
}

/// 状态页面内容组件
class StatusPageContent extends StatelessWidget {
  final List<StatusItemData> statusItems;
  final StatusContainerConfig config;
  final int pageIndex;
  
  const StatusPageContent({
    super.key,
    required this.statusItems,
    required this.config,
    required this.pageIndex,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: HeaderCenterConstants.statusContainerHeight - 4.0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: pageIndex == 1 
          ? _buildPerformanceTextItems()
          : _buildStatusIconItems(),
      ),
    );
  }
  
  /// 构建性能指标文字项目（第二页）
  List<Widget> _buildPerformanceTextItems() {
    final widgets = <Widget>[];
    
    for (int i = 0; i < statusItems.length; i++) {
      widgets.add(PerformanceTextWidget(
        item: statusItems[i],
        config: config,
      ));
      
      if (i < statusItems.length - 1) {
        widgets.add(SizedBox(width: config.itemSpacing * 2.0));
      }
    }
    
    return widgets;
  }
  
  /// 构建状态图标项目（第一页）
  List<Widget> _buildStatusIconItems() {
    final widgets = <Widget>[];
    
    for (int i = 0; i < statusItems.length; i++) {
      widgets.add(StatusItemWidget(
        item: statusItems[i],
        config: config,
      ));
      
      if (i < statusItems.length - 1) {
        widgets.add(SizedBox(width: config.itemSpacing));
      }
    }
    
    return widgets;
  }
}

/// 性能指标文字组件 - 用于第二页显示具体数值
class PerformanceTextWidget extends StatelessWidget {
  final StatusItemData item;
  final StatusContainerConfig config;
  
  const PerformanceTextWidget({
    super.key,
    required this.item,
    required this.config,
  });
  
  @override
  Widget build(BuildContext context) {
    final color = item.isNormal ? Colors.green : Colors.red;
    final displayValue = item.value ?? '未知';
    
    return Tooltip(
      message: _buildTooltipMessage(),
      child: Container(
        height: HeaderCenterConstants.statusContainerHeight - 8.0,
        padding: EdgeInsets.symmetric(
          horizontal: config.padding * 2.0,
          vertical: config.padding * 0.8,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(6.0),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1.2,
          ),
        ),
        child: Center(
          child: Text(
            displayValue,
            style: TextStyle(
              fontSize: _calculateFontSize(),
              fontWeight: FontWeight.w700,
              color: color,
              height: 1.0,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }
  
  double _calculateFontSize() {
    final baseSize = config.iconSize * 0.65;
    return baseSize.clamp(10.0, 16.0);
  }
  
  String _buildTooltipMessage() {
    final status = item.isNormal ? "正常" : "异常";
    return '${item.label}: $status (${item.value ?? "未知"})';
  }
}

/// 状态项组件 - 仅通过颜色显示状态
class StatusItemWidget extends StatelessWidget {
  final StatusItemData item;
  final StatusContainerConfig config;
  
  const StatusItemWidget({
    super.key,
    required this.item,
    required this.config,
  });
  
  @override
  Widget build(BuildContext context) {
    final color = item.isNormal ? Colors.green : Colors.red;
    
    return Tooltip(
      message: _buildTooltipMessage(),
      child: Container(
        width: config.iconSize + (config.padding * 2),
        height: config.iconSize + (config.padding * 2),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Center(
          child: Icon(
            StatusIconMapper.getIcon(item.label),
            color: color,
            size: config.iconSize,
          ),
        ),
      ),
    );
  }
  
  String _buildTooltipMessage() {
    final status = item.isNormal ? "正常" : "异常";
    final value = item.value != null ? " (${item.value})" : "";
    return '${item.label}: $status$value';
  }
}

/// 页面指示器组件
class PageIndicatorWidget extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  
  const PageIndicatorWidget({
    super.key,
    required this.currentPage,
    required this.totalPages,
  });
  
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(totalPages, (index) {
        return Container(
          margin: EdgeInsets.symmetric(
            horizontal: HeaderCenterConstants.indicatorDotSpacing / 2,
          ),
          width: HeaderCenterConstants.indicatorDotSize,
          height: HeaderCenterConstants.indicatorDotSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: index == currentPage 
                ? Colors.blue.shade600 
                : Colors.grey.shade400,
          ),
        );
      }),
    );
  }
}

/// 状态图标映射器
class StatusIconMapper {
  static const Map<String, IconData> _iconMap = {
    '数据库': Icons.storage,
    '推理服务': Icons.psychology,
    '卡密': Icons.key,
    '键鼠': Icons.mouse,
    '帧率': Icons.speed,
    '分辨率': Icons.aspect_ratio,
  };
  
  static IconData getIcon(String label) {
    return _iconMap[label] ?? Icons.check_circle;
  }
}

/// 刷新状态按钮组件
class RefreshStatusButtonWidget extends StatelessWidget {
  final VoidCallback onPressed;
  
  const RefreshStatusButtonWidget({
    super.key,
    required this.onPressed,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: HeaderCenterConstants.refreshButtonSize,
      height: HeaderCenterConstants.refreshButtonSize,
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
          width: 1.0,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(6),
          splashColor: Colors.blue.withValues(alpha: 0.2),
          highlightColor: Colors.blue.withValues(alpha: 0.1),
          child: Center(
            child: Icon(
              Icons.refresh,
              size: HeaderCenterConstants.refreshIconSize,
              color: Colors.blue.shade600,
            ),
          ),
        ),
      ),
    );
  }
} 