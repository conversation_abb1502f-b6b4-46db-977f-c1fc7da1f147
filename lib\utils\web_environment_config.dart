/// Web环境配置工具
/// 专门处理内网环境下的Web应用配置和兼容性问题

import 'package:flutter/foundation.dart';

/// Web环境配置类
class WebEnvironmentConfig {
  /// 是否为内网环境
  /// 在内网环境下，剪切板API通常不可用，需要使用手动方式
  static bool get isIntranetEnvironment {
    if (!kIsWeb) return false;
    
    // 检查当前URL是否为内网地址
    try {
      final currentUrl = Uri.base.toString();
      
      // 内网地址模式
      final intranetPatterns = [
        RegExp(r'^https?://localhost'),
        RegExp(r'^https?://127\.0\.0\.1'),
        RegExp(r'^https?://192\.168\.'),
        RegExp(r'^https?://10\.'),
        RegExp(r'^https?://172\.(1[6-9]|2[0-9]|3[0-1])\.'),
        RegExp(r'^https?://[^.]+$'), // 单个主机名（无域名）
        RegExp(r'^https?://.*\.local'),
        RegExp(r'^https?://.*\.lan'),
      ];
      
      return intranetPatterns.any((pattern) => pattern.hasMatch(currentUrl));
    } catch (e) {
      // 如果无法获取URL，默认认为是内网环境
      return true;
    }
  }
  
  /// 是否应该强制使用手动剪切板操作
  /// 在内网环境或HTTP协议下，强制使用手动方式
  static bool get shouldUseManualClipboard {
    if (!kIsWeb) return false;
    
    try {
      final currentUrl = Uri.base.toString();
      
      // HTTP协议下剪切板API不可用
      if (currentUrl.startsWith('http://')) {
        return true;
      }
      
      // 内网环境下通常剪切板API受限
      if (isIntranetEnvironment) {
        return true;
      }
      
      return false;
    } catch (e) {
      // 无法判断时，默认使用手动方式
      return true;
    }
  }
  
  /// 获取环境描述信息
  static String get environmentDescription {
    if (!kIsWeb) return '原生应用环境';
    
    try {
      final currentUrl = Uri.base.toString();
      
      if (currentUrl.startsWith('https://')) {
        if (isIntranetEnvironment) {
          return 'HTTPS内网环境';
        } else {
          return 'HTTPS公网环境';
        }
      } else if (currentUrl.startsWith('http://')) {
        return 'HTTP环境（不安全）';
      } else {
        return '未知Web环境';
      }
    } catch (e) {
      return 'Web环境（无法检测）';
    }
  }
  
  /// 获取剪切板支持状态描述
  static String get clipboardSupportDescription {
    if (!kIsWeb) return '原生剪切板API';
    
    if (shouldUseManualClipboard) {
      return '手动剪切板操作（${environmentDescription}）';
    } else {
      return '浏览器剪切板API';
    }
  }
  
  /// 检查是否支持自动剪切板操作
  static bool get supportsAutomaticClipboard {
    return !shouldUseManualClipboard;
  }
  
  /// 获取推荐的操作提示
  static String get recommendedAction {
    if (!kIsWeb) {
      return '使用原生剪切板功能';
    }
    
    if (shouldUseManualClipboard) {
      return '由于${environmentDescription}限制，建议使用手动复制粘贴操作';
    } else {
      return '可以使用自动剪切板功能';
    }
  }
}

/// Web环境剪切板配置
class WebClipboardConfig {
  /// 复制对话框的默认宽度
  static const double copyDialogWidth = 600.0;
  
  /// 粘贴对话框的默认宽度
  static const double pasteDialogWidth = 600.0;
  
  /// 文本框的默认行数
  static const int textFieldMaxLines = 10;
  
  /// 是否显示详细的操作指导
  static const bool showDetailedInstructions = true;
  
  /// 是否自动选中文本
  static const bool autoSelectText = true;
  
  /// 操作超时时间（秒）
  static const int operationTimeoutSeconds = 300; // 5分钟
}
