// ignore_for_file: deprecated_member_use

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

/// 键盘事件处理器 - 解决Windows平台键盘事件错误
class KeyboardHandler {
  static const String _logTag = 'KeyboardHandler';
  
  /// 初始化键盘事件处理
  static void initialize() {
    if (kDebugMode) {
      print('[$_logTag] 初始化键盘事件处理器');
    }
    
    // 设置全局键盘事件处理
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupKeyboardEventHandling();
    });
  }
  
  /// 设置键盘事件处理
  static void _setupKeyboardEventHandling() {
    try {
      // 重写RawKeyboard的事件处理以避免断言错误
      RawKeyboard.instance.addListener(_handleRawKeyEvent);
      
      if (kDebugMode) {
        print('[$_logTag] 键盘事件监听器已设置');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$_logTag] 设置键盘事件处理失败: $e');
      }
    }
  }
  
  /// 处理原始键盘事件
  static void _handleRawKeyEvent(RawKeyEvent event) {
    try {
      // 静默处理键盘事件，避免断言错误
      if (event is RawKeyDownEvent) {
        // 处理按键按下事件
        _handleKeyDown(event);
      } else if (event is RawKeyUpEvent) {
        // 处理按键释放事件
        _handleKeyUp(event);
      }
    } catch (e) {
      // 静默捕获所有键盘事件错误
      if (kDebugMode) {
        print('[$_logTag] 键盘事件处理错误: $e');
      }
    }
  }
  
  /// 处理按键按下事件
  static void _handleKeyDown(RawKeyDownEvent event) {
    // 这里可以添加自定义的按键处理逻辑
    // 目前只是静默处理以避免错误
  }
  
  /// 处理按键释放事件
  static void _handleKeyUp(RawKeyUpEvent event) {
    // 这里可以添加自定义的按键处理逻辑
    // 目前只是静默处理以避免错误
  }
  
  /// 清理键盘事件处理
  static void dispose() {
    try {
      RawKeyboard.instance.removeListener(_handleRawKeyEvent);
      
      if (kDebugMode) {
        print('[$_logTag] 键盘事件监听器已移除');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[$_logTag] 移除键盘事件监听器失败: $e');
      }
    }
  }
}

/// 键盘事件包装器Widget - 用于包装应用根组件
class KeyboardEventWrapper extends StatefulWidget {
  final Widget child;
  
  const KeyboardEventWrapper({
    Key? key,
    required this.child,
  }) : super(key: key);
  
  @override
  State<KeyboardEventWrapper> createState() => _KeyboardEventWrapperState();
}

class _KeyboardEventWrapperState extends State<KeyboardEventWrapper> {
  @override
  void initState() {
    super.initState();
    KeyboardHandler.initialize();
  }
  
  @override
  void dispose() {
    KeyboardHandler.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// 安全的键盘快捷键处理器
class SafeShortcuts extends StatelessWidget {
  final Map<ShortcutActivator, Intent> shortcuts;
  final Map<Type, Action<Intent>> actions;
  final Widget child;
  
  const SafeShortcuts({
    Key? key,
    required this.shortcuts,
    required this.actions,
    required this.child,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return CallbackShortcuts(
      bindings: _convertToCallbackShortcuts(shortcuts, actions),
      child: child,
    );
  }
  
  /// 转换为回调快捷键以避免RawKeyboard问题
  Map<ShortcutActivator, VoidCallback> _convertToCallbackShortcuts(
    Map<ShortcutActivator, Intent> shortcuts,
    Map<Type, Action<Intent>> actions,
  ) {
    final Map<ShortcutActivator, VoidCallback> callbacks = {};
    
    shortcuts.forEach((activator, intent) {
      final action = actions[intent.runtimeType];
      if (action != null) {
        callbacks[activator] = () {
          try {
            action.invoke(intent);
          } catch (e) {
            if (kDebugMode) {
              print('[SafeShortcuts] 快捷键执行错误: $e');
            }
          }
        };
      }
    });
    
    return callbacks;
  }
}
