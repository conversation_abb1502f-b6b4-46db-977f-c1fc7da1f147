// ignore_for_file: use_build_context_synchronously, unused_import, unused_field, depend_on_referenced_packages, unnecessary_import, prefer_final_fields, prefer_interpolation_to_compose_strings, unnecessary_string_escapes

import 'package:flutter/material.dart';
import '../models/data_collection_model.dart';
import '../models/auth_model.dart';
import '../models/game_model.dart';
import '../component/message_component.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;
import 'dart:convert';
import '../utils/blwebsocket.dart';
import '../services/server_service.dart';
import 'package:logging/logging.dart';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart' as app_logger;
import 'package:intl/intl.dart';

class DataCollectionController extends ChangeNotifier {
  // 常量定义
  static const int _requestTimeoutSeconds = 5;
  static const List<String> _availableHotkeys = ['左键', '右键', '中键', '前侧', '后侧', '无'];
  static const List<String> _availableTeams = ['警方', '匪方', '无'];
  static const Map<String, String> _teamSuffixes = {
    '警方': '_jf',
    '匪方': '_ff',
    '无': '_w',
  };

  // 依赖注入
  final DataCollectionModel _dataModel;
  final ServerService _serverService;
  final AuthModel _authModel;
  final GameModel _gameModel;
  final log = Logger('DataCollectionController');

  // 状态管理
  String _baseName = '';
  String _basePath = '';
  int _totalFiles = 0;
  Map<String, dynamic> _summary = {};
  String _fetchTimestamp = '';
  bool _hasFetchedData = false;
  List<Map<String, dynamic>> _collections = [];
  
  // 上传状态
  bool _isUploading = false;
  String _uploadStatus = '';
  Map<String, dynamic> _uploadDetails = {};
  String _uploadError = '';

  // 操作状态
  String _lastError = '';
  bool _isLoading = false;

  // Getters - 基础属性
  bool get isEnabled => _dataModel.isEnabled;
  String get mapHotkeyValue => _dataModel.mapHotkey;
  String get targetHotkeyValue => _dataModel.targetHotkey;
  String get collectionName => _dataModel.collectionName;
  String get baseName => _baseName;
  String get teamSide => _dataModel.teamSide;
  String get username => _authModel.username;
  String get gameName => _gameModel.currentGame;

  // Getters - 可用选项
  List<String> get availableHotkeys => _availableHotkeys;
  List<String> get availableTeams => _availableTeams;

  // Getters - 数据统计
  String get basePath => _basePath;
  int get totalFiles => _totalFiles;
  Map<String, dynamic> get summary => _summary;
  String get fetchTimestamp => _fetchTimestamp;
  bool get hasFetchedData => _hasFetchedData;
  List<Map<String, dynamic>> get collections => _collections;

  // Getters - 上传状态
  bool get isUploading => _isUploading;
  String get uploadStatus => _uploadStatus;
  Map<String, dynamic> get uploadDetails => _uploadDetails;
  String get uploadError => _uploadError;

  // Getters - 操作状态
  String get lastError => _lastError;
  bool get isLoading => _isLoading;

  // Getters - 计算属性
  int get totalImages => _extractCountFromSummary(['totalImages', 'total.images', 'images']);
  int get totalLabels => _extractCountFromSummary(['totalLabels', 'total.labels', 'labels']);
  int get mapImages => _extractCountFromCategory('maps', 'images');
  int get targetImages => _extractCountFromCategory('targets', 'images');

  // Setters
  set isEnabled(bool value) {
    _dataModel.isEnabled = value;
  }

  set mapHotkeyValue(String value) {
    if (_availableHotkeys.contains(value) && mapHotkeyValue != value) {
      _dataModel.mapHotkey = value;
    }
  }

  set targetHotkeyValue(String value) {
    if (_availableHotkeys.contains(value) && targetHotkeyValue != value) {
      _dataModel.targetHotkey = value;
    }
  }

  set collectionName(String value) {
    _baseName = value;
    app_logger.log.i('数据收集', '设置数据收集名称', value);
    
    if (_dataModel.collectionName != value) {
      _dataModel.collectionName = value;
    }
  }

  set teamSide(String value) {
    if (_availableTeams.contains(value) && teamSide != value) {
      _dataModel.teamSide = value;
      app_logger.log.i('数据收集', '阵营已更新', value);
    }
  }

  DataCollectionController({
    required DataCollectionModel dataCollectionModel,
    required ServerService serverService,
    required AuthModel authModel,
    required GameModel gameModel,
  }) : 
    _dataModel = dataCollectionModel,
    _serverService = serverService,
    _authModel = authModel,
    _gameModel = gameModel {
      _initializeController();
  }

  @override
  void dispose() {
    _dataModel.removeListener(_onModelChanged);
    _authModel.removeListener(_onAuthModelChanged);
    super.dispose();
  }

  // 公共方法
  String getTeamSuffix(String team) => _teamSuffixes[team] ?? '';

  String generateDefaultCollectionName() {
    final defaultName = _gameModel.currentGame;
    app_logger.log.i('数据收集', '生成默认名称', defaultName);
    _baseName = defaultName;
    return defaultName;
  }

  Future<void> saveSettings(BuildContext context) async {
    try {
      await _dataModel.saveSettings();
      
      final formattedName = _buildFormattedCollectionName();
      
      if (_serverService.isConnected) {
        await _sendSettingsToServer(formattedName);
        _showMessage(context, '设置已保存');
      } else {
        _showMessage(context, '设置已保存到本地，但未能同步到服务器(离线模式)');
      }
    } catch (e) {
      log.severe('保存数据收集设置时出错: $e');
      _showMessage(context, '保存设置失败: $e');
    }
  }

  void resetToDefaults() {
    _dataModel.resetToDefaults();
    _validateHotkeyValues();
    _extractBaseName();
    app_logger.log.i('数据收集', '重置所有设置为默认值');
  }

  Future<void> fetchData(BuildContext context) async {
    if (!_serverService.isConnected) {
      _showErrorMessage(context, 'WebSocket未连接，无法获取数据');
      return;
    }

    try {
      _setLoading(true);
      app_logger.log.i('数据收集', '开始获取数据');
      await _sendFetchRequest();
      _showInfoMessage(context, '正在获取数据，请稍候...');
    } catch (e) {
      _setLoading(false);
      log.severe('发送数据获取请求时出错: $e');
      _showErrorMessage(context, '获取数据失败: $e');
    }
  }

  Future<void> uploadData(BuildContext context) async {
    if (!_serverService.isConnected) {
      _showErrorMessage(context, 'WebSocket未连接，无法上传数据');
      return;
    }

    if (!_dataModel.isEnabled) {
      _showWarningMessage(context, '请先启用数据收集功能');
      return;
    }
    
    if (_isUploading) {
      _showWarningMessage(context, '已有上传任务在进行中，请稍候...');
      return;
    }

    try {
      _setUploadingState();
      app_logger.log.i('数据收集', '开始上传数据', '阵营: ${_dataModel.teamSide}');
      
      await _sendUploadRequest();
      _showInfoMessage(context, '正在上传数据，请稍候...');
      _setUploadTimeout(context);
    } catch (e) {
      _handleUploadError(e, context);
    }
  }

  Future<void> fetchDataSilently() async {
    if (!_serverService.isConnected) {
      log.warning('WebSocket未连接，无法静默获取数据');
      return;
    }

    try {
      app_logger.log.i('数据收集', '上传后静默获取数据');
      await _sendFetchRequest();
    } catch (e) {
      log.severe('静默获取数据时出错: $e');
    }
  }

  void clearUploadStatus() {
    _uploadStatus = '';
    _uploadError = '';
    _uploadDetails = {};
    _isUploading = false;
    notifyListeners();
    app_logger.log.i('数据收集', '清除上传状态', '已重置上传状态');
  }

  /// 清除错误状态
  void clearError() {
    _lastError = '';
    notifyListeners();
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _lastError = ''; // 开始加载时清除之前的错误
    }
    notifyListeners();
  }

  // 私有方法 - 初始化
  void _initializeController() {
    _initWebSocketListeners();
    _dataModel.addListener(_onModelChanged);
    _authModel.addListener(_onAuthModelChanged);
    _fetchSettingsFromServer();
    _validateHotkeyValues();
    _extractBaseName();
  }

  void _initWebSocketListeners() {
    _serverService.addMessageListener((dynamic message) {
      if (message is String) {
        _handleWebSocketMessage(message);
      }
    });
  }

  // 私有方法 - 事件处理
  void _onModelChanged() {
    notifyListeners();
  }

  void _onAuthModelChanged() {
    if (_baseName.isNotEmpty) {
      final fullName = _buildFullCollectionName(_baseName);
      if (collectionName != fullName) {
        _dataModel.collectionName = fullName;
        app_logger.log.i('数据收集', '用户名已更新', '新名称: $fullName');
        notifyListeners();
      }
    }
  }

  void _handleWebSocketMessage(String message) {
    try {
      final data = jsonDecode(message) as Map<String, dynamic>;
      final action = data['action'] ?? '';
      
      switch (action) {
        case 'data_collection_read_response':
        case 'data_collection_modify_response':
          _handleSettingsResponse(data);
          break;
        case 'data_collection_fetch_response':
          _handleFetchDataResponse(data);
          break;
        case 'data_collection_upload_response':
          _handleUploadDataResponse(data);
          break;
      }
    } catch (e) {
      log.warning('处理WebSocket消息时出错: $e');
    }
  }

  // 私有方法 - 数据处理
  int _extractCountFromSummary(List<String> paths) {
    for (final path in paths) {
      final value = _getNestedValue(_summary, path);
      if (value is int) return value;
    }
    return 0;
  }

  int _extractCountFromCategory(String category, String field) {
    if (!_summary.containsKey(category)) return 0;
    
    final categoryData = _summary[category];
    if (categoryData is Map && categoryData.containsKey(field)) {
      return categoryData[field] as int? ?? 0;
    } else if (categoryData is int) {
      return categoryData;
    }
    return 0;
  }

  dynamic _getNestedValue(Map<String, dynamic> map, String path) {
    final keys = path.split('.');
    dynamic current = map;
    
    for (final key in keys) {
      if (current is Map && current.containsKey(key)) {
        current = current[key];
      } else {
        return null;
      }
    }
    return current;
  }

  void _validateHotkeyValues() {
    if (!_availableHotkeys.contains(_dataModel.mapHotkey)) {
      _dataModel.mapHotkey = '右键';
    }
    
    if (!_availableHotkeys.contains(_dataModel.targetHotkey)) {
      _dataModel.targetHotkey = '前侧';
    }
    
    if (!_availableTeams.contains(_dataModel.teamSide)) {
      _dataModel.teamSide = '警方';
    }
  }

  void _extractBaseName() {
    final fullName = _dataModel.collectionName;
    String baseName = '';
    
    if (fullName.startsWith(_gameModel.currentGame + '_')) {
      final afterGameName = fullName.substring(_gameModel.currentGame.length + 1);
      
      for (final entry in _teamSuffixes.entries) {
        if (afterGameName.contains(entry.value)) {
          final suffixIndex = afterGameName.lastIndexOf(entry.value);
          if (suffixIndex > 0) {
            baseName = afterGameName.substring(0, suffixIndex);
            break;
          }
        }
      }
      
      if (baseName.isEmpty) {
        baseName = afterGameName;
      }
    } else {
      baseName = fullName;
    }
    
    _baseName = baseName;
    app_logger.log.i('数据收集', '提取基础名称', '从[$fullName]提取得到[$baseName]');
  }

  String _buildFullCollectionName(String baseName) {
    final teamSuffix = _teamSuffixes[teamSide] ?? '';
    final prefix = baseName.isNotEmpty ? _gameModel.currentGame + '_' + baseName : _gameModel.currentGame;
    return prefix + teamSuffix + '_' + username;
  }

  String _buildFormattedCollectionName() {
    return _baseName.isNotEmpty 
        ? '$gameName\_$_baseName${getTeamSuffix(teamSide)}' 
        : '$gameName${getTeamSuffix(teamSide)}';
  }

  // 私有方法 - 网络请求
  Future<void> _fetchSettingsFromServer() async {
    if (!_serverService.isConnected) {
      log.warning('WebSocket未连接，无法获取数据收集设置');
      return;
    }
    
    try {
      final request = {
        'action': 'data_collection_read',
        'content': {
          'username': _authModel.username,
          'gameName': _gameModel.currentGame,
        }
      };
      
      _serverService.sendMessage(jsonEncode(request));
      log.info('已发送数据收集设置获取请求');
    } catch (e) {
      log.severe('发送数据收集设置请求时出错: $e');
    }
  }

  Future<void> _sendSettingsToServer(String formattedName) async {
    final request = _dataModel.toJson();
    (request['content'] as Map<String, dynamic>)['collectionName'] = formattedName;
    
    app_logger.log.i('数据收集', '发送设置前检查游戏状态', {
      'GameModel.currentGame': _gameModel.currentGame,
      'formattedName': formattedName,
      'username': _authModel.username,
      'requestContent': request['content']
    });
    
    _serverService.sendMessage(jsonEncode(request));
    log.info('已发送数据收集设置更新请求');
    
    app_logger.log.i('数据收集', '保存设置', 
        '名称: $formattedName, 阵营: ${_dataModel.teamSide}');
  }

  Future<void> _sendFetchRequest() async {
    final request = {
      'action': 'data_collection_fetch',
      'content': {
        'username': _authModel.username,
        'gameName': _gameModel.currentGame,
        'includeAllCollections': true,
        'includeDetails': true,
      }
    };
    
    _serverService.sendMessage(jsonEncode(request));
  }

  Future<void> _sendUploadRequest() async {
    final request = {
      'action': 'data_collection_upload',
      'content': {
        'username': _authModel.username,
        'gameName': _gameModel.currentGame,
        'teamSide': _dataModel.teamSide,
        'mapHotkey': _dataModel.mapHotkey,
        'targetHotkey': _dataModel.targetHotkey,
        'timestamp': DateTime.now().toIso8601String(),
        'deleteAll': false,
      }
    };
    
    _serverService.sendMessage(jsonEncode(request));
  }

  // 私有方法 - 响应处理
  void _handleSettingsResponse(Map<String, dynamic> data) {
    try {
      final status = data['status'] ?? 'error';

      if (status == 'ok') {
        _dataModel.fromJson(data);
        log.info('已从服务器更新数据收集设置');
        _validateHotkeyValues();
        _extractBaseName();
      } else {
        // 处理错误响应
        final content = data['content'] ?? {};
        final error = content['error'] ?? {};
        final errorCode = error['code'] ?? 'UNKNOWN_ERROR';
        final errorMessage = error['message'] ?? '设置操作失败';

        final friendlyMessage = _getFriendlyErrorMessage(errorCode, errorMessage);
        _lastError = friendlyMessage;
        log.warning('数据收集设置操作失败: $errorCode - $errorMessage');
        app_logger.log.w('数据收集', '设置操作失败', friendlyMessage);

        // 触发UI错误提示
        notifyListeners();
      }
    } catch (e) {
      log.severe('处理设置响应时出错: $e');
      app_logger.log.e('数据收集', '处理设置响应出错', e.toString());
    }
  }

  void _handleFetchDataResponse(Map<String, dynamic> data) {
    try {
      log.info('收到数据获取响应: ${data['status']}');

      if (data['status'] == 'ok' && data['content'] != null) {
        _setLoading(false);
        _processFetchResponseContent(data['content']);
        notifyListeners();
      } else {
        _setLoading(false);
        // 处理错误响应，支持新的错误格式
        final content = data['content'] ?? {};
        final error = content['error'] ?? {};
        final errorCode = error['code'] ?? 'UNKNOWN_ERROR';
        final errorMessage = error['message'] ?? data['errorMessage'] as String? ?? '获取数据失败';

        final friendlyMessage = _getFriendlyErrorMessage(errorCode, errorMessage);
        _lastError = friendlyMessage;
        log.warning('获取数据返回错误: ${data['status']} - $errorCode: $errorMessage');
        app_logger.log.w('数据收集', '获取数据失败', friendlyMessage);

        _hasFetchedData = false;
        notifyListeners();
      }
    } catch (e, stackTrace) {
      _setLoading(false);
      log.severe('处理获取数据响应时出错: $e', e, stackTrace);
      app_logger.log.e('数据收集', '处理响应出错', e.toString());
      _hasFetchedData = false;
      notifyListeners();
    }
  }

  void _processFetchResponseContent(Map<String, dynamic> content) {
    if (content.containsKey('collections') && content['collections'] is List) {
      _processCollectionsData(content['collections'] as List);
    } else {
      _processLegacyData(content);
    }
  }

  void _processCollectionsData(List collections) {
    log.info('集合数量: ${collections.length}');
    
    _collections.clear();
    for (final collection in collections) {
      if (collection is Map<String, dynamic>) {
        _collections.add(collection);
      }
    }
    
    app_logger.log.i('数据收集', '获取成功', '发现 ${_collections.length} 个数据集合');
    _hasFetchedData = _collections.isNotEmpty;
    
    final currentCollection = _findCurrentCollection();
    if (currentCollection != null) {
      _processCollectionData(currentCollection);
    }
  }

  Map<String, dynamic>? _findCurrentCollection() {
    for (final collection in _collections) {
      if (collection['name'] == _dataModel.collectionName) {
        return collection;
      }
    }
    return _collections.isNotEmpty ? _collections[0] : null;
  }

  void _processCollectionData(Map<String, dynamic> collection) {
    _basePath = collection['path'] as String? ?? '';
    
    if (collection.containsKey('folders') && collection['folders'] is List) {
      _summary['folders'] = collection['folders'];
    }
    
    if (collection.containsKey('summary') && collection['summary'] is Map) {
      _summary = Map<String, dynamic>.from(collection['summary'] as Map);
    }
    
    if (collection.containsKey('detailedStats') && collection['detailedStats'] is Map) {
      final detailedStats = collection['detailedStats'] as Map<String, dynamic>;
      _summary.addAll(detailedStats);
      _totalFiles = _calculateTotalFiles(detailedStats);
    } else {
      _totalFiles = _calculateTotalFilesFromFolders(collection['folders'] as List? ?? []);
    }
    
    _fetchTimestamp = collection['lastUpdated'] as String? ?? '';
    
    app_logger.log.i('数据收集', '获取数据成功', 
        '总文件: $_totalFiles, 图像: $totalImages, 标签: $totalLabels');
  }

  void _processLegacyData(Map<String, dynamic> content) {
    _basePath = content['basePath'] as String? ?? '';
    _totalFiles = content['totalFiles'] as int? ?? 0;
    
    if (content.containsKey('stats') && content['stats'] is Map) {
      _summary = Map<String, dynamic>.from(content['stats'] as Map);
      
      if (_summary.containsKey('categories') && _summary['categories'] is Map) {
        _summary.addAll(_summary['categories'] as Map<String, dynamic>);
      }
    } else if (content.containsKey('summary') && content['summary'] is Map) {
      _summary = Map<String, dynamic>.from(content['summary'] as Map);
    }
    
    if (content.containsKey('folders') && content['folders'] is List && !_summary.containsKey('folders')) {
      _summary['folders'] = content['folders'];
    }
    
    _fetchTimestamp = content['timestamp'] as String? ?? '';
    _hasFetchedData = true;
    _collections.clear();
    
    app_logger.log.i('数据收集', '获取数据成功(旧格式)', 
        '总文件: $_totalFiles, 图像: $totalImages, 标签: $totalLabels');
  }

  int _calculateTotalFiles(Map<String, dynamic> detailedStats) {
    int total = 0;
    detailedStats.forEach((folder, stats) {
      if (stats is Map<String, dynamic> && stats.containsKey('total')) {
        total += (stats['total'] as int? ?? 0);
      } else if (stats is int) {
        total += stats;
      }
    });
    return total;
  }

  int _calculateTotalFilesFromFolders(List folders) {
    int total = 0;
    for (final folder in folders) {
      if (folder is Map<String, dynamic> && folder.containsKey('fileCount')) {
        total += (folder['fileCount'] as int? ?? 0);
      }
    }
    return total;
  }

  void _handleUploadDataResponse(Map<String, dynamic> data) {
    try {
      _isUploading = false;
      
      final status = data['status'] ?? 'error';
      final content = data['content'] ?? {};
      
      final uploadStatus = content.containsKey('uploadStatus') 
          ? content['uploadStatus'] 
          : (content.containsKey('message') && content['message'].toString().contains('成功') ? 'success' : 'failed');
      _uploadStatus = uploadStatus;
      
      final uploadComplete = content['uploadComplete'] as bool? ?? false;
      final filesDeleted = content['filesDeleted'] as bool? ?? false;

      if (status == 'ok' && (uploadStatus == 'success' || uploadComplete)) {
        _handleUploadSuccess(content, uploadComplete, filesDeleted);
      } else {
        _handleUploadFailure(content, uploadComplete, filesDeleted);
      }
      
      notifyListeners();
    } catch (e) {
      _handleUploadError(e, null);
    }
  }

  void _handleUploadSuccess(Map<String, dynamic> content, bool uploadComplete, bool filesDeleted) {
    _uploadDetails = {
      'uploadComplete': uploadComplete,
      'filesDeleted': filesDeleted,
      'message': content['message'] ?? '上传成功'
    };
    
    final statusMessage = uploadComplete ? '上传完成' : '上传未完成';
    final deleteStatus = filesDeleted ? '文件已删除' : '文件未删除';
    final detailLog = '$statusMessage, $deleteStatus';
    
    log.info('数据上传结果: $detailLog');
    app_logger.log.i('数据收集', '上传数据处理完成', detailLog);
    
    _uploadError = '';
    fetchDataSilently();
  }

  void _handleUploadFailure(Map<String, dynamic> content, bool uploadComplete, bool filesDeleted) {
    final errorMsg = content['message'] ?? '';
    final error = content['error'] ?? {};
    final errorCode = error['code'] ?? 'UNKNOWN_ERROR';
    final errorMessage = error['message'] ?? (errorMsg.isNotEmpty ? errorMsg : '未知错误');

    // 根据错误码提供更友好的错误信息
    final friendlyMessage = _getFriendlyErrorMessage(errorCode, errorMessage);
    _uploadError = friendlyMessage;

    log.warning('数据上传失败: $errorCode - $errorMessage');
    app_logger.log.w('数据收集', '上传数据失败', '$errorCode: $errorMessage');

    _uploadDetails = {
      'uploadComplete': uploadComplete,
      'filesDeleted': filesDeleted
    };
  }

  /// 根据错误码返回友好的错误信息
  String _getFriendlyErrorMessage(String errorCode, String originalMessage) {
    switch (errorCode) {
      case 'DATABASE_ERROR':
        return '数据库操作失败，请稍后重试';
      case 'VALIDATION_ERROR':
        return '数据验证失败，请检查输入信息';
      case 'USER_NOT_FOUND':
        return '用户不存在，请检查登录状态';
      case 'INVALID_GAME':
        return '无效的游戏名称';
      case 'SERVER_UNREACHABLE':
        return '无法连接到上传服务器，请检查网络';
      case 'AUTH_FAILED':
        return '认证失败，请重新登录';
      case 'INVALID_COLLECTION':
        return '数据收集名称格式错误';
      case 'NO_DATA':
        return '没有可上传的数据';
      case 'UPLOAD_INCOMPLETE':
        return '上传未完成，请重试';
      case 'DISK_FULL':
        return '服务器存储空间不足';
      case 'FILE_SYSTEM_ERROR':
        return '文件系统错误，请检查文件权限';
      default:
        return originalMessage.isNotEmpty ? '$errorCode: $originalMessage' : errorCode;
    }
  }

  // 私有方法 - 状态管理
  void _setUploadingState() {
    _isUploading = true;
    _uploadStatus = 'uploading';
    _uploadError = '';
    _uploadDetails = {};
    notifyListeners();
  }

  void _setUploadTimeout(BuildContext context) {
    Future.delayed(const Duration(seconds: 30), () {
      if (_isUploading) {
        log.warning('上传数据请求可能超时');
        app_logger.log.w('数据收集', '上传请求超时', '30秒后仍未收到响应');
        
        if (_isUploading && context.mounted) {
          _showWarningMessage(context, '上传请求时间较长，请耐心等待...');
        }
      }
    });
  }

  void _handleUploadError(dynamic error, BuildContext? context) {
    _isUploading = false;
    _uploadStatus = 'error';
    _uploadError = error.toString();
    notifyListeners();
    
    log.severe('发送数据上传请求时出错: $error');
    if (context != null && context.mounted) {
      _showErrorMessage(context, '上传数据失败: $error');
    }
  }

  // 私有方法 - UI消息
  void _showMessage(BuildContext context, String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  void _showInfoMessage(BuildContext context, String message) {
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.info,
      duration: const Duration(seconds: 2),
    );
  }

  void _showErrorMessage(BuildContext context, String message) {
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.error,
      duration: const Duration(seconds: 2),
    );
  }

  void _showWarningMessage(BuildContext context, String message) {
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.warning,
      duration: const Duration(seconds: 2),
    );
  }
} 