# 移动UI设计核心知识体系

## 🎨 移动UI设计原则

### Material Design核心理念
Material Design是Google推出的设计语言，强调物理世界的材质感和动态效果。

#### 核心原则
- **材质隐喻**：界面元素具有物理属性，如阴影、深度
- **大胆图形化**：使用鲜明的色彩、大字体、留白
- **有意义的动效**：动画提供视觉连续性和反馈

#### 设计系统组件
```dart
// Material Design 3 主题系统
ThemeData(
  useMaterial3: true,
  colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
  
  // 字体系统
  textTheme: TextTheme(
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
    ),
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.5,
    ),
  ),
  
  // 组件主题
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
    ),
  ),
)
```

### Cupertino Design指导
Apple的设计语言强调简洁、优雅和功能性。

#### 核心特征
- **清晰性**：文字清晰易读，图标精确明了
- **尊重性**：尊重用户内容，界面服务于内容
- **深度感**：通过层次和动效创建深度感

```dart
// Cupertino主题配置
CupertinoThemeData(
  primaryColor: CupertinoColors.systemBlue,
  brightness: Brightness.light,
  
  textTheme: CupertinoTextThemeData(
    navTitleTextStyle: TextStyle(
      fontSize: 17,
      fontWeight: FontWeight.w600,
    ),
    textStyle: TextStyle(
      fontSize: 17,
      fontWeight: FontWeight.w400,
    ),
  ),
)
```

## 🎯 用户体验设计原则

### 可用性设计
#### 费茨定律应用
按钮大小和距离影响用户操作效率。

```dart
// 合理的按钮尺寸设计
class ActionButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final ButtonSize size;
  
  @override
  Widget build(BuildContext context) {
    late double minWidth, minHeight;
    
    switch (size) {
      case ButtonSize.small:
        minWidth = 88; minHeight = 36;
        break;
      case ButtonSize.medium:
        minWidth = 112; minHeight = 44;
        break;
      case ButtonSize.large:
        minWidth = 140; minHeight = 52;
        break;
    }
    
    return ConstrainedBox(
      constraints: BoxConstraints(
        minWidth: minWidth,
        minHeight: minHeight,
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        child: Text(text),
      ),
    );
  }
}
```

#### 触摸目标设计
```dart
// 确保足够的触摸区域
class TouchableArea extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        constraints: BoxConstraints(
          minWidth: 44,  // iOS建议最小44pt
          minHeight: 44, // Android建议最小48dp
        ),
        child: child,
      ),
    );
  }
}
```

### 视觉层次设计
```dart
class VisualHierarchyExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主标题 - 最高优先级
        Text(
          '主要内容标题',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        
        SizedBox(height: 8),
        
        // 副标题 - 中等优先级
        Text(
          '副标题说明',
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.8),
          ),
        ),
        
        SizedBox(height: 16),
        
        // 正文 - 较低优先级
        Text(
          '这里是正文内容，提供详细信息...',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
            height: 1.5,
          ),
        ),
        
        SizedBox(height: 12),
        
        // 辅助信息 - 最低优先级
        Text(
          '最后更新：2024年1月1日',
          style: theme.textTheme.labelSmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.4),
          ),
        ),
      ],
    );
  }
}
```

## 📱 响应式布局设计

### 屏幕适配策略
```dart
class ScreenUtil {
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }
  
  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }
  
  // 根据屏幕宽度判断设备类型
  static DeviceType getDeviceType(BuildContext context) {
    final width = screenWidth(context);
    
    if (width < 600) return DeviceType.mobile;
    if (width < 1200) return DeviceType.tablet;
    return DeviceType.desktop;
  }
  
  // 响应式间距
  static double responsivePadding(BuildContext context) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return 16.0;
      case DeviceType.tablet:
        return 24.0;
      case DeviceType.desktop:
        return 32.0;
    }
  }
}

enum DeviceType { mobile, tablet, desktop }
```

### 自适应网格布局
```dart
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  
  const ResponsiveGrid({
    Key? key,
    required this.children,
    this.spacing = 16.0,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据可用宽度计算列数
        int crossAxisCount;
        if (constraints.maxWidth > 1200) {
          crossAxisCount = 4;
        } else if (constraints.maxWidth > 800) {
          crossAxisCount = 3;
        } else if (constraints.maxWidth > 600) {
          crossAxisCount = 2;
        } else {
          crossAxisCount = 1;
        }
        
        return GridView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: 1.2,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}
```

### 断点式设计
```dart
class Breakpoints {
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
}

class ResponsiveWidget extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  
  const ResponsiveWidget({
    Key? key,
    required this.mobile,
    this.tablet,
    this.desktop,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= Breakpoints.desktop) {
      return desktop ?? tablet ?? mobile;
    } else if (screenWidth >= Breakpoints.tablet) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }
}
```

## 🎨 色彩与字体设计

### 色彩理论应用
```dart
class ColorPalette {
  // 主色调
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryVariant = Color(0xFF1976D2);
  
  // 辅助色调
  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryVariant = Color(0xFFF57C00);
  
  // 语义色彩
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // 中性色调
  static const Color surface = Color(0xFFFFFFFF);
  static const Color background = Color(0xFFF5F5F5);
  static const Color onSurface = Color(0xFF212121);
  static const Color onBackground = Color(0xFF424242);
  
  // 创建ColorScheme
  static ColorScheme createColorScheme({Brightness brightness = Brightness.light}) {
    if (brightness == Brightness.dark) {
      return ColorScheme.dark(
        primary: primary,
        secondary: secondary,
        surface: Color(0xFF121212),
        background: Color(0xFF000000),
        error: error,
      );
    }
    
    return ColorScheme.light(
      primary: primary,
      secondary: secondary,
      surface: surface,
      background: background,
      error: error,
    );
  }
}
```

### 字体系统设计
```dart
class Typography {
  // 字体大小规范
  static const double headline1 = 96.0;
  static const double headline2 = 60.0;
  static const double headline3 = 48.0;
  static const double headline4 = 34.0;
  static const double headline5 = 24.0;
  static const double headline6 = 20.0;
  static const double subtitle1 = 16.0;
  static const double subtitle2 = 14.0;
  static const double body1 = 16.0;
  static const double body2 = 14.0;
  static const double caption = 12.0;
  static const double overline = 10.0;
  
  // 创建文本主题
  static TextTheme createTextTheme() {
    return TextTheme(
      headlineLarge: TextStyle(
        fontSize: headline1,
        fontWeight: FontWeight.w300,
        letterSpacing: -1.5,
      ),
      headlineMedium: TextStyle(
        fontSize: headline2,
        fontWeight: FontWeight.w300,
        letterSpacing: -0.5,
      ),
      headlineSmall: TextStyle(
        fontSize: headline3,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
      ),
      titleLarge: TextStyle(
        fontSize: headline4,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
      ),
      titleMedium: TextStyle(
        fontSize: headline5,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
      ),
      titleSmall: TextStyle(
        fontSize: headline6,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
      ),
      bodyLarge: TextStyle(
        fontSize: body1,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
      ),
      bodyMedium: TextStyle(
        fontSize: body2,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
      ),
      labelLarge: TextStyle(
        fontSize: subtitle1,
        fontWeight: FontWeight.w500,
        letterSpacing: 1.25,
      ),
      labelSmall: TextStyle(
        fontSize: overline,
        fontWeight: FontWeight.w400,
        letterSpacing: 1.5,
      ),
    );
  }
}
```

## 🎭 动效与交互设计

### 微交互设计
```dart
class MicroInteractionButton extends StatefulWidget {
  final String text;
  final VoidCallback onPressed;
  
  @override
  _MicroInteractionButtonState createState() => _MicroInteractionButtonState();
}

class _MicroInteractionButtonState extends State<MicroInteractionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    _colorAnimation = ColorTween(
      begin: Colors.blue,
      end: Colors.blue.shade700,
    ).animate(_controller);
  }
  
  void _handleTapDown(TapDownDetails details) {
    _controller.forward();
  }
  
  void _handleTapUp(TapUpDetails details) {
    _controller.reverse();
    widget.onPressed();
  }
  
  void _handleTapCancel() {
    _controller.reverse();
  }
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: _colorAnimation.value,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                widget.text,
                style: TextStyle(color: Colors.white),
              ),
            ),
          );
        },
      ),
    );
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

### 页面转场动画
```dart
class SlideTransitionRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final SlideDirection direction;
  
  SlideTransitionRoute({
    required this.page,
    this.direction = SlideDirection.rightToLeft,
  }) : super(
    pageBuilder: (context, animation, secondaryAnimation) => page,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      late Offset begin;
      
      switch (direction) {
        case SlideDirection.rightToLeft:
          begin = Offset(1.0, 0.0);
          break;
        case SlideDirection.leftToRight:
          begin = Offset(-1.0, 0.0);
          break;
        case SlideDirection.topToBottom:
          begin = Offset(0.0, -1.0);
          break;
        case SlideDirection.bottomToTop:
          begin = Offset(0.0, 1.0);
          break;
      }
      
      const end = Offset.zero;
      const curve = Curves.easeInOut;
      
      var tween = Tween(begin: begin, end: end).chain(
        CurveTween(curve: curve),
      );
      
      return SlideTransition(
        position: animation.drive(tween),
        child: child,
      );
    },
    transitionDuration: Duration(milliseconds: 300),
  );
}

enum SlideDirection {
  rightToLeft,
  leftToRight,
  topToBottom,
  bottomToTop,
}
```

### 加载状态设计
```dart
class LoadingStates extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 骨架屏
        SkeletonLoader(),
        
        SizedBox(height: 20),
        
        // 进度指示器
        ProgressIndicators(),
        
        SizedBox(height: 20),
        
        // 加载动画
        CustomLoadingAnimation(),
      ],
    );
  }
}

class SkeletonLoader extends StatefulWidget {
  @override
  _SkeletonLoaderState createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
    
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(_animation.value, 0),
              end: Alignment(_animation.value + 0.5, 0),
              colors: [
                Colors.grey[300]!,
                Colors.grey[100]!,
                Colors.grey[300]!,
              ],
            ),
          ),
        );
      },
    );
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

## 🛠️ 组件设计模式

### 原子设计方法论
```dart
// 原子级组件
class Atom {
  // 按钮原子
  static Widget button({
    required String text,
    required VoidCallback onPressed,
    ButtonType type = ButtonType.primary,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: _getButtonStyle(type),
      child: Text(text),
    );
  }
  
  // 输入框原子
  static Widget textField({
    String? hintText,
    ValueChanged<String>? onChanged,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextField(
      onChanged: onChanged,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        hintText: hintText,
        border: OutlineInputBorder(),
      ),
    );
  }
  
  static ButtonStyle _getButtonStyle(ButtonType type) {
    switch (type) {
      case ButtonType.primary:
        return ElevatedButton.styleFrom(backgroundColor: Colors.blue);
      case ButtonType.secondary:
        return ElevatedButton.styleFrom(backgroundColor: Colors.grey);
      case ButtonType.danger:
        return ElevatedButton.styleFrom(backgroundColor: Colors.red);
    }
  }
}

enum ButtonType { primary, secondary, danger }

// 分子级组件
class Molecule {
  // 搜索框分子
  static Widget searchBox({
    String? hintText,
    ValueChanged<String>? onChanged,
    VoidCallback? onSearch,
  }) {
    return Row(
      children: [
        Expanded(
          child: Atom.textField(
            hintText: hintText ?? '搜索...',
            onChanged: onChanged,
          ),
        ),
        SizedBox(width: 8),
        Atom.button(
          text: '搜索',
          onPressed: onSearch ?? () {},
        ),
      ],
    );
  }
  
  // 用户头像和名称
  static Widget userProfile({
    required String name,
    required String avatarUrl,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          CircleAvatar(
            backgroundImage: NetworkImage(avatarUrl),
            radius: 20,
          ),
          SizedBox(width: 12),
          Text(
            name,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

// 组织级组件
class Organism {
  // 导航栏组织
  static Widget navigationBar({
    required String title,
    List<Widget>? actions,
    VoidCallback? onBack,
  }) {
    return AppBar(
      title: Text(title),
      leading: onBack != null
          ? IconButton(
              icon: Icon(Icons.arrow_back),
              onPressed: onBack,
            )
          : null,
      actions: actions,
    );
  }
  
  // 卡片列表组织
  static Widget cardList({
    required List<CardData> items,
    ItemWidgetBuilder<CardData>? itemBuilder,
  }) {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        return Card(
          margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: itemBuilder?.call(context, items[index]) ??
              ListTile(
                title: Text(items[index].title),
                subtitle: Text(items[index].subtitle),
              ),
        );
      },
    );
  }
}

typedef ItemWidgetBuilder<T> = Widget Function(BuildContext context, T item);

class CardData {
  final String title;
  final String subtitle;
  
  CardData({required this.title, required this.subtitle});
}
```

### 可复用组件库
```dart
// 组件库基类
abstract class DesignSystemComponent extends StatelessWidget {
  const DesignSystemComponent({Key? key}) : super(key: key);
  
  // 获取设计系统主题
  DesignSystemTheme getTheme(BuildContext context) {
    return DesignSystemTheme.of(context);
  }
}

// 设计系统主题
class DesignSystemTheme extends InheritedWidget {
  final ColorScheme colorScheme;
  final TextTheme textTheme;
  final double borderRadius;
  final double spacing;
  
  const DesignSystemTheme({
    Key? key,
    required this.colorScheme,
    required this.textTheme,
    this.borderRadius = 8.0,
    this.spacing = 16.0,
    required Widget child,
  }) : super(key: key, child: child);
  
  static DesignSystemTheme of(BuildContext context) {
    final result = context.dependOnInheritedWidgetOfExactType<DesignSystemTheme>();
    assert(result != null, 'No DesignSystemTheme found in context');
    return result!;
  }
  
  @override
  bool updateShouldNotify(DesignSystemTheme oldWidget) {
    return colorScheme != oldWidget.colorScheme ||
           textTheme != oldWidget.textTheme ||
           borderRadius != oldWidget.borderRadius ||
           spacing != oldWidget.spacing;
  }
}

// 自定义按钮组件
class DSButton extends DesignSystemComponent {
  final String text;
  final VoidCallback? onPressed;
  final DSButtonVariant variant;
  final DSButtonSize size;
  
  const DSButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.variant = DSButtonVariant.primary,
    this.size = DSButtonSize.medium,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final theme = getTheme(context);
    
    return ElevatedButton(
      onPressed: onPressed,
      style: _getButtonStyle(theme),
      child: Padding(
        padding: _getPadding(),
        child: Text(
          text,
          style: _getTextStyle(theme),
        ),
      ),
    );
  }
  
  ButtonStyle _getButtonStyle(DesignSystemTheme theme) {
    Color backgroundColor;
    Color foregroundColor;
    
    switch (variant) {
      case DSButtonVariant.primary:
        backgroundColor = theme.colorScheme.primary;
        foregroundColor = theme.colorScheme.onPrimary;
        break;
      case DSButtonVariant.secondary:
        backgroundColor = theme.colorScheme.secondary;
        foregroundColor = theme.colorScheme.onSecondary;
        break;
      case DSButtonVariant.outline:
        backgroundColor = Colors.transparent;
        foregroundColor = theme.colorScheme.primary;
        break;
    }
    
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(theme.borderRadius),
        side: variant == DSButtonVariant.outline
            ? BorderSide(color: theme.colorScheme.primary)
            : BorderSide.none,
      ),
    );
  }
  
  EdgeInsets _getPadding() {
    switch (size) {
      case DSButtonSize.small:
        return EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case DSButtonSize.medium:
        return EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case DSButtonSize.large:
        return EdgeInsets.symmetric(horizontal: 20, vertical: 16);
    }
  }
  
  TextStyle _getTextStyle(DesignSystemTheme theme) {
    late TextStyle baseStyle;
    
    switch (size) {
      case DSButtonSize.small:
        baseStyle = theme.textTheme.labelSmall!;
        break;
      case DSButtonSize.medium:
        baseStyle = theme.textTheme.labelMedium!;
        break;
      case DSButtonSize.large:
        baseStyle = theme.textTheme.labelLarge!;
        break;
    }
    
    return baseStyle.copyWith(
      fontWeight: FontWeight.w600,
    );
  }
}

enum DSButtonVariant { primary, secondary, outline }
enum DSButtonSize { small, medium, large }
```

这份移动UI设计知识体系从设计原则到具体实现，为Flutter开发者提供了全面的界面设计指导。 