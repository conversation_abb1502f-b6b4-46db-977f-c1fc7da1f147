<execution>
  <constraint>
    ## 移动应用质量客观限制
    - **设备性能差异**：不同型号设备的CPU、内存、GPU性能差异巨大
    - **操作系统版本**：iOS和Android系统版本碎片化严重
    - **网络环境变化**：移动网络不稳定、延迟高、带宽受限
    - **电池寿命**：移动设备电池容量和续航时间限制
    - **屏幕尺寸多样**：从小屏手机到大屏平板的适配需求
    - **应用商店审核**：App Store和Google Play的严格审核标准
  </constraint>

  <rule>
    ## 移动应用质量强制规则
    - **性能底线**：应用启动时间不得超过3秒，UI响应不得超过100ms
    - **内存管理**：应用内存使用不得超过设备可用内存的20%
    - **崩溃率控制**：应用崩溃率必须低于0.1%（千分之一）
    - **电池消耗**：应用不得成为电池消耗的主要原因
    - **安全合规**：必须通过安全扫描，无高危漏洞
    - **隐私保护**：严格遵循GDPR、CCPA等隐私法规要求
    - **无障碍支持**：必须支持基本的无障碍访问功能
  </rule>

  <guideline>
    ## 移动应用质量指导原则
    - **用户第一**：所有质量决策以用户体验为优先考虑
    - **性能优先**：在功能和性能之间，优先保证核心性能指标
    - **渐进增强**：从基础功能开始，逐步增加高级特性
    - **数据驱动**：基于真实用户数据和使用场景优化质量
    - **持续监控**：建立完善的质量监控和预警机制
    - **平台一致**：确保iOS和Android平台的质量水准一致
    - **前瞻性设计**：考虑未来设备和系统版本的兼容性
  </guideline>

  <process>
    ## 移动应用质量保证完整流程

    ### Phase 1: 质量需求定义 (规划阶段)

    #### 1.1 性能需求基准
    ```
    核心性能指标：
    ├── 启动时间
    │   ├── 冷启动 < 3秒
    │   ├── 温启动 < 1.5秒
    │   └── 热启动 < 0.5秒
    ├── 响应时间
    │   ├── UI交互 < 100ms
    │   ├── 页面切换 < 300ms
    │   └── 网络请求 < 2秒
    ├── 内存使用
    │   ├── 基础内存 < 50MB
    │   ├── 峰值内存 < 150MB
    │   └── 内存泄漏 = 0
    └── 电池消耗
        ├── 后台耗电 < 2%/小时
        ├── 前台耗电 < 10%/小时
        └── CPU使用率 < 30%
    ```

    #### 1.2 用户体验质量标准
    ```
    UX质量矩阵：
    ├── 可用性
    │   ├── 界面响应性 (100ms规则)
    │   ├── 操作一致性 (平台规范)
    │   └── 错误恢复 (优雅降级)
    ├── 可访问性
    │   ├── 屏幕阅读器支持
    │   ├── 颜色对比度 > 4.5:1
    │   └── 字体大小可调节
    ├── 兼容性
    │   ├── 设备兼容性 > 95%
    │   ├── 系统版本兼容性
    │   └── 屏幕尺寸适配
    └── 可靠性
        ├── 崩溃率 < 0.1%
        ├── ANR率 < 0.05%
        └── 网络异常处理
    ```

    #### 1.3 安全质量要求
    ```
    安全质量检查清单：
    ├── 数据保护
    │   ├── 敏感数据加密存储
    │   ├── 网络传输HTTPS
    │   └── 本地数据清理
    ├── 权限管理
    │   ├── 最小权限原则
    │   ├── 权限申请说明
    │   └── 权限使用审计
    ├── 代码安全
    │   ├── 代码混淆启用
    │   ├── 防逆向工程
    │   └── 安全漏洞扫描
    └── 隐私合规
        ├── 隐私政策完整
        ├── 用户同意机制
        └── 数据处理透明
    ```

    ### Phase 2: 开发阶段质量控制

    #### 2.1 代码质量检查
    ```yaml
    # analysis_options.yaml
    analyzer:
      strong-mode:
        implicit-casts: false
        implicit-dynamic: false
      errors:
        missing_required_param: error
        missing_return: error
        todo: ignore
    
    linter:
      rules:
        - prefer_const_constructors
        - prefer_const_literals_to_create_immutables
        - avoid_print
        - prefer_single_quotes
    ```

    #### 2.2 自动化测试集成
    ```dart
    // 测试覆盖率配置
    void main() {
      group('应用核心功能测试', () {
        testWidgets('首页加载测试', (WidgetTester tester) async {
          // 性能测试
          final stopwatch = Stopwatch()..start();
          await tester.pumpWidget(MyApp());
          await tester.pumpAndSettle();
          stopwatch.stop();
          
          // 验证启动时间
          expect(stopwatch.elapsedMilliseconds, lessThan(3000));
        });
        
        testWidgets('内存泄漏测试', (WidgetTester tester) async {
          // 内存基线
          final initialMemory = await getMemoryUsage();
          
          // 执行操作
          for (int i = 0; i < 100; i++) {
            await tester.pumpWidget(TestWidget());
            await tester.pumpWidget(Container());
          }
          
          // 强制垃圾回收
          await tester.binding.reassembleApplication();
          
          // 检查内存泄漏
          final finalMemory = await getMemoryUsage();
          expect(finalMemory - initialMemory, lessThan(10 * 1024 * 1024)); // 10MB
        });
      });
    }
    ```

    #### 2.3 持续性能监控
    ```dart
    class PerformanceMonitor {
      static void trackAppLaunch() {
        final stopwatch = Stopwatch()..start();
        
        WidgetsBinding.instance.addPostFrameCallback((_) {
          stopwatch.stop();
          final launchTime = stopwatch.elapsedMilliseconds;
          
          // 记录启动时间
          Analytics.track('app_launch_time', {
            'duration_ms': launchTime,
            'is_cold_start': true,
          });
          
          // 性能预警
          if (launchTime > 3000) {
            FirebaseCrashlytics.instance.recordError(
              'Slow app launch: ${launchTime}ms',
              null,
              fatal: false,
            );
          }
        });
      }
      
      static void trackFrameMetrics() {
        WidgetsBinding.instance.addTimingsCallback((timings) {
          for (final timing in timings) {
            final frameTime = timing.totalSpan.inMilliseconds;
            
            if (frameTime > 16) { // 超过16ms表示可能掉帧
              Analytics.track('frame_drop', {
                'frame_time_ms': frameTime,
                'build_duration': timing.buildDuration.inMilliseconds,
                'raster_duration': timing.rasterDuration.inMilliseconds,
              });
            }
          }
        });
      }
    }
    ```

    ### Phase 3: 测试阶段质量验证

    #### 3.1 性能压力测试
    ```dart
    void main() {
      group('性能压力测试', () {
        testWidgets('大数据列表滚动性能', (WidgetTester tester) async {
          // 创建大数据集
          final items = List.generate(10000, (index) => 'Item $index');
          
          await tester.pumpWidget(MaterialApp(
            home: ListView.builder(
              itemCount: items.length,
              itemBuilder: (context, index) => ListTile(
                title: Text(items[index]),
              ),
            ),
          ));
          
          // 快速滚动测试
          final stopwatch = Stopwatch()..start();
          await tester.fling(
            find.byType(ListView),
            const Offset(0, -5000),
            10000,
          );
          await tester.pumpAndSettle();
          stopwatch.stop();
          
          // 验证滚动性能
          expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        });
        
        testWidgets('内存压力测试', (WidgetTester tester) async {
          // 创建大量Widget
          for (int i = 0; i < 1000; i++) {
            await tester.pumpWidget(
              MaterialApp(
                home: Scaffold(
                  body: Column(
                    children: List.generate(100, (index) => 
                      Container(height: 50, child: Text('Item $index'))
                    ),
                  ),
                ),
              ),
            );
            
            if (i % 100 == 0) {
              // 检查内存使用
              final memoryUsage = await getMemoryUsage();
              expect(memoryUsage, lessThan(200 * 1024 * 1024)); // 200MB
            }
          }
        });
      });
    }
    ```

    #### 3.2 设备兼容性测试
    ```dart
    class DeviceCompatibilityTest {
      static Future<void> runOnMultipleDevices() async {
        final testDevices = [
          'iPhone 12', 'iPhone SE', 'iPad Pro',
          'Samsung Galaxy S21', 'Google Pixel 5', 'OnePlus 9',
          'Xiaomi Mi 11', 'Huawei P40', 'Nokia G20'
        ];
        
        for (final device in testDevices) {
          await runDeviceSpecificTests(device);
        }
      }
      
      static Future<void> runDeviceSpecificTests(String device) async {
        // 模拟不同设备特性
        final deviceInfo = await getDeviceInfo(device);
        
        // 屏幕尺寸适配测试
        testWidgets('$device 屏幕适配测试', (WidgetTester tester) async {
          tester.binding.window.physicalSizeTestValue = 
              Size(deviceInfo.width, deviceInfo.height);
          
          await tester.pumpWidget(MyApp());
          
          // 验证UI元素是否正确显示
          expect(find.byType(AppBar), findsOneWidget);
          expect(find.byType(BottomNavigationBar), findsOneWidget);
        });
        
        // 性能基准测试
        testWidgets('$device 性能基准测试', (WidgetTester tester) async {
          final stopwatch = Stopwatch()..start();
          await tester.pumpWidget(MyApp());
          await tester.pumpAndSettle();
          stopwatch.stop();
          
          // 根据设备性能调整预期
          final expectedTime = deviceInfo.isLowEnd ? 5000 : 3000;
          expect(stopwatch.elapsedMilliseconds, lessThan(expectedTime));
        });
      }
    }
    ```

    #### 3.3 网络环境测试
    ```dart
    class NetworkQualityTest {
      static Future<void> testDifferentNetworkConditions() async {
        final networkConditions = [
          NetworkCondition.wifi,
          NetworkCondition.lte,
          NetworkCondition.threeG,
          NetworkCondition.twoG,
          NetworkCondition.offline,
        ];
        
        for (final condition in networkConditions) {
          await testUnderNetworkCondition(condition);
        }
      }
      
      static Future<void> testUnderNetworkCondition(
        NetworkCondition condition
      ) async {
        // 模拟网络环境
        await NetworkSimulator.setCondition(condition);
        
        testWidgets('${condition.name} 网络环境测试', (WidgetTester tester) async {
          await tester.pumpWidget(MyApp());
          
          // 测试数据加载
          final stopwatch = Stopwatch()..start();
          await tester.tap(find.text('加载数据'));
          await tester.pumpAndSettle();
          stopwatch.stop();
          
          if (condition == NetworkCondition.offline) {
            // 离线模式验证
            expect(find.text('离线模式'), findsOneWidget);
          } else {
            // 在线模式性能验证
            final expectedTime = condition.isSlowNetwork ? 10000 : 3000;
            expect(stopwatch.elapsedMilliseconds, lessThan(expectedTime));
          }
        });
      }
    }
    ```

    ### Phase 4: 发布前质量审核

    #### 4.1 应用商店预审核
    ```
    App Store检查清单：
    ├── 应用元数据
    │   ├── 应用名称和描述准确
    │   ├── 关键词相关性
    │   └── 截图和预览视频
    ├── 应用内容
    │   ├── 无违规内容
    │   ├── 版权信息完整
    │   └── 年龄分级适当
    ├── 技术要求
    │   ├── 64位支持
    │   ├── iOS版本兼容性
    │   └── 无私有API使用
    └── 隐私合规
        ├── 隐私政策链接
        ├── 数据收集说明
        └── 第三方SDK审查
    
    Google Play检查清单：
    ├── 应用签名
    │   ├── App Bundle格式
    │   ├── 签名密钥安全
    │   └── Play App Signing
    ├── 目标API级别
    │   ├── targetSdkVersion最新
    │   ├── 权限申请合理
    │   └── 后台限制遵循
    ├── 内容政策
    │   ├── 无恶意软件
    │   ├── 无误导信息
    │   └── 无版权侵犯
    └── 技术质量
        ├── 崩溃率 < 2%
        ├── ANR率 < 1%
        └── 安装成功率 > 95%
    ```

    #### 4.2 最终质量验证
    ```dart
    class FinalQualityCheck {
      static Future<bool> performFinalCheck() async {
        final results = <String, bool>{};
        
        // 性能检查
        results['performance'] = await checkPerformance();
        
        // 功能检查
        results['functionality'] = await checkFunctionality();
        
        // 安全检查
        results['security'] = await checkSecurity();
        
        // 兼容性检查
        results['compatibility'] = await checkCompatibility();
        
        // 用户体验检查
        results['user_experience'] = await checkUserExperience();
        
        // 所有检查都必须通过
        return results.values.every((result) => result);
      }
      
      static Future<bool> checkPerformance() async {
        // 启动时间检查
        final launchTime = await measureLaunchTime();
        if (launchTime > 3000) return false;
        
        // 内存使用检查
        final memoryUsage = await measureMemoryUsage();
        if (memoryUsage > 200 * 1024 * 1024) return false;
        
        // CPU使用检查
        final cpuUsage = await measureCpuUsage();
        if (cpuUsage > 30) return false;
        
        return true;
      }
      
      static Future<bool> checkSecurity() async {
        // 静态安全扫描
        final staticScanResult = await StaticSecurityScanner.scan();
        if (staticScanResult.hasHighRiskVulnerabilities) return false;
        
        // 依赖安全检查
        final dependencyCheck = await DependencySecurityChecker.check();
        if (dependencyCheck.hasVulnerabilities) return false;
        
        // 数据加密验证
        final encryptionCheck = await DataEncryptionValidator.validate();
        if (!encryptionCheck.isValid) return false;
        
        return true;
      }
    }
    ```

    ### Phase 5: 发布后质量监控

    #### 5.1 实时质量监控
    ```dart
    class RealTimeQualityMonitor {
      static void initialize() {
        // 崩溃监控
        FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
        
        // 性能监控
        FirebasePerformance.instance.setPerformanceCollectionEnabled(true);
        
        // 用户行为监控
        FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
        
        // 自定义质量指标
        setupCustomMetrics();
      }
      
      static void setupCustomMetrics() {
        // 启动时间监控
        final launchTrace = FirebasePerformance.instance.newTrace('app_launch');
        launchTrace.start();
        
        WidgetsBinding.instance.addPostFrameCallback((_) {
          launchTrace.stop();
        });
        
        // 页面性能监控
        NavigatorObserver.addListener(PagePerformanceObserver());
        
        // 网络请求监控
        HttpClient.addInterceptor(NetworkPerformanceInterceptor());
      }
    }
    ```

    #### 5.2 质量预警系统
    ```dart
    class QualityAlertSystem {
      static void checkQualityMetrics() {
        Timer.periodic(Duration(hours: 1), (timer) async {
          final metrics = await fetchQualityMetrics();
          
          // 崩溃率预警
          if (metrics.crashRate > 0.1) {
            sendAlert(AlertType.highCrashRate, metrics.crashRate);
          }
          
          // 性能预警
          if (metrics.averageLaunchTime > 3000) {
            sendAlert(AlertType.slowLaunch, metrics.averageLaunchTime);
          }
          
          // 用户评分预警
          if (metrics.averageRating < 4.0) {
            sendAlert(AlertType.lowRating, metrics.averageRating);
          }
        });
      }
      
      static void sendAlert(AlertType type, dynamic value) {
        final alert = QualityAlert(
          type: type,
          value: value,
          timestamp: DateTime.now(),
          severity: determineSeverity(type, value),
        );
        
        // 发送到监控系统
        QualityMonitoringService.sendAlert(alert);
        
        // 通知开发团队
        SlackNotification.send(alert.toMessage());
      }
    }
    ```
  </process>

  <criteria>
    ## 移动应用质量评价标准

    ### 核心性能指标
    - ✅ 冷启动时间 ≤ 3秒
    - ✅ UI响应时间 ≤ 100ms
    - ✅ 页面切换时间 ≤ 300ms
    - ✅ 内存使用峰值 ≤ 200MB
    - ✅ CPU使用率 ≤ 30%（前台）

    ### 稳定性指标
    - ✅ 应用崩溃率 ≤ 0.1%
    - ✅ ANR（无响应）率 ≤ 0.05%
    - ✅ 网络请求成功率 ≥ 99%
    - ✅ 数据同步成功率 ≥ 98%
    - ✅ 功能可用性 ≥ 99.9%

    ### 用户体验指标
    - ✅ 应用商店评分 ≥ 4.5星
    - ✅ 用户留存率（7天）≥ 40%
    - ✅ 用户留存率（30天）≥ 20%
    - ✅ 平均会话时长 ≥ 3分钟
    - ✅ 卸载率 ≤ 5%

    ### 兼容性指标
    - ✅ 设备兼容性 ≥ 95%
    - ✅ 操作系统版本支持 ≥ 90%用户
    - ✅ 屏幕尺寸适配完整
    - ✅ 网络环境适配良好
    - ✅ 无障碍功能完备

    ### 安全质量指标
    - ✅ 安全漏洞数量 = 0（高危）
    - ✅ 数据传输加密率 = 100%
    - ✅ 敏感数据本地加密 = 100%
    - ✅ 权限申请合规性 = 100%
    - ✅ 隐私政策完整性 = 100%

    ### 代码质量指标
    - ✅ 测试覆盖率 ≥ 80%
    - ✅ 代码重复率 ≤ 5%
    - ✅ 代码复杂度 ≤ 10（圈复杂度）
    - ✅ 静态分析问题 = 0（严重）
    - ✅ 技术债务控制在合理范围

    ### 发布质量指标
    - ✅ 应用商店审核通过率 ≥ 95%
    - ✅ 发布后回退率 ≤ 1%
    - ✅ 版本升级成功率 ≥ 98%
    - ✅ 新版本采用率（7天）≥ 60%
    - ✅ 新版本采用率（30天）≥ 85%
  </criteria>
</execution> 