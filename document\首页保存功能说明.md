# 首页保存功能说明

## 功能概述

首页保存按钮现在采用三步验证流程：
1. **第一步**：发送登录验证请求 (`login_read`)
2. **第二步**：登录验证成功后，发送首页配置保存请求 (`home_modify`)
3. **第三步**：保存成功后，异步触发页头刷新状态，等待服务器响应后完成操作

## 修改内容

### 1. HomeController 新增功能

#### 新增状态管理
- `_isSaving`: 保存操作进行中标志
- `_loginRequestSent`: 登录请求已发送标志  
- `_waitingForHomeResponse`: 等待首页配置响应标志
- `_waitingForStatusRefresh`: **新增** - 等待状态刷新响应标志
- `_statusRefreshTimeout`: **新增** - 状态刷新超时定时器

#### 新增方法
- `_startSaveProcess()`: 开始保存流程
- `_sendLoginRequest()`: 发送登录验证请求
- `_buildLoginRequest()`: 构建登录请求数据
- `_sendHomeConfigRequest()`: 发送首页配置请求
- `_resetSaveState()`: 重置保存状态
- `_showInfoMessage()`: 显示信息提示
- `_handleLoginResponse()`: 处理登录响应
- `_triggerHeaderRefreshStatusAsync()`: **重构** - 异步触发页头刷新状态
- `_handleStatusBarResponse()`: **新增** - 处理状态栏响应
- `_completeStatusRefresh()`: **新增** - 完成状态刷新流程

#### 修改方法
- `saveHomeConfig()`: 重构为三步流程
- `_handleModifyResponse()`: 区分保存流程和普通修改，添加异步状态刷新
- `onCardKeyChanged()`: **增强** - 添加异步状态刷新触发
- `refreshHomeConfig()`: **增强** - 添加异步状态刷新触发
- `_handleServerMessage()`: **增强** - 添加状态栏响应处理

### 2. HomeScreen UI 更新

#### 保存按钮状态
- **正常状态**: 蓝色背景，显示"保存"文字和保存图标
- **保存中状态**: 灰色背景，显示"保存中..."文字和加载动画
- **禁用状态**: 保存过程中按钮不可点击

## 保存流程

### 流程图
```
用户点击保存按钮
    ↓
检查服务器连接状态
    ↓
开始保存流程 (_startSaveProcess)
    ↓
发送登录验证请求 (login_read)
    ↓
等待登录响应...
    ↓
登录验证成功？
    ├─ 是 → 发送首页配置请求 (home_modify)
    └─ 否 → 显示错误信息，重置状态
    ↓
等待首页配置响应...
    ↓
配置保存成功？
    ├─ 是 → 显示成功信息 + 异步触发页头刷新状态
    └─ 否 → 显示错误信息，重置状态
    ↓
发送状态刷新请求 (status_bar_query)
    ↓
等待状态刷新响应...
    ↓
收到状态响应 → 完成整个保存流程，重置状态
```

### 详细步骤

1. **用户操作**
   - 用户点击首页的"保存"按钮

2. **前置检查**
   - 检查服务器连接状态
   - 检查是否已有保存操作在进行中

3. **开始保存流程**
   - 设置 `_isSaving = true`
   - 显示"正在验证登录状态..."提示
   - 更新UI状态（按钮变为加载状态）

4. **发送登录验证**
   - 构建登录请求数据
   - 发送 `login_read` 请求到服务器
   - 设置 `_loginRequestSent = true`

5. **处理登录响应**
   - 如果登录成功：继续第6步
   - 如果登录失败：显示错误信息，重置状态

6. **发送首页配置**
   - 显示"正在保存首页配置..."提示
   - 发送 `home_modify` 请求到服务器
   - 设置 `_waitingForHomeResponse = true`

7. **处理配置响应**
   - 如果保存成功：显示"首页配置保存成功" + **异步触发页头刷新状态**
   - 如果保存失败：显示"首页配置保存失败"，重置状态

8. **🆕 异步状态刷新** 
   - 设置 `_waitingForStatusRefresh = true`
   - 启动5秒超时定时器
   - 发送 `status_bar_query` 请求
   - 等待服务器响应...

9. **🆕 完成状态刷新**
   - 收到 `status_bar_response` 响应
   - 状态数据自动更新到HeaderModel
   - 重置所有保存状态
   - 用户看到最新的正确状态数据

## 🆕 状态刷新增强功能

### 异步等待机制
- **问题解决**: 确保UI更新的是服务器返回的最新数据，而不是旧数据
- **时序控制**: 等待服务器响应后才完成操作流程
- **超时保护**: 5秒超时机制，避免无限等待
- **重复请求保护**: 防止同时发送多个状态刷新请求

### 触发时机
1. **保存按钮操作**：保存成功后异步触发，等待响应完成
2. **卡密输入变化**：每次卡密输入变化后异步触发
3. **刷新配置操作**：点击刷新按钮后异步触发
4. **普通配置修改**：任何配置修改成功后异步触发

### 实现原理
```dart
void _triggerHeaderRefreshStatusAsync() {
  if (!_serverService.isConnected) {
    _completeStatusRefresh();
    return;
  }
  
  if (_waitingForStatusRefresh) {
    // 防止重复请求
    return;
  }
  
  _waitingForStatusRefresh = true;
  
  // 设置5秒超时
  _statusRefreshTimeout = Timer(Duration(seconds: 5), () {
    if (_waitingForStatusRefresh) {
      _completeStatusRefresh();
    }
  });
  
  // 发送状态查询请求
  _serverService.sendMessage({
    'action': 'status_bar_query',
    'token': _serverService.token,
  });
}

void _handleStatusBarResponse(Map<String, dynamic> responseData) {
  if (!_waitingForStatusRefresh) return;
  
  // 状态数据已通过ServerService自动更新到HeaderModel
  _completeStatusRefresh();
}
```

### 用户体验提升
- **数据准确性**: 确保UI显示的是服务器返回的最新状态
- **时序正确性**: 操作完成后才显示最终结果
- **无需手动**: 用户无需手动点击页头刷新按钮
- **智能触发**: 只在有意义的操作后触发，避免过度请求
- **超时保护**: 网络异常时不会无限等待

## 用户体验改进

### 视觉反馈
- **加载动画**: 保存过程中显示圆形进度指示器
- **按钮状态**: 保存中按钮变灰且不可点击
- **文字提示**: 按钮文字从"保存"变为"保存中..."
- **Toast消息**: 显示详细的操作进度和结果
- **🆕 状态更新**: 操作完成后页头状态显示最新的正确数据

### 错误处理
- **连接检查**: 保存前检查服务器连接状态
- **重复操作**: 防止用户重复点击保存按钮
- **超时处理**: 通过状态管理避免无限等待
- **错误提示**: 详细的错误信息显示
- **🆕 状态刷新容错**: 状态刷新失败不影响主要功能
- **🆕 超时保护**: 状态刷新5秒超时，避免无限等待

### 状态管理
- **状态隔离**: 区分保存流程和普通配置修改
- **状态重置**: 操作完成后自动重置状态
- **状态同步**: UI状态与控制器状态实时同步
- **🆕 页头状态同步**: 异步等待确保状态数据的准确性
- **🆕 时序控制**: 严格控制操作完成的时机

## API 请求格式

### 登录验证请求
```json
{
  "action": "login_read",
  "content": {
    "username": "当前用户名",
    "password": "用户密码",
    "token": "用户令牌",
    "isPro": true/false,
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

### 首页配置请求
```json
{
  "action": "home_modify",
  "content": {
    "username": "当前用户名",
    "gameName": "选中的游戏",
    "cardKey": "用户卡密",
    "isPro": true/false,
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

### 🆕 状态刷新请求
```json
{
  "action": "status_bar_query",
  "token": "用户令牌"
}
```

### 🆕 状态刷新响应
```json
{
  "action": "status_bar_response",
  "status": "ok",
  "data": {
    "dbStatus": true,
    "inferenceStatus": true,
    "cardKeyStatus": false,
    "keyMouseStatus": true,
    "frameRate": 286,
    "width": 1920,
    "height": 1080,
    "updatedAt": "2025-05-25T17:22:15Z"
  }
}
```

## 安全性提升

1. **身份验证**: 每次保存前都验证用户身份
2. **权限检查**: 通过登录验证确保用户有权限进行操作
3. **状态验证**: 确保用户登录状态有效
4. **数据完整性**: 验证通过后才进行数据保存
5. **🆕 状态一致性**: 异步等待确保页头状态与实际系统状态一致
6. **🆕 时序安全**: 严格控制操作时序，避免数据竞争

## 兼容性说明

- 修改后的功能完全向后兼容
- 不影响其他页面的保存功能
- 保持原有的API响应处理逻辑
- UI组件可以正常响应状态变化
- **🆕 状态刷新功能**: 不影响现有页头功能，仅增强用户体验
- **🆕 异步机制**: 不影响现有同步操作，仅优化状态更新时序

## 🆕 卡密输入框增强

### 功能改进
- **实时状态更新**: 卡密输入变化后异步触发状态刷新
- **即时反馈**: 用户可以看到卡密状态的准确变化
- **智能触发**: 只在实际输入变化时触发，避免重复请求
- **🆕 数据准确性**: 等待服务器响应确保显示正确的验证结果

### 实现方式
```dart
void onCardKeyChanged(String value) {
  _gameModel.updateCardKey(value);
  _notifyServerModelChanged('卡密');
  
  // 异步触发页头刷新状态，等待响应后更新UI
  _triggerHeaderRefreshStatusAsync();
}
```

### 用户体验
- 输入卡密后看到准确的验证结果
- 无需等待或手动刷新
- 状态变化准确反映在页头显示中
- **🆕 数据可靠性**: 显示的是服务器验证后的真实状态 