// ignore_for_file: library_private_types_in_public_api, use_super_parameters, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../controllers/function_controller.dart';
import 'card_manager_component.dart';
import '../../component/message_component.dart';
import '../../component/dropdown_component.dart';
import '../../models/function_model.dart';

/// 功能设置页面 - 管理各种功能配置和预设
class FunctionScreen extends StatefulWidget {
  const FunctionScreen({Key? key}) : super(key: key);
  
  @override
  _FunctionScreenState createState() => _FunctionScreenState();
}

class _FunctionScreenState extends State<FunctionScreen> {
  static const String _title = '功能设置';
  static const EdgeInsets _headerPadding = EdgeInsets.all(16.0);
  static const double _buttonSpacing = 12.0;
  
  bool _isInitialized = false;
  bool _hasError = false;
  String _errorMessage = '';
  
  @override
  void initState() {
    super.initState();
    // 确保在下一帧初始化，避免布局冲突
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeScreen();
    });
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次依赖变化时都更新context
    final controller = Provider.of<FunctionController>(context, listen: false);
    controller.setContext(context);
  }
  
  /// 初始化屏幕
  void _initializeScreen() {
    if (!mounted) return;
    
    try {
      final controller = Provider.of<FunctionController>(context, listen: false);
      
      // 设置context
      controller.setContext(context);
      
      // 检查控制器是否有数据
      if (controller.cards.isEmpty) {
        // 如果没有数据，尝试刷新
        controller.refreshFunctionConfig();
      }
      
      setState(() {
        _isInitialized = true;
        _hasError = false;
      });
    } catch (e) {
      setState(() {
        _isInitialized = true;
        _hasError = true;
        _errorMessage = '初始化失败: $e';
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    // 如果还未初始化，显示加载状态
    if (!_isInitialized) {
      return _buildLoadingView();
    }
    
    // 如果有错误，显示错误状态
    if (_hasError) {
      return _buildErrorView();
    }
    
    // 使用Consumer监听FunctionController的变化
    return Consumer<FunctionController>(
      builder: (context, controller, child) {
        // 检查控制器状态
        if (controller.cards.isEmpty) {
          return _buildEmptyStateView(controller);
        }
        
        return Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                _buildHeader(context, controller),
                Expanded(
                  child: _buildCardManager(controller),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  /// 构建加载视图
  Widget _buildLoadingView() {
    return const Scaffold(
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                '正在加载功能配置...',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建错误视图
  Widget _buildErrorView() {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 80,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                '加载失败',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage,
                style: const TextStyle(color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isInitialized = false;
                    _hasError = false;
                  });
                  _initializeScreen();
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建空状态视图
  Widget _buildEmptyStateView(FunctionController controller) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context, controller),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.settings,
                      size: 80,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '暂无功能配置',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.grey),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '请检查网络连接或稍后重试',
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () => _handleRefreshConfig(context, controller),
                      icon: const Icon(Icons.refresh),
                      label: const Text('刷新配置'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建卡片管理器
  Widget _buildCardManager(FunctionController controller) {
    return CardManagerComponent(
      cards: controller.cards,
      appBarTitle: _title,
      externalPropertyHandling: true,
      onCardPropertyChanged: controller.updateCardProperty,
      onCardBoolPropertyChanged: controller.updateCardBoolProperty,
      onGetAllCardsInfo: (cards) => _handleSaveConfig(context, controller),
      enableScroll: true,
      hideAppBar: true,
    );
  }
  
  /// 构建顶部标题和按钮
  Widget _buildHeader(BuildContext context, FunctionController controller) {
    return Container(
      padding: _headerPadding,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              _title,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          _buildActionButtons(context, controller),
        ],
      ),
    );
  }
  
  /// 构建操作按钮组
  Widget _buildActionButtons(BuildContext context, FunctionController controller) {
    return Row(
      children: [
        _buildRefreshButton(context, controller),
        const SizedBox(width: _buttonSpacing),
        _buildSaveButton(context, controller),
      ],
    );
  }
  
  /// 构建刷新按钮
  Widget _buildRefreshButton(BuildContext context, FunctionController controller) {
    return Tooltip(
      message: '刷新参数',
      child: Container(
        height: 32, // 与ButtonSize.small的高度一致
        width: 32,  // 保持圆形比例
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [
              Color(0xFF4CAF50), // 浅绿色
              Color(0xFF2E7D32), // 深绿色
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF2E7D32).withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () => _handleRefreshConfig(context, controller),
            child: Container(
              alignment: Alignment.center,
              child: const Icon(
                Icons.refresh,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// 构建保存按钮
  Widget _buildSaveButton(BuildContext context, FunctionController controller) {
    return Tooltip(
      message: '保存当前设置',
      child: Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(6),
            onTap: () => _handleSaveConfig(context, controller),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.save,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(width: 6),
                const Text(
                  '保存',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  /// 处理保存配置
  void _handleSaveConfig(BuildContext context, FunctionController controller) {
    try {
      controller.saveAllCardsInfo(context);
      
      // 显示保存提示
      MessageComponent.showIconToast(
        context: context,
        message: '正在保存功能配置...',
        type: MessageType.info,
        duration: const Duration(seconds: 1),
      );
    } catch (e) {
      MessageComponent.showIconToast(
        context: context,
        message: '保存失败: $e',
        type: MessageType.error,
        duration: const Duration(seconds: 2),
      );
    }
  }
  
  /// 处理刷新配置
  void _handleRefreshConfig(BuildContext context, FunctionController controller) {
    try {
      // 确保context是最新的
      controller.setContext(context);
      
      // 执行刷新（内部会显示持续的加载提示）
      controller.refreshFunctionConfig();
      
    } catch (e) {
      MessageComponent.showIconToast(
        context: context,
        message: '❌ 刷新失败: $e',
        type: MessageType.error,
        duration: const Duration(seconds: 2),
      );
    }
  }
}
