# 状态栏查询频率自定义功能说明

## 功能概述

本次更新为Flutter应用添加了状态栏查询频率自定义功能，用户可以在首页配置中设置自定义的状态栏查询频率，控制页头状态信息（数据库、推理、卡密、键鼠、分辨率、刷新率等）的更新频率。

## 功能特性

### 🎯 核心功能
- **自定义查询频率**：支持1-300秒范围内的状态栏查询频率设置
- **实时生效**：修改频率后立即应用到服务器连接
- **持久化存储**：查询频率设置会自动保存到本地存储
- **输入验证**：自动验证输入范围，防止无效设置

### 📱 用户界面
- 在首页配置页面新增"状态栏查询频率"输入框
- 支持数字键盘输入，提升用户体验
- 实时显示当前设置的查询频率
- 输入提示：1-300秒有效范围，控制页头状态信息更新频率

### 🔍 控制的状态信息
此功能控制以下页头状态信息的更新频率：
- **数据库状态**：true/false
- **推理状态**：true/false  
- **卡密状态**：true/false
- **键鼠状态**：true/false
- **分辨率信息**：如 1920x1080
- **刷新率信息**：如 286Hz
- **版本信息**：当前版本和最新版本

## 技术实现

### 📂 修改的文件

#### 1. `lib/models/game_model.dart`
- **新增属性**：
  ```dart
  int _heartbeatInterval = _defaultHeartbeatInterval; // 状态栏查询频率（秒）
  ```
- **新增方法**：
  - `updateHeartbeatInterval(int interval)` - 更新状态栏查询频率
  - `addHeartbeatChangeListener()` - 添加频率变更监听器
  - `removeHeartbeatChangeListener()` - 移除频率变更监听器
- **持久化支持**：自动保存和加载查询频率设置

#### 2. `lib/services/server_service.dart`
- **新增属性**：
  ```dart
  Duration _heartbeatInterval = _defaultHeartbeatInterval; // 可配置的状态栏查询频率
  ```
- **新增方法**：
  - `setHeartbeatInterval(int seconds)` - 设置状态栏查询频率
  - `get heartbeatInterval` - 获取当前查询频率
- **核心逻辑**：
  - `_startHeartbeat()` - 启动状态栏查询定时器，发送 `status_bar_query` 请求
  - 动态调整：连接状态下实时调整查询频率

#### 3. `lib/controllers/home_controller.dart`
- **新增控制器**：
  ```dart
  final TextEditingController heartbeatIntervalController; // 状态栏查询频率输入控制器
  ```
- **新增方法**：
  - `onHeartbeatIntervalChanged(String value)` - 处理频率输入变化
  - `_onHeartbeatIntervalChanged(int newInterval)` - 处理频率变更事件
- **同步机制**：GameModel、ServerService和UI之间的数据同步

#### 4. `lib/views/home_screen.dart`
- **新增UI组件**：状态栏查询频率输入框
- **输入验证**：数字键盘类型，范围提示
- **响应式布局**：适配不同屏幕尺寸

### 🔄 数据流程

```
用户输入查询频率 → HomeController → GameModel → ServerService
        ↓                ↓            ↓           ↓
       UI更新      ←  事件监听    ←  持久化存储  ←  状态栏查询调整
                                                    ↓
                                           定期发送 status_bar_query
                                                    ↓
                                           更新页头状态信息显示
```

1. **用户输入**：在首页输入状态栏查询频率
2. **控制器处理**：HomeController验证并处理输入
3. **模型更新**：GameModel更新并持久化存储
4. **服务同步**：ServerService接收变更并调整查询频率
5. **定时查询**：定期发送 `status_bar_query` 请求获取最新状态
6. **UI反馈**：页头实时显示最新的状态信息

### 🛡️ 安全与验证

- **范围验证**：仅接受1-300秒的有效范围
- **类型检查**：确保输入为有效数字
- **异常处理**：无效输入时记录日志并忽略
- **默认值**：初始化时使用15秒作为默认值

## 使用方法

### 设置状态栏查询频率
1. 打开应用，进入首页配置
2. 找到"状态栏查询频率 (秒)"输入框
3. 输入1-300之间的数字（单位：秒）
4. 设置会自动保存并立即生效
5. 观察页头状态信息按新频率更新

### 推荐设置
- **正常使用**：15-30秒（默认15秒）- 平衡性能和及时性
- **快速响应**：5-10秒 - 适用于需要快速状态更新的场景
- **节能模式**：60-120秒 - 适用于后台运行或节省资源
- **调试模式**：1-5秒 - 适用于开发调试，观察状态变化

## 注意事项

### ⚠️ 重要提醒
- **网络负载**：频率过高可能增加网络负载和服务器压力
- **电池消耗**：高频率查询会增加电池消耗
- **服务器压力**：请根据服务器性能合理设置查询频率
- **状态及时性**：频率过低可能导致状态信息更新不及时

### 🔧 技术限制
- 最小值：1秒（防止过度频繁请求）
- 最大值：300秒（防止状态信息过时）
- 精度：秒级（不支持毫秒级设置）
- 生效时机：仅在正常模式下生效（快速模式300ms不受影响）

### 📊 状态信息说明
- **查询请求**：`status_bar_query`
- **响应数据**：包含数据库、推理、卡密、键鼠状态及系统信息
- **显示位置**：页头中间区域的状态显示组件
- **更新机制**：定时查询 + 实时响应

## 日志与调试

### 日志输出示例
```
✅ 状态栏查询频率已更新: 15s -> 30s (控制页头状态信息更新频率)
🔍 GameModel状态检查: 状态栏查询频率: 30秒
📡 重启状态栏查询定时器以应用新频率
🔄 启动状态栏查询定时器，间隔30秒
📤 发送状态栏查询请求 (获取页头状态信息)
```

### 调试方法
- 查看控制台日志确认查询频率设置是否生效
- 观察页头状态信息更新频率验证设置
- 使用开发者工具监控 `status_bar_query` 请求频率
- 使用GameModel的debugPrintCurrentState方法检查状态

## 版本兼容性

- **最低Flutter版本**：与项目现有要求一致
- **存储兼容性**：新增设置不影响现有数据
- **向后兼容**：未设置时使用默认15秒频率

## 未来扩展

### 可能的增强功能
- [ ] 预设查询频率模式（节能、正常、高频）
- [ ] 网络状态自适应频率调整
- [ ] 状态查询失败重试机制配置
- [ ] 状态信息统计显示（成功率、延迟等）
- [ ] 不同状态项的独立查询频率控制

---

*最后更新：2024年12月* 