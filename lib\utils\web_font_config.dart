/// Web环境字体配置工具
/// 专门处理Web环境下的字体加载和降级方案

import 'package:flutter/foundation.dart';

/// Web字体配置类
class WebFontConfig {
  /// Web安全字体映射
  /// 将Google Fonts映射到Web安全的系统字体
  static const Map<String, String> webSafeFonts = {
    'Roboto': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    'Lato': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif',
    'Open Sans': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif',
    'Montserrat': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif',
    'Poppins': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif',
    'Inter': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif',
    'Source Sans Pro': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif',
    'Nunito': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif',
  };

  /// 默认Web字体栈
  static const String defaultWebFontStack = 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif';

  /// 获取Web安全字体
  static String getWebSafeFont(String fontFamily) {
    if (!kIsWeb) {
      return fontFamily; // 非Web环境返回原字体名
    }
    
    return webSafeFonts[fontFamily] ?? defaultWebFontStack;
  }

  /// 检查是否应该使用Google Fonts
  static bool shouldUseGoogleFonts() {
    // Web环境下禁用Google Fonts
    return !kIsWeb;
  }

  /// 获取字体降级方案
  static String getFallbackFont(String fontFamily) {
    if (kIsWeb) {
      return getWebSafeFont(fontFamily);
    }
    
    // 原生环境的降级方案
    switch (fontFamily) {
      case 'Roboto':
        return 'sans-serif';
      case 'Lato':
      case 'Open Sans':
      case 'Montserrat':
      case 'Poppins':
      case 'Inter':
      case 'Source Sans Pro':
      case 'Nunito':
        return 'sans-serif';
      default:
        return 'sans-serif';
    }
  }

  /// 获取环境描述
  static String getEnvironmentDescription() {
    if (kIsWeb) {
      return 'Web环境 - 使用系统字体';
    } else {
      return '原生环境 - 支持Google Fonts';
    }
  }

  /// 获取字体加载状态描述
  static String getFontLoadingStatus(bool useGoogleFonts, String fontFamily) {
    if (kIsWeb) {
      return '使用Web安全字体: ${getWebSafeFont(fontFamily)}';
    } else if (useGoogleFonts) {
      return '使用Google Fonts: $fontFamily';
    } else {
      return '使用系统字体: $fontFamily';
    }
  }

  /// 检查字体是否可用
  static bool isFontAvailable(String fontFamily) {
    if (kIsWeb) {
      // Web环境下所有字体都通过降级方案可用
      return true;
    }
    
    // 原生环境下检查Google Fonts可用性
    return webSafeFonts.containsKey(fontFamily);
  }

  /// 获取推荐字体列表
  static List<String> getRecommendedFonts() {
    if (kIsWeb) {
      // Web环境推荐系统字体
      return ['system-ui', 'Roboto', 'Helvetica Neue', 'Arial'];
    } else {
      // 原生环境推荐Google Fonts
      return webSafeFonts.keys.toList();
    }
  }
}

/// Web字体加载配置
class WebFontLoadingConfig {
  /// 字体加载超时时间（毫秒）
  static const int fontLoadTimeoutMs = 3000;
  
  /// 是否启用字体预加载
  static const bool enableFontPreload = false;
  
  /// 字体显示策略
  static const String fontDisplay = 'swap';
  
  /// 是否启用字体降级
  static const bool enableFontFallback = true;
}
