// ignore_for_file: use_super_parameters, library_private_types_in_public_api

import 'package:flutter/material.dart';
import '../controllers/pid_controller.dart';
import 'package:provider/provider.dart';
import '../component/Slider_component.dart';
import '../component/Button_component.dart';

/// PID设置页面 - 负责近端瞄准辅助参数调整的UI
class PidScreen extends StatelessWidget {
  // 布局常量
  static const double pagePadding = 12.0;
  static const double headerSpacing = 10.0;
  static const double cardSpacing = 8.0;
  static const double buttonSpacing = 10.0;
  static const double bottomSpacing = 10.0;
  static const double titleFontSize = 22.0;
  static const double descriptionFontSize = 14.0;
  static const int wideScreenBreakpoint = 768;
  
  // 颜色常量
  static const Color cardBackgroundColor = Colors.white;
  static const Color primaryButtonColor = Colors.blue;
  static const Color secondaryButtonColor = Colors.grey;
  static const Color refreshButtonColor = Color(0xFF2E7D32); // 深绿色，更好的对比度
  static const Color infoButtonColor = Colors.cyan;
  static const Color disabledButtonColor = Colors.grey;
  static const Color lockedButtonColor = Colors.orange;
  
  const PidScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<PidController>(
      builder: (context, pidController, _) {
        return Scaffold(
          body: Padding(
            padding: const EdgeInsets.all(pagePadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildHeader(pidController, context),
                const SizedBox(height: headerSpacing),
                buildDescriptionCard(),
                const SizedBox(height: cardSpacing),
                Expanded(
                  child: SingleChildScrollView(
                    child: buildParameterSection(pidController, context),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    );
  }
  
  /// 构建顶部标题和按钮
  Widget buildHeader(PidController controller, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        buildTitle(),
        buildActionButtons(controller, context),
      ],
    );
  }

  /// 构建页面标题
  Widget buildTitle() {
    return const Flexible(
      child: Text(
        'PID设置',
        style: TextStyle(
          fontSize: titleFontSize,
          fontWeight: FontWeight.bold,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// 构建操作按钮组
  Widget buildActionButtons(PidController controller, BuildContext context) {
    return Row(
      children: [
        buildRefreshButton(controller),
        const SizedBox(width: buttonSpacing),
        buildSaveButton(controller, context),
      ],
    );
  }

  /// 构建刷新按钮
  Widget buildRefreshButton(PidController controller) {
    return Tooltip(
      message: '刷新参数',
      child: Container(
        height: 32, // 与ButtonSize.small的高度一致
        width: 32,  // 保持圆形比例
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [
              Color(0xFF4CAF50), // 浅绿色
              Color(0xFF2E7D32), // 深绿色
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16), // 调整圆角以适应新尺寸
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF2E7D32).withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () => controller.requestPidParams(),
            child: Container(
              alignment: Alignment.center,
              child: const Icon(
                Icons.refresh,
                color: Colors.white,
                size: 18, // 稍微调小图标以适应新尺寸
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建保存按钮
  Widget buildSaveButton(PidController controller, BuildContext context) {
    final isLocked = controller.saveButtonLocked;
    final hasChanges = controller.hasUnsavedChanges;
    
    return Tooltip(
      message: isLocked ? '请使用加减按钮调整参数后保存' : '保存当前设置',
      child: Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: isLocked ? lockedButtonColor : primaryButtonColor,
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: (isLocked ? lockedButtonColor : primaryButtonColor).withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(6),
            onTap: isLocked ? null : () => controller.savePidConfig(context),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isLocked ? Icons.lock : (hasChanges ? Icons.save_alt : Icons.save),
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(width: 6),
                Text(
                  isLocked ? '已锁定' : '保存',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建页面描述卡片
  Widget buildDescriptionCard() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      color: cardBackgroundColor,
      child: const Padding(
        padding: EdgeInsets.all(8.0),
        child: Text(
          '调整游戏中的瞄准辅助参数。滚轮调整后会立即发送到服务器并锁定保存按钮，使用加减按钮调整可解锁保存功能。',
          style: TextStyle(fontSize: descriptionFontSize),
        ),
      ),
    );
  }

  /// 构建参数设置区域
  Widget buildParameterSection(PidController controller, BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth > wideScreenBreakpoint;
        
        final moveControlCard = createMoveControlParams(controller, context);
        final precisionControlCard = createPrecisionControlParams(controller, context);
        final distanceAxisCard = createDistanceAxisParams(controller, context);
        
        return Column(
          children: [
            isWideScreen 
                ? buildWideScreenLayout(moveControlCard, precisionControlCard, distanceAxisCard)
                : buildNarrowScreenLayout(moveControlCard, precisionControlCard, distanceAxisCard),
            const SizedBox(height: bottomSpacing),
            buildResetButton(controller, context),
          ],
        );
      }
    );
  }

  /// 构建宽屏布局
  Widget buildWideScreenLayout(Widget moveCard, Widget precisionCard, Widget distanceAxisCard) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(child: moveCard),
        const SizedBox(width: cardSpacing),
        Expanded(child: precisionCard),
        const SizedBox(width: cardSpacing),
        Expanded(child: distanceAxisCard),
      ],
    );
  }

  /// 构建窄屏布局
  Widget buildNarrowScreenLayout(Widget moveCard, Widget precisionCard, Widget distanceAxisCard) {
    return Column(
      children: [
        moveCard,
        const SizedBox(height: cardSpacing),
        precisionCard,
        const SizedBox(height: cardSpacing),
        distanceAxisCard,
      ],
    );
  }

  /// 创建移动控制参数组
  Widget createMoveControlParams(PidController controller, BuildContext context) {
    return createSlider(
      cardTitle: '移动控制参数',
      configs: [
        SliderConfig(
          title: '近端移动速度',
          value: controller.nearMoveFactor,
          min: 0.0,
          max: 5.0,
          step: 0.01,
          onChanged: (value) {
            controller.nearMoveFactor = value;
            controller.handleButtonOperation();
          },
          onChangeEnd: (_) => controller.handleSliderDragEnd(context),
          decimalPlaces: 2,
        ),
        SliderConfig(
          title: '近端跟踪速度',
          value: controller.nearStabilizer,
          min: 0.0,
          max: 5.0,
          step: 0.01,
          onChanged: (value) {
            controller.nearStabilizer = value;
            controller.handleButtonOperation();
          },
          onChangeEnd: (_) => controller.handleSliderDragEnd(context),
          decimalPlaces: 2,
        ),
        SliderConfig(
          title: '近端抖动力度',
          value: controller.nearResponseRate,
          min: 0.0,
          max: 5.0,
          step: 0.01,
          onChanged: (value) {
            controller.nearResponseRate = value;
            controller.handleButtonOperation();
          },
          onChangeEnd: (_) => controller.handleSliderDragEnd(context),
          decimalPlaces: 2,
        ),
      ],
    );
  }

  /// 创建精度控制参数组
  Widget createPrecisionControlParams(PidController controller, BuildContext context) {
    return createSlider(
      cardTitle: '精度控制参数',
      configs: [
        SliderConfig(
          title: '近端死区大小',
          value: controller.nearAssistZone,
          min: 0.0,
          max: 10.0,
          step: 0.5,
          onChanged: (value) {
            controller.nearAssistZone = value;
            controller.handleButtonOperation();
          },
          onChangeEnd: (_) => controller.handleSliderDragEnd(context),
          decimalPlaces: 2,
        ),
        SliderConfig(
          title: '近端回弹速度',
          value: controller.nearResponseDelay,
          min: 0.0,
          max: 2.0,
          step: 0.1,
          onChanged: (value) {
            controller.nearResponseDelay = value;
            controller.handleButtonOperation();
          },
          onChangeEnd: (_) => controller.handleSliderDragEnd(context),
          decimalPlaces: 2,
        ),
        SliderConfig(
          title: '近端积分限制',
          value: controller.nearMaxAdjustment,
          min: 0.0,
          max: 200.0,
          step: 1.0,
          onChanged: (value) {
            controller.nearMaxAdjustment = value;
            controller.handleButtonOperation();
          },
          onChangeEnd: (_) => controller.handleSliderDragEnd(context),
          decimalPlaces: 2,
        ),
      ],
    );
  }

  /// 创建远端与轴向参数组
  Widget createDistanceAxisParams(PidController controller, BuildContext context) {
    return createSlider(
      cardTitle: '远端与轴向参数',
      configs: [
        SliderConfig(
          title: '远端系数',
          value: controller.farFactor,
          min: 0.0,
          max: 2.0,
          step: 0.1,
          onChanged: (value) {
            controller.farFactor = value;
            controller.handleButtonOperation();
          },
          onChangeEnd: (_) => controller.handleSliderDragEnd(context),
          decimalPlaces: 2,
        ),
        SliderConfig(
          title: 'Y轴系数',
          value: controller.yAxisFactor,
          min: 0.0,
          max: 2.0,
          step: 0.1,
          onChanged: (value) {
            controller.yAxisFactor = value;
            controller.handleButtonOperation();
          },
          onChangeEnd: (_) => controller.handleSliderDragEnd(context),
          decimalPlaces: 2,
        ),
        SliderConfig(
          title: 'PID随机系数',
          value: controller.pidRandomFactor,
          min: 0.0,
          max: 1.0,
          step: 0.05,
          onChanged: (value) {
            controller.pidRandomFactor = value;
            controller.handleButtonOperation();
          },
          onChangeEnd: (_) => controller.handleSliderDragEnd(context),
          decimalPlaces: 2,
        ),
      ],
    );
  }
  


  /// 构建重置按钮
  Widget buildResetButton(PidController controller, BuildContext context) {
    return Center(
      child: Button.create(ButtonConfig.textWithIcon(
        '恢复默认值',
        Icons.restore,
        onPressed: () => controller.resetToDefaults(context),
        type: ButtonType.secondary,
        size: ButtonSize.small,
      )),
    );
  }
}

 