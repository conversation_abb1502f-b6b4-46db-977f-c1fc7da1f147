# 游戏列表更新说明

## 更新内容

### 添加PUBG游戏支持

在游戏模型中添加了对PUBG游戏的完整支持：

#### 1. 游戏列表更新
```dart
// 更新前的游戏列表
final List<String> _gameList = [
  'apex',
  'cf',
  'cfhd',
  'csgo2',
  'sjz',
  'ssjj2',
  'wwqy'
];

// 更新后的游戏列表
final List<String> _gameList = [
  'apex',
  'cf',
  'cfhd',
  'csgo2',
  'pubg',  // 新增PUBG游戏
  'sjz',
  'ssjj2',
  'wwqy'
];
```

#### 2. 图标路径支持
添加了PUBG游戏的图标路径映射：
```dart
'pubg': 'assets/GameImgIco/pubg.png',
```

#### 3. 自动升级机制
为现有用户实现了自动升级机制，确保：
- 检测到缺少pubg游戏时自动添加
- 保持向后兼容性
- 自动保存更新后的游戏列表

## 支持的游戏列表

现在应用支持以下9款游戏：

| 游戏ID | 游戏名称 | 图标路径 |
|--------|----------|----------|
| apex | Apex Legends | assets/GameImgIco/apex.png |
| cf | CrossFire | assets/GameImgIco/cf.png |
| cfhd | CrossFire HD | assets/GameImgIco/cfhd.jpg |
| csgo2 | CS:GO 2 | assets/GameImgIco/csgo2.png |
| hpjy | 和平精英 | assets/GameImgIco/hpjy.jpg |
| pubg | PUBG | assets/GameImgIco/pubg.png |
| sjz | 生死狙击 | assets/GameImgIco/sjz.png |
| ssjj2 | 生死狙击2 | assets/GameImgIco/ssjj2.png |
| wwqy | 无畏契约 | assets/GameImgIco/wwqy.png |

## 升级日志

### v1.2.0 - 添加和平精英游戏支持 (2025-07-22)

#### 新增功能
1. **新增hpjy游戏**：添加了"和平精英"游戏支持
2. **图标资源**：使用 `assets/GameImgIco/hpjy.jpg` 作为游戏图标
3. **显示名称**：中文显示名称为"和平精英"

#### 技术更新
- 更新游戏列表：从8款游戏扩展到9款游戏
- 添加图标路径映射：`GameIconPaths.hpjy`
- 更新常量定义：`SUPPORTED_GAMES` 和 `GAME_DISPLAY_NAMES`
- 完善自动升级机制：检测并添加缺失的hpjy游戏

### 自动检测和升级
应用启动时会自动检测游戏列表版本：

1. **检测缺少PUBG**: 如果用户的游戏列表中没有pubg，会自动添加
2. **检测旧版本**: 如果发现sj2游戏名称，会自动更新为sjz
3. **保存更新**: 自动保存更新后的游戏列表到本地存储

### 日志输出示例
```
从存储加载的游戏列表: [apex, cf, cfhd, csgo2, sjz, ssjj2, wwqy]
检测到缺少pubg游戏，需要添加
更新游戏列表为最新版本
最终游戏列表: [apex, cf, cfhd, csgo2, pubg, sjz, ssjj2, wwqy]
```

## API文档兼容性

所有相关的API文档已经包含了PUBG游戏的支持：
- `api/home_api.md`
- `api/sidebar_api.md`
- `api/fun_api.md`
- `api/data_collection_api.md`
- `api/register_api.md`

## 注意事项

1. **图标资源**: 确保`assets/GameImgIco/pubg.png`图标文件存在
2. **服务器支持**: 确认服务器端也支持pubg游戏的配置处理
3. **测试验证**: 建议测试PUBG游戏的选择、切换和配置功能

## 用户体验

- **无缝升级**: 现有用户无需任何操作，应用会自动添加PUBG游戏
- **保持设置**: 用户的其他游戏设置和当前选择不会受到影响
- **即时可用**: 添加后用户可以立即选择和使用PUBG游戏

通过这次更新，应用现在完整支持8款主流游戏，为用户提供更丰富的游戏选择。 