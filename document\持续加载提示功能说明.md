# 持续加载提示功能说明

## 📋 概述

实现了功能设置页面的持续加载提示功能，当发送请求后会显示持续的加载提示，直到收到后端响应才消失并显示结果提示。

## 🎯 功能特性

### 1. 持续加载提示
- **显示时机**: 发送请求到服务器时立即显示
- **持续时间**: 直到收到服务器响应或请求超时
- **提示样式**: 圆形进度指示器 + 加载文字
- **位置**: 屏幕中央
- **背景**: 半透明黑色，不阻挡用户操作其他区域

### 2. 响应结果处理
- **成功响应**: 关闭加载提示，显示绿色成功Toast
- **错误响应**: 关闭加载提示，显示红色错误Toast
- **请求超时**: 关闭加载提示，显示橙色超时Toast

### 3. 用户体验优化
- **防重复请求**: 如果已有请求在进行中，显示提示并阻止新请求
- **自动清理**: 页面销毁时自动关闭所有加载提示
- **状态同步**: 确保UI状态与请求状态保持一致

## 🔧 技术实现

### 1. MessageComponent新增功能

#### 持续加载提示 (showPersistentLoading)
```dart
static VoidCallback showPersistentLoading({
  required BuildContext context,
  required String message,
  MessagePosition position = MessagePosition.center,
})
```

特点：
- 使用Overlay实现，不会阻塞其他UI操作
- 返回VoidCallback用于手动关闭
- 支持自定义位置显示
- 带有圆形进度指示器动画

#### 持续加载对话框 (showPersistentLoadingDialog)
```dart
static VoidCallback showPersistentLoadingDialog({
  required BuildContext context,
  required String message,
  bool barrierDismissible = false,
})
```

特点：
- 使用Dialog实现，会阻挡背景操作
- 支持设置是否可点击背景关闭
- 更强的视觉焦点，适合重要操作

### 2. FunctionController改进

#### 加载状态管理
```dart
class FunctionController extends ChangeNotifier {
  // 加载提示管理
  VoidCallback? _currentLoadingDismiss;
  
  // 显示持续加载提示
  void _showPersistentLoadingMessage(String message);
  
  // 关闭当前加载提示
  void _dismissCurrentLoadingMessage();
  
  // 显示成功并关闭加载提示
  void _showSuccessAndDismissLoading(String message);
  
  // 显示错误并关闭加载提示
  void _showErrorAndDismissLoading(String message);
}
```

#### 请求流程优化
1. **请求开始**: 显示持续加载提示
2. **请求发送**: 保持加载提示显示
3. **收到响应**: 关闭加载提示，显示结果
4. **请求超时**: 关闭加载提示，显示超时提示

## 📱 用户交互流程

### 场景1: 点击侧边栏功能菜单
1. 用户点击"功能设置"菜单
2. 显示"切换到功能设置"短暂提示 (0.8秒)
3. FunctionController自动请求数据
4. 显示"正在向服务器请求功能配置..."持续提示
5. 收到响应后关闭加载提示
6. 显示"功能配置加载成功！共加载 X 个配置"成功提示

### 场景2: 点击页面刷新按钮
1. 用户点击功能页面的刷新按钮
2. 立即显示"正在向服务器请求功能配置..."持续提示
3. 发送请求到服务器
4. 收到响应后关闭加载提示
5. 显示相应的成功或错误提示

### 场景3: 侧边栏全局刷新
1. 用户点击侧边栏的刷新按钮
2. 显示"正在刷新所有页面数据..."提示
3. 如果当前在功能页面，额外显示特殊提示
4. 各页面独立处理自己的加载状态

## 🎨 视觉设计

### 持续加载提示样式
```dart
Container(
  padding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
  decoration: BoxDecoration(
    color: Colors.black87,
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: [...],
  ),
  child: Row(
    children: [
      CircularProgressIndicator(strokeWidth: 2.0, color: white),
      SizedBox(width: 12.0),
      Text(message, style: white text style),
    ],
  ),
)
```

### 颜色方案
- **加载提示背景**: `Colors.black87` (半透明黑色)
- **进度指示器**: `Colors.white` (白色)
- **文字颜色**: `Colors.white` (白色)
- **成功提示**: `Colors.green` (绿色)
- **错误提示**: `Colors.red` (红色)

## 🔍 调试功能

### 详细日志
```
FunctionController: 显示持续加载提示: 正在向服务器请求功能配置...
FunctionController: 🚀 开始请求功能配置
FunctionController: 📤 发送请求: {...}
FunctionController: 🔥 FunctionController收到服务器消息
FunctionController: 🎯 检测到功能配置读取响应
FunctionController: 关闭当前加载提示
FunctionController: 显示成功消息: 功能配置加载成功！共加载 4 个配置
```

### 错误处理日志
```
FunctionController: ❌ WebSocket未连接，无法请求配置
FunctionController: 关闭当前加载提示
FunctionController: 显示错误消息: WebSocket未连接，无法加载功能配置
```

## 🚀 优势特点

### 1. 用户体验提升
- **状态明确**: 用户清楚知道当前的加载状态
- **及时反馈**: 立即显示加载状态，无需等待
- **结果明确**: 成功或失败都有明确的反馈

### 2. 技术优势
- **内存安全**: 自动清理，防止内存泄漏
- **状态同步**: 确保UI状态与业务状态一致
- **可扩展性**: 其他页面可以复用相同的模式

### 3. 开发友好
- **调试方便**: 详细的日志输出
- **代码清晰**: 职责分离，易于维护
- **可配置**: 支持自定义消息和位置

## 📊 性能考虑

### 1. 内存管理
- 使用VoidCallback管理Overlay生命周期
- 页面销毁时自动清理所有提示
- 避免重复创建Overlay Entry

### 2. 用户交互
- 不阻塞其他UI操作（使用Overlay而非Dialog）
- 支持快速连续操作的防抖动机制
- 自动超时处理，避免无限等待

### 3. 网络优化
- 防重复请求机制
- 合理的超时时间设置（10秒）
- 详细的错误分类和处理

## 🔄 使用示例

### 基础使用
```dart
// 显示持续加载提示
final dismiss = MessageComponent.showPersistentLoading(
  context: context,
  message: '正在处理...',
);

// 处理完成后关闭
dismiss();
```

### 在Controller中使用
```dart
void requestData() {
  _showPersistentLoadingMessage('正在请求数据...');
  
  try {
    // 发送请求
    await sendRequest();
    _showSuccessAndDismissLoading('数据加载成功');
  } catch (e) {
    _showErrorAndDismissLoading('加载失败: $e');
  }
}
```

## 🎯 后续优化方向

1. **进度百分比**: 支持显示具体的加载进度
2. **取消功能**: 允许用户取消正在进行的请求
3. **批量操作**: 支持多个并发请求的状态管理
4. **动画优化**: 更流畅的显示/隐藏动画效果 