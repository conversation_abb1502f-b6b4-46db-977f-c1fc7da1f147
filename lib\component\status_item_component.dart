// ignore_for_file: use_super_parameters

import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';

/// 状态项显示模式枚举
enum StatusItemDisplayMode {
  /// 仅显示文字
  textOnly,
  
  /// 仅显示图标
  iconOnly,
  
  /// 显示图标和文字
  iconWithText,
}

/// 状态项组件配置
class StatusItemConfig {
  final double fontSize;
  final double iconSize;
  final double spacing;
  final Color normalColor;
  final Color errorColor;
  final Color unknownColor;
  final FontWeight fontWeight;
  
  const StatusItemConfig({
    this.fontSize = 12.0,
    this.iconSize = 16.0,
    this.spacing = 4.0,
    this.normalColor = Colors.green,
    this.errorColor = Colors.red,
    this.unknownColor = Colors.grey,
    this.fontWeight = FontWeight.w500,
  });
}

/// 状态项容器配置
class StatusItemContainerConfig {
  final int maxItems;
  final double iconSize;
  final double itemSpacing;
  final double rowSpacing;
  final double padding;
  final EdgeInsets containerPadding;
  final bool useMultiRow;
  final int itemsPerRow;
  
  const StatusItemContainerConfig({
    required this.maxItems,
    required this.iconSize,
    required this.itemSpacing,
    required this.rowSpacing,
    required this.padding,
    required this.containerPadding,
    required this.useMultiRow,
    required this.itemsPerRow,
  });
}

/// 状态项数据模型
class StatusItemData {
  final String label;
  final bool isNormal;
  final String? value;
  final VoidCallback? onTap;
  
  const StatusItemData({
    required this.label,
    required this.isNormal,
    this.value,
    this.onTap,
  });
}

/// 状态项容器组件 - 负责管理多个状态项的布局，基于GetWidget封装
class StatusItemContainer extends StatelessWidget {
  final List<StatusItemData> items;
  final StatusItemContainerConfig config;
  final Color? borderColor;
  final Color? backgroundColor;
  
  const StatusItemContainer({
    Key? key,
    required this.items,
    required this.config,
    this.borderColor,
    this.backgroundColor,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final displayItems = items.take(config.maxItems).toList();
    
    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算可用宽度，考虑容器内边距
        final availableWidth = constraints.maxWidth - 
            config.containerPadding.horizontal;
        
        // 如果空间太小，使用紧凑模式
        if (availableWidth < 60) {
          return _buildCompactMode(displayItems);
        }
        
        return GFCard(
          padding: config.containerPadding,
          margin: EdgeInsets.zero,
          elevation: 1.0,
          color: backgroundColor ?? Colors.grey.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(4.0),
          content: config.useMultiRow 
              ? _buildMultiRowLayout(displayItems, availableWidth)
              : _buildSingleRowLayout(displayItems, availableWidth),
        );
      },
    );
  }
  
  Widget _buildCompactMode(List<StatusItemData> items) {
    // 紧凑模式：只显示一个状态指示器
    final normalCount = items.where((item) => item.isNormal).length;
    final totalCount = items.length;
    final isAllNormal = normalCount == totalCount;
    
    return GFBadge(
      color: isAllNormal ? Colors.green : Colors.red,
      size: 24.0,
      shape: GFBadgeShape.circle,
      child: Icon(
        isAllNormal ? Icons.check : Icons.warning,
        color: Colors.white,
        size: 12,
      ),
    );
  }
  
  Widget _buildSingleRowLayout(List<StatusItemData> items, double availableWidth) {
    // 计算每个项目的估计宽度（图标 + 间距）
    final totalSpacing = (items.length - 1) * config.itemSpacing;
    final totalItemsWidth = items.length * config.iconSize;
    final totalRequiredWidth = totalItemsWidth + totalSpacing;
    
    // 如果总宽度超过可用宽度，启用水平滚动
    if (totalRequiredWidth > availableWidth) {
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: _intersperse(
            items.map((item) => _buildStatusItem(item)).toList(),
            SizedBox(width: config.itemSpacing),
          ),
        ),
      );
    }
    
    // 如果空间足够，正常显示
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: _intersperse(
        items.map((item) => _buildStatusItem(item)).toList(),
        SizedBox(width: config.itemSpacing),
      ),
    );
  }
  
  Widget _buildMultiRowLayout(List<StatusItemData> items, double availableWidth) {
    // 计算每行可以放置的项目数量
    final estimatedItemWidth = config.iconSize + config.itemSpacing;
    final maxItemsPerRow = (availableWidth / estimatedItemWidth).floor().clamp(1, config.itemsPerRow);
    
    final rows = _splitItemsIntoRows(items, maxItemsPerRow);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: rows.asMap().entries.map((entry) {
        final index = entry.key;
        final rowItems = entry.value;
        final widgets = rowItems.map((item) => _buildStatusItem(item)).toList();
        
        return Padding(
          padding: EdgeInsets.only(top: index > 0 ? config.rowSpacing : 0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: _intersperse(widgets, SizedBox(width: config.itemSpacing)),
          ),
        );
      }).toList(),
    );
  }
  
  Widget _buildStatusItem(StatusItemData item) {
    final itemConfig = StatusItemConfig(
      iconSize: config.iconSize,
      spacing: config.itemSpacing,
      normalColor: Colors.green,
      errorColor: Colors.red,
    );
    
    if (item.value != null) {
      return StatusItemComponent.createPerformanceStatus(
        label: item.label,
        isNormal: item.isNormal,
        value: item.value!,
        displayMode: StatusItemDisplayMode.iconOnly,
        config: itemConfig,
        onTap: item.onTap,
      );
    } else {
      return StatusItemComponent.createServiceStatus(
        label: item.label,
        isNormal: item.isNormal,
        displayMode: StatusItemDisplayMode.iconOnly,
        config: itemConfig,
        onTap: item.onTap,
      );
    }
  }
  
  List<List<StatusItemData>> _splitItemsIntoRows(List<StatusItemData> items, int itemsPerRow) {
    final rows = <List<StatusItemData>>[];
    for (int i = 0; i < items.length; i += itemsPerRow) {
      final end = (i + itemsPerRow < items.length) ? i + itemsPerRow : items.length;
      rows.add(items.sublist(i, end));
    }
    return rows;
  }
  
  List<Widget> _intersperse(List<Widget> items, Widget separator) {
    if (items.isEmpty) return items;
    
    final result = <Widget>[];
    for (int i = 0; i < items.length; i++) {
      result.add(items[i]);
      if (i < items.length - 1) {
        result.add(separator);
      }
    }
    return result;
  }
}

/// 通用状态项组件 - 基于GetWidget封装
/// 支持图标和文字两种显示模式
/// 
/// 使用示例：
/// ```dart
/// // 创建服务状态项
/// StatusItemComponent.createServiceStatus(
///   label: '数据库',
///   isNormal: true,
///   displayMode: StatusItemDisplayMode.iconOnly,
/// )
/// 
/// // 创建性能状态项
/// StatusItemComponent.createPerformanceStatus(
///   label: '帧率',
///   isNormal: true,
///   value: '60fps',
///   displayMode: StatusItemDisplayMode.iconWithText,
/// )
/// 
/// // 创建徽章样式状态项
/// StatusItemComponent.createBadgeStatus(
///   label: '在线',
///   isNormal: true,
///   shape: GFBadgeShape.circle,
/// )
/// 
/// // 状态项容器
/// StatusItemContainer(
///   items: [
///     StatusItemData(label: '数据库', isNormal: true),
///     StatusItemData(label: '推理服务', isNormal: false),
///   ],
///   config: StatusItemContainerPresets.medium,
/// )
/// ```
class StatusItemComponent extends StatelessWidget {
  final String label;
  final bool isNormal;
  final String? value;
  final IconData? normalIcon;
  final IconData? errorIcon;
  final IconData? unknownIcon;
  final StatusItemDisplayMode displayMode;
  final StatusItemConfig config;
  final VoidCallback? onTap;
  final String? tooltip;

  const StatusItemComponent({
    Key? key,
    required this.label,
    required this.isNormal,
    this.value,
    this.normalIcon,
    this.errorIcon,
    this.unknownIcon,
    this.displayMode = StatusItemDisplayMode.textOnly,
    this.config = const StatusItemConfig(),
    this.onTap,
    this.tooltip,
  }) : super(key: key);

  /// 创建状态项组件的工厂方法
  static Widget create({
    required String label,
    required bool isNormal,
    String? value,
    IconData? normalIcon,
    IconData? errorIcon,
    IconData? unknownIcon,
    StatusItemDisplayMode displayMode = StatusItemDisplayMode.textOnly,
    StatusItemConfig config = const StatusItemConfig(),
    VoidCallback? onTap,
    String? tooltip,
  }) {
    return StatusItemComponent(
      label: label,
      isNormal: isNormal,
      value: value,
      normalIcon: normalIcon,
      errorIcon: errorIcon,
      unknownIcon: unknownIcon,
      displayMode: displayMode,
      config: config,
      onTap: onTap,
      tooltip: tooltip,
    );
  }

  /// 创建服务状态项（数据库、推理服务等）
  static Widget createServiceStatus({
    required String label,
    required bool isNormal,
    StatusItemDisplayMode displayMode = StatusItemDisplayMode.iconOnly,
    StatusItemConfig? config,
    VoidCallback? onTap,
    String? tooltip,
  }) {
    final serviceIcon = _getServiceIcon(label, isNormal);
    return StatusItemComponent.create(
      label: label,
      isNormal: isNormal,
      normalIcon: serviceIcon,
      errorIcon: serviceIcon, // 异常时也使用相同的服务图标
      displayMode: displayMode,
      config: config ?? const StatusItemConfig(),
      onTap: onTap,
      tooltip: tooltip ?? '$label: ${isNormal ? "正常" : "异常"}',
    );
  }

  /// 创建性能状态项（帧率、分辨率等）
  static Widget createPerformanceStatus({
    required String label,
    required bool isNormal,
    required String value,
    StatusItemDisplayMode displayMode = StatusItemDisplayMode.iconWithText,
    StatusItemConfig? config,
    VoidCallback? onTap,
    String? tooltip,
  }) {
    final performanceIcon = _getPerformanceIcon(label);
    return StatusItemComponent.create(
      label: label,
      isNormal: isNormal,
      value: value,
      normalIcon: performanceIcon,
      errorIcon: performanceIcon, // 异常时也使用相同的性能图标
      displayMode: displayMode,
      config: config ?? const StatusItemConfig(),
      onTap: onTap,
      tooltip: tooltip ?? '$label: $value',
    );
  }

  /// 创建基于GFBadge的状态项
  static Widget createBadgeStatus({
    required String label,
    required bool isNormal,
    String? value,
    GFBadgeShape shape = GFBadgeShape.standard,
    double size = 16.0,
    VoidCallback? onTap,
  }) {
    final color = isNormal ? Colors.green : Colors.red;
    final displayText = value ?? label;
    
    Widget badge = GFBadge(
      text: displayText,
      color: color,
      shape: shape,
      size: size,
    );
    
    if (onTap != null) {
      badge = GFButton(
        onPressed: onTap,
        type: GFButtonType.transparent,
        padding: EdgeInsets.zero,
        child: badge,
      );
    }
    
    return badge;
  }

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor();
    final icon = _getStatusIcon();
    
    Widget content = _buildContent(color, icon);
    
    if (onTap != null) {
      // 使用GFButton包装可点击的状态项
      content = GFButton(
        onPressed: onTap,
        type: GFButtonType.transparent,
        padding: EdgeInsets.all(_calculatePadding()),
        child: content,
      );
    }
    
    if (tooltip != null && tooltip!.isNotEmpty) {
      content = Tooltip(
        message: tooltip!,
        preferBelow: false,
        waitDuration: const Duration(milliseconds: 500),
        child: content,
      );
    }
    
    return content;
  }

  /// 构建内容
  Widget _buildContent(Color color, IconData? icon) {
    switch (displayMode) {
      case StatusItemDisplayMode.textOnly:
        return _buildTextOnly(color);
      
      case StatusItemDisplayMode.iconOnly:
        return _buildIconOnly(color, icon);
      
      case StatusItemDisplayMode.iconWithText:
        return _buildIconWithText(color, icon);
    }
  }

  /// 构建仅文字模式
  Widget _buildTextOnly(Color color) {
    final displayText = value != null && value!.isNotEmpty 
        ? '$label: $value' 
        : label;
    
    return Text(
      displayText,
      style: TextStyle(
        fontSize: config.fontSize,
        color: color,
        fontWeight: config.fontWeight,
      ),
    );
  }

  /// 构建仅图标模式
  Widget _buildIconOnly(Color color, IconData? icon) {
    if (icon == null) {
      // 如果没有图标，回退到文字模式
      return _buildTextOnly(color);
    }
    
    return Icon(
      icon,
      color: color,
      size: config.iconSize,
    );
  }

  /// 构建图标+文字模式
  Widget _buildIconWithText(Color color, IconData? icon) {
    final displayText = value != null && value!.isNotEmpty 
        ? '$label: $value' 
        : label;
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            color: color,
            size: config.iconSize,
          ),
          SizedBox(width: config.spacing),
        ],
        Text(
          displayText,
          style: TextStyle(
            fontSize: config.fontSize,
            color: color,
            fontWeight: config.fontWeight,
          ),
        ),
      ],
    );
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    // 完全基于isNormal参数来决定颜色，不检查数值有效性
    return isNormal ? config.normalColor : config.errorColor;
  }

  /// 获取状态图标
  IconData? _getStatusIcon() {
    // 始终返回对应的图标，不根据数值有效性改变图标
    return isNormal ? normalIcon : errorIcon;
  }

  /// 获取服务图标
  static IconData _getServiceIcon(String serviceName, bool isNormal) {
    // 无论状态如何，都返回对应的服务图标
    switch (serviceName) {
      case '数据库':
        return Icons.storage;
      case '推理服务':
        return Icons.psychology;
      case '卡密':
        return Icons.key;
      case '键鼠':
        return Icons.mouse;
      default:
        return Icons.check_circle;
    }
  }

  /// 获取性能图标
  static IconData _getPerformanceIcon(String performanceName) {
    switch (performanceName) {
      case '帧率':
        return Icons.speed;
      case '分辨率':
        return Icons.aspect_ratio;
      default:
        return Icons.monitor;
    }
  }

  double _calculatePadding() {
    if (config.iconSize <= 10.0) {
      return 1.0;
    }
    return config.iconSize <= 16.0 ? 3.0 : 4.0;
  }
}

/// 状态项组件的预设配置
class StatusItemPresets {
  /// 小尺寸配置
  static const StatusItemConfig small = StatusItemConfig(
    fontSize: 10.0,
    iconSize: 14.0,
    spacing: 3.0,
  );
  
  /// 中等尺寸配置
  static const StatusItemConfig medium = StatusItemConfig(
    fontSize: 12.0,
    iconSize: 16.0,
    spacing: 4.0,
  );
  
  /// 大尺寸配置
  static const StatusItemConfig large = StatusItemConfig(
    fontSize: 14.0,
    iconSize: 18.0,
    spacing: 5.0,
  );
  
  /// 页头专用配置
  static const StatusItemConfig header = StatusItemConfig(
    fontSize: 12.0,
    iconSize: 16.0,
    spacing: 4.0,
    normalColor: Colors.green,
    errorColor: Colors.red,
    unknownColor: Colors.grey,
    fontWeight: FontWeight.w500,
  );

  /// 移动端页头配置（更大的图标，更好的触摸体验）
  static const StatusItemConfig mobileHeader = StatusItemConfig(
    fontSize: 11.0,
    iconSize: 18.0,
    spacing: 3.0,
    normalColor: Colors.green,
    errorColor: Colors.red,
    unknownColor: Colors.grey,
    fontWeight: FontWeight.w500,
  );
}

/// 状态项容器预设配置
class StatusItemContainerPresets {
  static const StatusItemContainerConfig extraSmall = StatusItemContainerConfig(
    maxItems: 6,
    iconSize: 10.0,
    itemSpacing: 3.0,
    rowSpacing: 0.0,
    padding: 1.0,
    containerPadding: EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
    useMultiRow: false,
    itemsPerRow: 6,
  );
  
  static const StatusItemContainerConfig small = StatusItemContainerConfig(
    maxItems: 6,
    iconSize: 12.0,
    itemSpacing: 4.0,
    rowSpacing: 0.0,
    padding: 1.5,
    containerPadding: EdgeInsets.symmetric(horizontal: 6.0, vertical: 3.0),
    useMultiRow: false,
    itemsPerRow: 6,
  );
  
  static const StatusItemContainerConfig medium = StatusItemContainerConfig(
    maxItems: 6,
    iconSize: 14.0,
    itemSpacing: 6.0,
    rowSpacing: 0.0,
    padding: 2.0,
    containerPadding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
    useMultiRow: false,
    itemsPerRow: 6,
  );
  
  static const StatusItemContainerConfig large = StatusItemContainerConfig(
    maxItems: 6,
    iconSize: 16.0,
    itemSpacing: 8.0,
    rowSpacing: 0.0,
    padding: 3.0,
    containerPadding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 5.0),
    useMultiRow: false,
    itemsPerRow: 6,
  );
} 