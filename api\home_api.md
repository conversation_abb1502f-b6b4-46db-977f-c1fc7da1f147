# 首页配置系统API

本文档描述首页配置系统的WebSocket API请求和响应格式。

## 请求和响应格式

系统使用两种主要操作：
- `home_read`: 读取首页配置
- `home_modify`: 修改首页配置

### 读取首页配置 (home_read)

**客户端请求**：

```json
{
  "action": "home_read",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "isPro": true,
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

**服务器响应**：

```json
{
  "action": "home_read",
  "status": "ok",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "cardKeyExpireTime": "2024-12-31T23:59:59Z",
    "updatedAt": "2023-06-01T13:30:00Z"
  }
}
```

### 修改首页配置 (home_modify)

**客户端请求**：

```json
{
  "action": "home_modify",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "isPro": true,
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

**服务器响应**：

```json
{
  "action": "home_modify_response",
  "status": "ok",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "cardKeyExpireTime": "2024-12-31T23:59:59Z",
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

## 参数说明

| 参数名称 | 类型 | 描述 | 可选值 | 备注 |
|---------|------|------|-------|------|
| username | string | 用户名 | 任意字符串 | |
| gameName | string | 游戏名称 | apex, cf, cfhd, csgo2, pubg, sjz, ssjj2, wwqy | |
| cardKey | string | 用户卡密 | 任意字符串 | |
| cardKeyExpireTime | string | 卡密到期时间 | ISO8601格式的时间字符串 | **仅在服务器响应中返回，不保存到数据库** |
| isPro | boolean | 是否为Pro用户 | true, false | **仅用于状态标识，不保存到数据库** |
| updatedAt | string | 更新时间戳 | ISO8601格式的时间字符串 | |

## 特殊说明

### isPro字段说明
- **用途**: 标识当前用户是否为Pro用户，用于服务器端的权限判断和功能控制
- **数据流向**: 仅从客户端发送到服务器，服务器响应中不包含此字段
- **存储策略**: **该字段不会保存到数据库中**，仅作为请求时的状态标识使用
- **获取方式**: 客户端从本地登录状态中获取Pro信息并在每次请求时发送

### cardKeyExpireTime字段说明
- **用途**: 显示用户卡密的到期时间，用于前端UI展示
- **数据流向**: 仅从服务器返回到客户端，客户端不发送此字段
- **存储策略**: **该字段不会保存到数据库中**，由后端根据卡密实时计算并返回
- **格式**: ISO8601格式的时间字符串，如 "2024-12-31T23:59:59Z"
- **显示用途**: 前端可以根据此时间计算剩余天数并在UI中显示