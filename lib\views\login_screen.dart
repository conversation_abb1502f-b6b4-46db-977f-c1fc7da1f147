// ignore_for_file: use_super_parameters, library_private_types_in_public_api, avoid_print, use_build_context_synchronously, deprecated_member_use, unused_import

import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:provider/provider.dart';
import '../component/Button_component.dart';
import '../component/input_component.dart';
import '../component/background_component.dart';
import '../component/status_display_component.dart';
import '../controllers/login_controller.dart';
import '../services/server_service.dart';
import '../models/login_model.dart';

/// 登录界面
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  // 🎨 移动端紧凑设计常量
  static const double _cardWidth = 440.0;

  // 响应式间距 - 移动端更紧凑
  static double _getCardPadding(BuildContext context) {
    return MediaQuery.of(context).size.width < 600 ? 20.0 : 32.0;
  }

  static double _getTitleFontSize(BuildContext context) {
    return MediaQuery.of(context).size.width < 600 ? 26.0 : 32.0;
  }



  static double _getSpacing(BuildContext context) {
    return MediaQuery.of(context).size.width < 600 ? 12.0 : 20.0;
  }

  static double _getInputSpacing(BuildContext context) {
    return MediaQuery.of(context).size.width < 600 ? 16.0 : 24.0;
  }

  static double _getSectionSpacing(BuildContext context) {
    return MediaQuery.of(context).size.width < 600 ? 20.0 : 32.0;
  }
  
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  void _initAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<ServerService, LoginModel, LoginController>(
      builder: (context, serverService, loginModel, controller, _) {
        return SafeArea( // 添加安全区域适配
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFFF8FAFC), // 更纯净的白色
                  const Color(0xFFEBF4FF), // 柔和的蓝色
                  const Color(0xFFDEEAFF), // 深一点的蓝色
                  Theme.of(context).colorScheme.primary.withOpacity(0.08),
                ],
                stops: const [0.0, 0.4, 0.7, 1.0],
              ),
            ),
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(), // iOS风格的弹性滚动
                  keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag, // 拖拽时隐藏键盘
                  padding: EdgeInsets.symmetric(
                    horizontal: MediaQuery.of(context).size.width < 600 ? 16.0 : 20.0,
                    vertical: MediaQuery.of(context).size.width < 600 ? 20.0 : 32.0,
                  ),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width > 600 ? 480 : double.infinity,
                      minHeight: MediaQuery.of(context).size.height - (MediaQuery.of(context).size.width < 600 ? 80 : 120),
                    ),
                    child: Center(
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: SlideTransition(
                          position: _slideAnimation,
                          child: _buildLoginCard(context, serverService, controller),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ); // SafeArea闭合
      },
    );
  }

  Widget _buildLoginCard(BuildContext context, ServerService serverService, LoginController controller) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;
    final cardWidth = isMobile ? screenWidth * 0.95 : _cardWidth;
    final borderRadius = isMobile ? 16.0 : 24.0;

    return Container(
      width: cardWidth,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        color: Colors.white,
        border: Border.all(
          color: Colors.grey.withOpacity(0.1),
          width: 1,
        ),
        boxShadow: [
          // 主阴影
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 32,
            offset: const Offset(0, 16),
            spreadRadius: 0,
          ),
          // 细节阴影
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          // 环境光阴影
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.05),
            blurRadius: 64,
            offset: const Offset(0, 32),
            spreadRadius: -8,
          ),
        ],
      ),
      child: Container(
        padding: EdgeInsets.all(_getCardPadding(context)),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildHeader(context),
              SizedBox(height: _getSectionSpacing(context)),
              _buildInputFields(controller, context),
              SizedBox(height: _getSpacing(context)),
              _buildHorizontalButtons(serverService, controller, context),
              SizedBox(height: _getSpacing(context)),
              _buildStatusIndicators(serverService, controller),
              SizedBox(height: _getSectionSpacing(context)),
              _buildRegisterButton(controller),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final logoSize = isMobile ? 60.0 : 80.0;

    return Column(
      children: [
        // Logo或图标区域 - 移动端更小
        Container(
          width: logoSize,
          height: logoSize,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                blurRadius: 16,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Icon(
            Icons.security,
            color: Colors.white,
            size: isMobile ? 32 : 40,
          ),
        ),
        SizedBox(height: isMobile ? 16 : 24),

        // 主标题
        Text(
          '欢迎登录',
          style: TextStyle(
            fontSize: _getTitleFontSize(context),
            fontWeight: FontWeight.bold,
            color: Colors.black87,
            letterSpacing: -0.5,
          ),
        ),
      ],
    );
  }

  Widget _buildInputFields(LoginController controller, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 服务器配置区域（水平排列）
        InputComponent.createHorizontalInputs(
          inputs: controller.getServerInputFieldsData(),
          controllers: [
            controller.serverAddressController,
            controller.portController,
          ],
          spacing: _getSpacing(context),
          passwordVisibleList: const [false, false], // 服务器配置不需要密码
          onTogglePasswordVisibility: null,
        ),

        SizedBox(height: _getInputSpacing(context)),

        // 用户认证区域（垂直排列）
        InputComponent.createMultiInputs(
          inputs: controller.getUserInputFieldsData(),
          controllers: [
            controller.usernameController,
            controller.passwordController,
          ],
          spacing: _getInputSpacing(context),
          passwordVisibleList: controller.passwordVisibleList.skip(2).toList(), // 跳过前两个（服务器配置）
          onTogglePasswordVisibility: (index, visible) {
            // 调整索引，因为密码字段在用户认证区域的第二个位置（index=1）
            controller.updatePasswordVisibility(index + 2, visible);
          },
        ),
      ],
    );
  }

  Widget _buildStatusIndicators(ServerService serverService, LoginController controller) {
    return Column(
      children: [
        StatusDisplayComponent.createServerStatus(
          isConnected: serverService.isConnected,
          isConnecting: serverService.isConnecting,
          hasError: serverService.hasError,
          errorText: serverService.errorText,
          serverAddress: serverService.serverAddress,
          serverPort: serverService.serverPort,
        ),
        if (controller.isLoading)
          StatusDisplayComponent.create(
            message: '正在登录，请稍候...',
            type: StatusDisplayType.loading,
          ),
      ],
    );
  }

  /// 水平排列的按钮组（连接服务器 + 登录）
  Widget _buildHorizontalButtons(ServerService serverService, LoginController controller, BuildContext context) {
    final isConnectedOrConnecting = serverService.isConnected || serverService.isConnecting;
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Row(
      children: [
        // 连接服务器按钮
        Expanded(
          flex: 5, // 调整为更协调的比例
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: serverService.isConnected ? [
                BoxShadow(
                  color: Colors.green.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ] : null,
            ),
            child: Button.create(ButtonConfig.textWithIcon(
              _getConnectButtonLabel(serverService),
              _getConnectButtonIcon(serverService),
              onPressed: isConnectedOrConnecting ? null : () => controller.handleConnectService(context),
              type: _getConnectButtonType(serverService),
              fullWidth: true,
              size: isMobile ? ButtonSize.medium : ButtonSize.large,
            )),
          ),
        ),

        SizedBox(width: _getSpacing(context)),

        // 登录按钮
        Expanded(
          flex: 6, // 调整为更协调的比例，登录按钮稍大一些
          child: Button.create(ButtonConfig.textWithIcon(
            _getLoginButtonText(controller),
            _getLoginButtonIconData(controller),
            onPressed: _getLoginButtonOnPressed(controller),
            type: ButtonType.primary,
            fullWidth: true,
            size: isMobile ? ButtonSize.medium : ButtonSize.large,
          )),
        ),
      ],
    );
  }



  String _getConnectButtonLabel(ServerService serverService) {
    final label = serverService.isConnecting
        ? '连接中...'
        : serverService.isConnected
            ? '已连接' // 简化文本，减少字符数
            : '连接服务器';

    // 添加调试信息
    print('[LoginScreen] 按钮标签: $label, isConnecting: ${serverService.isConnecting}, isConnected: ${serverService.isConnected}, 实例: ${serverService.hashCode}');

    return label;
  }

  IconData _getConnectButtonIcon(ServerService serverService) {
    if (serverService.isConnecting) return Icons.sync;
    if (serverService.isConnected) return Icons.check_circle;
    return Icons.link;
  }

  ButtonType _getConnectButtonType(ServerService serverService) {
    if (serverService.isConnected) return ButtonType.success;
    if (serverService.isConnecting) return ButtonType.secondary;
    return ButtonType.outline; // 未连接时使用outline样式，更协调
  }





  VoidCallback? _getLoginButtonOnPressed(LoginController controller) {
    if (controller.isLoading) return null;

    return () {
      if (_formKey.currentState?.validate() ?? false) {
        controller.handleLogin(context);
      }
    };
  }

  String _getLoginButtonText(LoginController controller) {
    if (controller.isLoading) return '登录中...';
    return '登录';
  }

  IconData _getLoginButtonIconData(LoginController controller) {
    if (controller.isLoading) return Icons.sync;
    return Icons.login;
  }

  Widget _buildRegisterButton(LoginController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 4),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 提示文本
          Text(
            '还没有账号？',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 15,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(width: 8),
          // 注册按钮 - 使用TextButton样式更紧凑
          TextButton(
            onPressed: () => controller.handleRegister(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              '立即注册',
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                decoration: TextDecoration.underline,
                decorationColor: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
