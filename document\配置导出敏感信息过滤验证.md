# 配置导出敏感信息过滤验证

## 📋 安全要求

**绝对不能导出真实的用户名和卡密！** 这是最重要的安全要求，必须确保：
- 导出的配置文件不包含任何真实的用户身份信息
- 剪切板中的配置数据不包含敏感信息
- 即使有敏感字段，也必须用占位符替换

## 🔧 增强的过滤机制

### 1. 多层次敏感信息过滤

#### 顶层过滤
```dart
// 移除顶层的敏感信息
filteredData.remove('username');
filteredData.remove('cardKey');
```

#### 嵌套结构过滤
```dart
// 处理嵌套在content中的敏感信息
if (filteredData.containsKey('content') && filteredData['content'] is Map<String, dynamic>) {
  final content = Map<String, dynamic>.from(filteredData['content'] as Map<String, dynamic>);
  
  // 用占位符替换敏感信息
  if (content.containsKey('username')) {
    content['username'] = '[FILTERED]'; // 用占位符替换
  }
  
  if (content.containsKey('cardKey')) {
    content['cardKey'] = '[FILTERED]'; // 用占位符替换
  }
  
  filteredData['content'] = content;
}
```

#### 全模块扫描过滤
```dart
/// 过滤所有模块中的敏感信息
void _filterSensitiveDataInAllModules(Map<String, dynamic> data) {
  final sensitiveFields = ['username', 'cardKey'];
  
  for (final entry in data.entries) {
    if (entry.value is Map<String, dynamic>) {
      final moduleData = entry.value as Map<String, dynamic>;
      
      // 检查模块顶层
      for (final field in sensitiveFields) {
        if (moduleData.containsKey(field)) {
          moduleData[field] = '[FILTERED]';
        }
      }
      
      // 检查content子对象
      if (moduleData.containsKey('content') && moduleData['content'] is Map<String, dynamic>) {
        final content = moduleData['content'] as Map<String, dynamic>;
        for (final field in sensitiveFields) {
          if (content.containsKey(field)) {
            content[field] = '[FILTERED]';
          }
        }
      }
    }
  }
}
```

### 2. metadata信息过滤

```dart
// 添加游戏信息（不包含敏感的用户信息）
result['metadata'] = {
  'username': '[FILTERED]', // 过滤用户名
  'currentGame': _gameModel.currentGame,
  'exportTime': DateTime.now().toIso8601String(),
};
```

### 3. 全局过滤应用

在配置数据收集的最后阶段，对整个结果进行扫描：

```dart
// 最后一步：对整个结果进行敏感信息过滤
_filterSensitiveDataInAllModules(result);
_logger.i(_tag, '已完成所有配置数据的敏感信息过滤');
```

## ✅ 预期的导出结果

### 修复前（不安全）
```json
{
  "home": {
    "action": "home_modify",
    "content": {
      "username": "yibao",                    // ❌ 真实用户名
      "cardKey": "0faf2d66-9126-4523-aa9c-3de93e13f15e", // ❌ 真实卡密
      "gameName": "cf",
      "heartbeatInterval": 2
    }
  },
  "pid": {
    "action": "pid_modify",
    "content": {
      "username": "yibao",                    // ❌ 真实用户名
      "gameName": "cf",
      "nearMoveFactor": 1.3
    }
  },
  "metadata": {
    "username": "yibao",                      // ❌ 真实用户名
    "currentGame": "cf",
    "exportTime": "2025-07-18T13:57:45.242404"
  }
}
```

### 修复后（安全）
```json
{
  "home": {
    "action": "home_modify",
    "content": {
      "username": "[FILTERED]",              // ✅ 已过滤
      "cardKey": "[FILTERED]",               // ✅ 已过滤
      "gameName": "cf",                      // ✅ 保留技术配置
      "heartbeatInterval": 2                 // ✅ 保留技术配置
    }
  },
  "pid": {
    "action": "pid_modify",
    "content": {
      "username": "[FILTERED]",              // ✅ 已过滤
      "gameName": "cf",                      // ✅ 保留技术配置
      "nearMoveFactor": 1.3                  // ✅ 保留技术配置
    }
  },
  "metadata": {
    "username": "[FILTERED]",               // ✅ 已过滤
    "currentGame": "cf",                     // ✅ 保留游戏信息
    "exportTime": "2025-07-18T13:57:45.242404" // ✅ 保留时间戳
  }
}
```

## 🔍 验证方法

### 1. 导出测试
1. **执行导出操作**（文件导出或剪切板导出）
2. **检查导出结果**：
   - 搜索真实用户名，应该找不到
   - 搜索真实卡密，应该找不到
   - 所有敏感字段都应该显示为`[FILTERED]`

### 2. 日志验证
查看控制台日志，应该看到：
```
[ConfigManagementController] 已将username替换为占位符
[ConfigManagementController] 已将cardKey替换为占位符
[ConfigManagementController] 已过滤模块 pid.content 中的 username
[ConfigManagementController] 已过滤模块 fov.content 中的 username
[ConfigManagementController] 已完成所有配置数据的敏感信息过滤
```

### 3. 导入测试
1. **导入过滤后的配置**
2. **验证功能正常**：
   - 技术配置正确应用
   - 游戏选择正确切换
   - 用户身份保持为当前用户

## 🛡️ 安全保障

### 1. 多重防护
- **字段移除**：直接删除敏感字段
- **占位符替换**：用`[FILTERED]`替换敏感值
- **全局扫描**：确保没有遗漏的敏感信息

### 2. 结构化过滤
- **顶层过滤**：处理直接的敏感字段
- **嵌套过滤**：处理content对象中的敏感字段
- **模块扫描**：遍历所有模块进行过滤

### 3. 日志审计
- **过滤操作记录**：每次过滤都有日志记录
- **完成确认**：最终确认所有敏感信息已过滤
- **调试支持**：便于排查过滤问题

## 📱 用户体验

### 1. 透明过滤
- **用户无感知**：过滤过程对用户透明
- **功能完整**：技术配置完全保留
- **安全保障**：敏感信息完全保护

### 2. 分享安全
- **放心分享**：用户可以安全地分享配置
- **隐私保护**：接收方无法获取敏感信息
- **功能完整**：接收方可以正常使用配置

### 3. 导入兼容
- **向下兼容**：可以导入旧版本的配置
- **占位符处理**：正确处理`[FILTERED]`占位符
- **身份补充**：自动使用当前用户身份

## 🔄 测试清单

### 导出安全检查
- [ ] 文件导出不包含真实用户名
- [ ] 文件导出不包含真实卡密
- [ ] 剪切板导出不包含真实用户名
- [ ] 剪切板导出不包含真实卡密
- [ ] 所有敏感字段都显示为`[FILTERED]`

### 功能完整性检查
- [ ] 游戏配置参数完整保留
- [ ] PID参数完整保留
- [ ] FOV参数完整保留
- [ ] 瞄准参数完整保留
- [ ] 射击参数完整保留
- [ ] 数据收集参数完整保留

### 导入兼容性检查
- [ ] 可以正常导入过滤后的配置
- [ ] 技术参数正确应用
- [ ] 游戏选择正确切换
- [ ] 用户身份保持为当前用户

---

> **安全总结**: 通过多层次的敏感信息过滤机制，确保导出的配置绝对不包含真实的用户名和卡密
> **最后更新**: 2025年7月18日 | **维护者**: Flutter开发团队
