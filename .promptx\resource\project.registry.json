{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-23T10:13:39.481Z", "updatedAt": "2025-07-23T10:13:39.487Z", "resourceCount": 7}, "resources": [{"id": "flutter-development-workflow", "source": "project", "protocol": "execution", "name": "Flutter Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/flutter-developer/execution/flutter-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-23T10:13:39.483Z", "updatedAt": "2025-07-23T10:13:39.483Z", "scannedAt": "2025-07-23T10:13:39.483Z", "path": "domain/flutter-developer/execution/flutter-development-workflow.execution.md"}}, {"id": "mobile-app-quality-standards", "source": "project", "protocol": "execution", "name": "Mobile App Quality Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/flutter-developer/execution/mobile-app-quality-standards.execution.md", "metadata": {"createdAt": "2025-07-23T10:13:39.483Z", "updatedAt": "2025-07-23T10:13:39.483Z", "scannedAt": "2025-07-23T10:13:39.483Z", "path": "domain/flutter-developer/execution/mobile-app-quality-standards.execution.md"}}, {"id": "flutter-developer", "source": "project", "protocol": "role", "name": "Flutter Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/flutter-developer/flutter-developer.role.md", "metadata": {"createdAt": "2025-07-23T10:13:39.484Z", "updatedAt": "2025-07-23T10:13:39.484Z", "scannedAt": "2025-07-23T10:13:39.484Z", "path": "domain/flutter-developer/flutter-developer.role.md"}}, {"id": "dart-programming", "source": "project", "protocol": "knowledge", "name": "Dart Programming 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/flutter-developer/knowledge/dart-programming.knowledge.md", "metadata": {"createdAt": "2025-07-23T10:13:39.484Z", "updatedAt": "2025-07-23T10:13:39.484Z", "scannedAt": "2025-07-23T10:13:39.484Z", "path": "domain/flutter-developer/knowledge/dart-programming.knowledge.md"}}, {"id": "flutter-framework", "source": "project", "protocol": "knowledge", "name": "Flutter Framework 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/flutter-developer/knowledge/flutter-framework.knowledge.md", "metadata": {"createdAt": "2025-07-23T10:13:39.485Z", "updatedAt": "2025-07-23T10:13:39.485Z", "scannedAt": "2025-07-23T10:13:39.485Z", "path": "domain/flutter-developer/knowledge/flutter-framework.knowledge.md"}}, {"id": "mobile-ui-design", "source": "project", "protocol": "knowledge", "name": "Mobile Ui Design 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/flutter-developer/knowledge/mobile-ui-design.knowledge.md", "metadata": {"createdAt": "2025-07-23T10:13:39.486Z", "updatedAt": "2025-07-23T10:13:39.486Z", "scannedAt": "2025-07-23T10:13:39.486Z", "path": "domain/flutter-developer/knowledge/mobile-ui-design.knowledge.md"}}, {"id": "flutter-development", "source": "project", "protocol": "thought", "name": "Flutter Development 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/flutter-developer/thought/flutter-development.thought.md", "metadata": {"createdAt": "2025-07-23T10:13:39.487Z", "updatedAt": "2025-07-23T10:13:39.487Z", "scannedAt": "2025-07-23T10:13:39.487Z", "path": "domain/flutter-developer/thought/flutter-development.thought.md"}}], "stats": {"totalResources": 7, "byProtocol": {"execution": 2, "role": 1, "knowledge": 3, "thought": 1}, "bySource": {"project": 7}}}