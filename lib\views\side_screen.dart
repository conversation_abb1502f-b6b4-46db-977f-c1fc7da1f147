// ignore_for_file: use_super_parameters, library_private_types_in_public_api, unnecessary_brace_in_string_interps, deprecated_member_use, unused_import, unused_local_variable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;

import '../component/message_component.dart';
import '../component/Button_component.dart';
import '../models/sidebar_model.dart';
import '../controllers/side_controller.dart';

/// 侧边栏组件 - 提供导航菜单和页面切换功能
/// 
/// 永久显示，根据屏幕尺寸自动调整显示模式（展开/收起）
class SidebarScreen extends StatefulWidget {
  // 布局常量
  static const double minWidth = 48.0;
  static const double shadowBlurRadius = 8.0;
  static const double shadowOpacity = 0.15;
  static const double borderWidth = 3.0;
  static const double borderRadius = 12.0;
  static const double itemBorderRadius = 8.0;
  
  // 图标尺寸常量
  static const double iconSizeSelected = 26.0;
  static const double iconSizeNormal = 22.0;
  static const double iconSizeExpanded = 24.0;
  static const double iconSizeCollapsed = 20.0;
  
  // 字体尺寸常量
  static const double fontSizeSelected = 16.0;
  static const double fontSizeNormal = 14.0;
  static const double versionFontSize = 12.0;
  
  // 间距常量
  static const double paddingHorizontal = 16.0;
  static const double paddingVertical = 12.0;
  static const double paddingSmall = 8.0;
  static const double spacingMedium = 12.0;
  static const double buttonMinSize = 44.0;
  static const double itemSpacing = 4.0;
  
  // 颜色常量
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color primaryDarkColor = Color(0xFF1976D2);
  static const Color backgroundColor = Color(0xFFFAFAFA);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color activeColor = Color(0xFF1976D2);
  static const Color inactiveColor = Color(0xFF757575);
  static const Color hoverColor = Color(0xFFE3F2FD);
  static const Color selectedColor = Color(0xFFE8F4FD);
  static const Color dividerColor = Color(0xFFE0E0E0);
  
  // 刷新按钮专用配色
  static const Color refreshButtonStartColor = Color(0xFF9C27B0);  // 紫色
  static const Color refreshButtonEndColor = Color(0xFF673AB7);    // 深紫色
  static const Color refreshButtonHoverColor = Color(0xFF8E24AA);  // 悬停色
  static const Color refreshButtonIconColor = Color(0xFFFFFFFF);   // 图标白色
  
  /// 选中的菜单ID
  final String selectedMenuId;
  
  /// 菜单选择回调
  final Function(String) onMenuSelected;
  
  /// 刷新回调
  final VoidCallback onRefresh;
  
  /// 版本信息
  final String version;
  
  /// 是否使用收起模式（仅图标）
  final bool initialCollapsed;
  
  /// 构造函数
  const SidebarScreen({
    Key? key,
    required this.selectedMenuId,
    required this.onMenuSelected,
    required this.onRefresh,
    required this.version,
    this.initialCollapsed = false,
  }) : super(key: key);
  
  @override
  _SidebarScreenState createState() => _SidebarScreenState();
}

/// 侧边栏状态
class _SidebarScreenState extends State<SidebarScreen> {
  late bool isExpanded;
  
  @override
  void initState() {
    super.initState();
    _initializeSidebar();
  }
  
  /// 初始化侧边栏状态
  void _initializeSidebar() {
    // 根据初始设置确定显示模式
    isExpanded = !widget.initialCollapsed;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      _syncStateToModel();
    });
  }
  
  /// 同步状态到模型
  void _syncStateToModel() {
    final sidebarModel = Provider.of<SidebarModel>(context, listen: false);
    if (sidebarModel.expanded != isExpanded) {
      sidebarModel.setExpandedState(isExpanded);
    }
  }

  // 处理菜单项点击
  void _handleMenuItemClick(Map<String, dynamic> item) {
    final menuId = item['id'] as String;
    
    // 直接处理菜单选择，不需要复杂的延迟逻辑
    if (mounted) {
      _updateActiveItem(menuId);
    }
  }
  
  /// 更新活动菜单项
  void _updateActiveItem(String menuId) {
    // 确保在安全的时机更新状态
    if (!mounted) return;
    
    // 更新侧边栏模型状态
    Provider.of<SidebarModel>(context, listen: false).setActiveItem(menuId);
    
    // 触发菜单选择回调 - 这会通知MainLayout进行页面切换
    widget.onMenuSelected(menuId);
    
    // 通知侧边栏控制器页面切换
    final sideController = Provider.of<SideController>(context, listen: false);
    sideController.setActivePage(menuId, context);
    
    // 安全地触发UI刷新
    if (mounted) {
      setState(() {});
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer<SidebarModel>(
      builder: (context, sidebarModel, _) {
        // 检查状态同步，但不在构建期间调用setState
        final needsSync = sidebarModel.expanded != isExpanded;
        if (needsSync) {
          // 延迟到下一帧同步状态，避免在构建期间调用setState
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && sidebarModel.expanded != isExpanded) {
              setState(() {
                isExpanded = sidebarModel.expanded;
              });
            }
          });
        }
        
        return _buildSidebarContainer(sidebarModel);
      },
    );
  }
  
  /// 构建侧边栏容器
  Widget _buildSidebarContainer(SidebarModel sidebarModel) {
    final config = sidebarModel.sidebarConfig;
    final currentWidth = _calculateCurrentWidth(config);
    
    return Container(
      width: currentWidth,
      height: double.infinity,
      decoration: _buildContainerDecoration(),
      child: _buildSidebarContent(sidebarModel, config),
    );
  }
  
  /// 计算当前宽度
  double _calculateCurrentWidth(Map<String, dynamic> config) {
    final expandedWidth = config['width']['expanded'] as double;
    final collapsedWidth = config['width']['collapsed'] as double;
    
    return isExpanded 
        ? math.max(expandedWidth, SidebarScreen.minWidth)
        : math.max(collapsedWidth, SidebarScreen.minWidth);
  }
  
  /// 构建容器装饰
  BoxDecoration _buildContainerDecoration() {
    return BoxDecoration(
      color: SidebarScreen.backgroundColor,
      borderRadius: const BorderRadius.only(
        topRight: Radius.circular(SidebarScreen.borderRadius),
        bottomRight: Radius.circular(SidebarScreen.borderRadius),
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(SidebarScreen.shadowOpacity),
          blurRadius: SidebarScreen.shadowBlurRadius,
          offset: const Offset(2, 0),
          spreadRadius: 1,
        ),
      ],
    );
  }
  
  /// 构建侧边栏内容
  Widget _buildSidebarContent(SidebarModel sidebarModel, Map<String, dynamic> config) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildHeader(),
        _buildMenuList(sidebarModel, config),
        _buildBottomSection(config),
      ],
    );
  }
  
  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(SidebarScreen.paddingHorizontal),
      decoration: const BoxDecoration(
        color: SidebarScreen.primaryColor,
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(SidebarScreen.borderRadius),
        ),
      ),
      child: isExpanded 
          ? _buildExpandedHeader()
          : _buildCollapsedHeader(),
    );
  }
  
  /// 构建展开状态头部
  Widget _buildExpandedHeader() {
    return Row(
      children: [
        Icon(
          Icons.dashboard,
          color: Colors.white,
          size: 24,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(
            '控制面板',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
  
  /// 构建收起状态头部
  Widget _buildCollapsedHeader() {
    return const Center(
      child: Icon(
        Icons.dashboard,
        color: Colors.white,
        size: 28,
      ),
    );
  }
  
  /// 构建菜单列表
  Widget _buildMenuList(SidebarModel sidebarModel, Map<String, dynamic> config) {
    final menuItems = sidebarModel.menuItemsAsMap;
    
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: SidebarScreen.paddingSmall,
          horizontal: SidebarScreen.paddingSmall,
        ),
        child: ListView.separated(
          padding: EdgeInsets.zero,
          itemCount: menuItems.length,
          separatorBuilder: (context, index) => const SizedBox(height: SidebarScreen.itemSpacing),
          itemBuilder: (context, index) {
            final item = menuItems[index];
            final isSelected = item['id'] == widget.selectedMenuId;
            return _buildMenuItem(item, isSelected);
          },
        ),
      ),
    );
  }
  
  /// 构建菜单项
  Widget _buildMenuItem(Map<String, dynamic> item, bool isSelected) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _handleMenuItemClick(item),
        borderRadius: BorderRadius.circular(SidebarScreen.itemBorderRadius),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: _buildMenuItemDecoration(isSelected),
          padding: _getMenuItemPadding(),
          child: _buildMenuItemContent(item, isSelected),
        ),
      ),
    );
  }
  
  /// 构建菜单项装饰
  BoxDecoration _buildMenuItemDecoration(bool isSelected) {
    return BoxDecoration(
      color: isSelected ? SidebarScreen.selectedColor : Colors.transparent,
      borderRadius: BorderRadius.circular(SidebarScreen.itemBorderRadius),
      border: isSelected 
          ? Border.all(color: SidebarScreen.activeColor.withOpacity(0.3), width: 1)
          : null,
    );
  }
  
  /// 获取菜单项内边距
  EdgeInsets _getMenuItemPadding() {
    return isExpanded
        ? const EdgeInsets.symmetric(
            horizontal: 12.0,
            vertical: 10.0,
          )
        : const EdgeInsets.symmetric(
            vertical: SidebarScreen.paddingVertical,
            horizontal: SidebarScreen.paddingSmall,
          );
  }
  
  /// 构建菜单项内容
  Widget _buildMenuItemContent(Map<String, dynamic> item, bool isSelected) {
    return isExpanded 
        ? _buildExpandedMenuItem(item, isSelected)
        : _buildCollapsedMenuItem(item, isSelected);
  }
  
  /// 构建收起状态菜单项
  Widget _buildCollapsedMenuItem(Map<String, dynamic> item, bool isSelected) {
    return Center(
      child: Tooltip(
        message: item['title'],
        child: Icon(
          item['icon'],
          color: isSelected ? SidebarScreen.activeColor : SidebarScreen.inactiveColor,
          size: isSelected ? SidebarScreen.iconSizeSelected : SidebarScreen.iconSizeNormal,
        ),
      ),
    );
  }
  
  /// 构建展开状态菜单项
  Widget _buildExpandedMenuItem(Map<String, dynamic> item, bool isSelected) {
    return Row(
      children: [
        Icon(
          item['icon'],
          color: isSelected ? SidebarScreen.activeColor : SidebarScreen.inactiveColor,
          size: isSelected ? 20.0 : 18.0,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(
            item['title'],
            style: TextStyle(
              fontSize: isSelected ? 14.0 : 13.0,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              color: isSelected ? SidebarScreen.activeColor : SidebarScreen.inactiveColor,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
  
  /// 构建底部
  Widget _buildBottomSection(Map<String, dynamic> config) {
    return Column(
      children: [
        const Divider(
          color: SidebarScreen.dividerColor,
          height: 1,
          thickness: 1,
        ),
        _buildRefreshButton(),
        _buildVersionInfo(),
      ],
    );
  }
  
  /// 构建刷新按钮
  Widget _buildRefreshButton() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: isExpanded ? 6.0 : SidebarScreen.paddingSmall,
        vertical: SidebarScreen.paddingSmall,
      ),
      child: isExpanded 
          ? _buildExpandedRefreshButton()
          : _buildCollapsedRefreshButton(),
    );
  }
  
  /// 构建展开状态刷新按钮
  Widget _buildExpandedRefreshButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            SidebarScreen.refreshButtonStartColor,
            SidebarScreen.refreshButtonEndColor,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: SidebarScreen.refreshButtonStartColor.withOpacity(0.3),
            blurRadius: 8.0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _handleRefresh,
          borderRadius: BorderRadius.circular(8.0),
          child: Container(
            height: 36.0,
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.refresh,
                  color: SidebarScreen.refreshButtonIconColor,
                  size: 14,
                ),
                const SizedBox(width: 4.0),
                Flexible(
                  child: Text(
                    '刷新数据',
                    style: TextStyle(
                      color: SidebarScreen.refreshButtonIconColor,
                      fontSize: 12.0,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  /// 构建收起状态刷新按钮
  Widget _buildCollapsedRefreshButton() {
    return Center(
      child: Tooltip(
        message: '刷新数据',
        child: Container(
          width: 36.0,
          height: 36.0,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                SidebarScreen.refreshButtonStartColor,
                SidebarScreen.refreshButtonEndColor,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: SidebarScreen.refreshButtonStartColor.withOpacity(0.3),
                blurRadius: 6.0,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            shape: const CircleBorder(),
            child: InkWell(
              onTap: _handleRefresh,
              customBorder: const CircleBorder(),
              child: const Center(
                child: Icon(
                  Icons.refresh,
                  color: SidebarScreen.refreshButtonIconColor,
                  size: 20,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
    
  /// 处理刷新操作
  void _handleRefresh() {
    final sideController = Provider.of<SideController>(context, listen: false);
    
    // 执行全面的数据刷新（内部会显示持续的加载提示）
    sideController.refreshData(context);
    
    // 触发外部刷新回调
    widget.onRefresh();
  }
  
  /// 构建版本信息
  Widget _buildVersionInfo() {
    if (!isExpanded) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: SidebarScreen.paddingSmall,
        vertical: SidebarScreen.paddingSmall / 2,
      ),
      child: Text(
        'v${widget.version}',
        style: const TextStyle(
          fontSize: SidebarScreen.versionFontSize,
          color: SidebarScreen.inactiveColor,
          fontWeight: FontWeight.w300,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
