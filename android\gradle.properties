org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# 强制使用Java 17
org.gradle.java.installations.auto-download=false
kotlin.jvm.target.validation.mode=warning

# Network configuration to handle connection issues
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true

# Network timeout settings
systemProp.http.connectionTimeout=60000
systemProp.http.socketTimeout=60000
systemProp.https.connectionTimeout=60000
systemProp.https.socketTimeout=60000

# Retry settings
org.gradle.internal.http.connectionTimeout=60000
org.gradle.internal.http.socketTimeout=60000
