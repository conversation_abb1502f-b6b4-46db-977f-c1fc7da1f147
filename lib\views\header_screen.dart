// ignore_for_file: library_private_types_in_public_api, use_key_in_widget_constructors, prefer_const_constructors_in_immutables

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'dart:async';

import '../component/message_component.dart';
import '../PageComponents/header_left_component.dart';
import '../PageComponents/header_center_component.dart';
import '../PageComponents/header_right_component.dart';

import '../controllers/header_controller.dart';
import '../models/header_model.dart';
import '../services/server_service.dart';

/// 页头布局配置
class HeaderLayoutConfig {
  final double leftWidth;
  final double centerWidth;
  final double rightWidth;
  final double leftSpacing;
  final double rightSpacing;
  
  const HeaderLayoutConfig({
    required this.leftWidth,
    required this.centerWidth,
    required this.rightWidth,
    required this.leftSpacing,
    required this.rightSpacing,
  });
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HeaderLayoutConfig &&
          runtimeType == other.runtimeType &&
          leftWidth == other.leftWidth &&
          centerWidth == other.centerWidth &&
          rightWidth == other.rightWidth &&
          leftSpacing == other.leftSpacing &&
          rightSpacing == other.rightSpacing;

  @override
  int get hashCode =>
      leftWidth.hashCode ^
      centerWidth.hashCode ^
      rightWidth.hashCode ^
      leftSpacing.hashCode ^
      rightSpacing.hashCode;
}

/// 页头组件常量配置
class HeaderConstants {
  static const double statusRowHeight = 44.0; // 顶部状态信息行高度
  static const double mainRowHeight = 64.0;   // 主要控制行高度
  static const double totalHeight = statusRowHeight + mainRowHeight; // 总高度
  static const double horizontalPadding = 12.0;
  static const double sectionSpacing = 16.0;
  
  // 响应式断点
  static const int extraSmallBreakpoint = 360;
  static const int smallBreakpoint = 480;
  static const int mediumBreakpoint = 768;
  
  // 间距配置
  static const double minSectionSpacing = 8.0;
  static const double maxSectionSpacing = 24.0;
  
  // 现代化阴影效果
  static const Color shadowColor = Color.fromRGBO(0, 0, 0, 0.08);
  static const double shadowBlurRadius = 6.0;
  static const Offset shadowOffset = Offset(0, 3);
}

/// 页头组件
class HeaderScreen extends StatefulWidget {
  final VoidCallback onLogout;
  final VoidCallback onRefreshSystem;
  final VoidCallback? onRefreshData; // 新增数据刷新回调

  const HeaderScreen({
    super.key,
    required this.onLogout,
    required this.onRefreshSystem,
    this.onRefreshData, // 可选的数据刷新回调
  });

  @override
  State<HeaderScreen> createState() => _HeaderScreenState();
}

/// 头部页面组件状态
class _HeaderScreenState extends State<HeaderScreen> {
  Timer? _heartbeatTimer;
  final bool _showStatusInfo = true; // 状态信息始终显示
  HeaderLayoutConfig? _cachedLayoutConfig; // 缓存布局配置
  double? _lastScreenWidth; // 缓存屏幕宽度
  double? _lastAvailableWidth; // 缓存可用宽度

  @override
  void initState() {
    super.initState();
    _initializeHeartbeatMonitoring();
  }
  
  @override
  void dispose() {
    _heartbeatTimer?.cancel();
    super.dispose();
  }
  
  /// 初始化心跳监控
  void _initializeHeartbeatMonitoring() {
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (mounted) {
        final headerModel = Provider.of<HeaderModel>(context, listen: false);
        headerModel.checkHeartbeatHealth();
      }
    });
  }

  /// 处理刷新状态
  void _handleRefreshStatus() {
    final serverService = Provider.of<ServerService>(context, listen: false);
    
    if (!serverService.isConnected) {
      MessageComponent.showIconToast(
        context: context,
        message: '服务器未连接，无法刷新状态',
        type: MessageType.warning,
        duration: const Duration(seconds: 1),
      );
      return;
    }
    
    // 只发送状态栏查询请求
    serverService.sendMessage({
      'action': 'status_bar_query',
      'token': serverService.token,
    });
    
    MessageComponent.showIconToast(
      context: context,
      message: '正在刷新状态...',
      type: MessageType.info,
      duration: const Duration(seconds: 1),
    );
  }

  /// 处理连接/断开服务器切换
  void _handleReconnect() {
    final serverService = Provider.of<ServerService>(context, listen: false);
    final headerController = Provider.of<HeaderController>(context, listen: false);

    // 根据当前连接状态执行不同的操作
    if (serverService.isConnected) {
      // 当前已连接，执行断开操作
      MessageComponent.showIconToast(
        context: context,
        message: '正在断开服务器连接...',
        type: MessageType.warning,
        duration: const Duration(seconds: 1),
      );

      // 断开连接
      serverService.disconnect();

      // 延迟检查断开结果
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          MessageComponent.showIconToast(
            context: context,
            message: '已断开服务器连接',
            type: MessageType.info,
            duration: const Duration(seconds: 1),
          );
        }
      });

    } else {
      // 当前未连接，执行连接操作
      MessageComponent.showIconToast(
        context: context,
        message: '正在连接服务器...',
        type: MessageType.info,
        duration: const Duration(seconds: 1),
      );

      // 重新连接WebSocket
      headerController.handleRefreshSystem();

      // 延迟检查连接结果并触发数据刷新
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          if (serverService.isConnected) {
            // 连接成功，显示成功消息
            MessageComponent.showIconToast(
              context: context,
              message: '服务器连接成功',
              type: MessageType.success,
              duration: const Duration(seconds: 1),
            );

            // 延迟500ms后自动触发数据刷新
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted && widget.onRefreshData != null) {
                // 显示数据刷新提示
                MessageComponent.showIconToast(
                  context: context,
                  message: '正在刷新数据...',
                  type: MessageType.info,
                  duration: const Duration(seconds: 1),
                );

                // 调用数据刷新回调
                widget.onRefreshData!();

                // 延迟2秒后显示刷新完成
                Future.delayed(const Duration(seconds: 2), () {
                if (mounted) {
                  MessageComponent.showIconToast(
                    context: context,
                    message: '数据刷新完成',
                    type: MessageType.success,
                    duration: const Duration(seconds: 1),
                  );
                }
              });
            }
          });
          } else {
            MessageComponent.showIconToast(
              context: context,
              message: '服务器连接失败，请检查网络',
              type: MessageType.error,
              duration: const Duration(seconds: 2),
            );
          }
        }
      });
    }
  }

  /// 处理系统刷新
  void _handleRefresh() {
    final headerModel = Provider.of<HeaderModel>(context, listen: false);
    final headerController = Provider.of<HeaderController>(context, listen: false);
    
    if (!headerModel.canRefresh) {
      MessageComponent.showIconToast(
        context: context,
        message: '刷新过于频繁，请稍后再试',
        type: MessageType.warning,
        duration: const Duration(seconds: 1),
      );
      return;
    }
    
    headerModel.startRefresh();
    headerController.handleRefreshSystem();
    widget.onRefreshSystem();
    
    MessageComponent.showIconToast(
      context: context,
      message: '正在刷新系统...',
      type: MessageType.info,
      duration: const Duration(seconds: 1),
    );
    
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        headerModel.completeRefresh();
      }
    });
  }
  
  /// 处理退出登录
  void _handleLogout() {
    final headerController = Provider.of<HeaderController>(context, listen: false);
    headerController.handleLogout(context);
    widget.onLogout();
  }

  /// 处理版本更新
  void _handleVersionUpdate() {
    final serverService = Provider.of<ServerService>(context, listen: false);
    
    if (!serverService.isConnected) {
      MessageComponent.showIconToast(
        context: context,
        message: '服务器未连接，无法更新版本',
        type: MessageType.warning,
        duration: const Duration(seconds: 2),
      );
      return;
    }
    
    // 发送版本更新请求
    serverService.sendMessage({
      'action': 'version_update',
      'token': serverService.token,
    });
    
    MessageComponent.showIconToast(
      context: context,
      message: '版本更新请求已发送...',
      type: MessageType.success,
      duration: const Duration(seconds: 2),
    );
  }


  
  /// 智能计算主要控制行布局配置（带缓存优化）
  HeaderLayoutConfig _calculateSmartLayout(double availableWidth, double screenWidth) {
    // 检查是否可以使用缓存的配置
    if (_cachedLayoutConfig != null && 
        _lastScreenWidth == screenWidth && 
        _lastAvailableWidth == availableWidth) {
      return _cachedLayoutConfig!;
    }
    
    // 优化布局计算，针对不同设备类型进行差异化配置
    final safetyMargin = 2.0;
    final safeAvailableWidth = (availableWidth - safetyMargin).clamp(100.0, availableWidth);
    
    // 优化间距配置 - 根据设备类型调整
    double spacing;
    if (screenWidth < HeaderConstants.extraSmallBreakpoint) {
      spacing = 2.0; // 极小屏幕使用最小间距
    } else if (screenWidth < HeaderConstants.smallBreakpoint) {
      spacing = 3.0;
    } else if (screenWidth < HeaderConstants.mediumBreakpoint) {
      spacing = 4.0; // 移动端间距
    } else {
      spacing = 6.0; // 网页端使用更大间距，布局更舒适
    }
    
    // 针对不同设备类型优化宽度配置
    double leftWidth, rightWidth;
    
    if (screenWidth < HeaderConstants.mediumBreakpoint) {
      // 移动端：最小化左右两侧宽度，最大化中间空间
      leftWidth = 40.0;  // 移动端左侧宽度
      rightWidth = 70.0; // 移动端右侧宽度，增加以容纳版本更新按钮
    } else if (screenWidth < 1200) {
      // 小型网页端：适中配置，确保右侧有足够空间
      leftWidth = 80.0;  // 减少小型网页端左侧宽度
      rightWidth = 120.0; // 增加小型网页端右侧宽度，确保足够显示按钮
    } else {
      // 大型网页端：充足配置，但不过度
      leftWidth = 100.0; // 减少大型网页端左侧宽度
      rightWidth = 140.0; // 增加大型网页端右侧宽度，确保足够显示4个按钮
    }
    
    // 计算中间组件宽度 - 现在中间区域可以用于其他内容
    final totalSpacing = spacing * 2;
    final centerWidth = (safeAvailableWidth - leftWidth - rightWidth - totalSpacing)
        .clamp(100.0, double.infinity);
    
    // 创建新的布局配置
    final newConfig = HeaderLayoutConfig(
      leftWidth: leftWidth,
      centerWidth: centerWidth,
      rightWidth: rightWidth,
      leftSpacing: spacing,
      rightSpacing: spacing,
    );
    
    // 缓存配置和参数
    _cachedLayoutConfig = newConfig;
    _lastScreenWidth = screenWidth;
    _lastAvailableWidth = availableWidth;
    
    if (kDebugMode) {
      print('页头布局优化: 总宽度=${availableWidth.toInt()}px, '
            '左侧=${leftWidth.toInt()}px, 中间=${centerWidth.toInt()}px, '
            '右侧=${rightWidth.toInt()}px, 间距=${spacing.toInt()}px, '
            '设备类型=${_getDeviceType(screenWidth)}');
    }
    
    return newConfig;
  }
  
  /// 获取设备类型描述
  String _getDeviceType(double screenWidth) {
    if (screenWidth < HeaderConstants.extraSmallBreakpoint) {
      return '极小屏幕';
    } else if (screenWidth < HeaderConstants.smallBreakpoint) {
      return '小屏幕';
    } else if (screenWidth < HeaderConstants.mediumBreakpoint) {
      return '移动端';
    } else if (screenWidth < 1200) {
      return '小型网页端';
    } else {
      return '大型网页端';
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isExtraSmall = screenWidth < HeaderConstants.extraSmallBreakpoint;

    return Container(
      height: HeaderConstants.totalHeight,
      decoration: _buildDecoration(),
      child: Consumer<HeaderModel>(
        builder: (context, headerModel, child) {
          return Stack(
            children: [
              // 整体背景层
              Container(
                width: double.infinity,
                height: HeaderConstants.totalHeight,
                color: Colors.transparent,
              ),
              
              // 顶部状态信息行
              if (_showStatusInfo)
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  height: HeaderConstants.statusRowHeight,
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(
                      horizontal: isExtraSmall ? 8.0 : HeaderConstants.horizontalPadding,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.05),
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey.withValues(alpha: 0.2),
                          width: 1.0,
                        ),
                      ),
                    ),
                    child: HeaderCenterComponent(
                      showStatusInfo: _showStatusInfo,
                      onRefreshStatus: _handleRefreshStatus,
                    ),
                  ),
                ),
              
              // 主要控制行（层叠在状态行之上）
              Positioned(
                top: _showStatusInfo ? HeaderConstants.statusRowHeight + 2.0 : 2.0, // 留出2px间隙
                left: isExtraSmall ? 8.0 : HeaderConstants.horizontalPadding,
                right: isExtraSmall ? 8.0 : HeaderConstants.horizontalPadding,
                bottom: 4.0,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isExtraSmall ? 8.0 : HeaderConstants.horizontalPadding,
                    vertical: 6.0,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6.0),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.2),
                      width: 1.0,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.12), // 稍微增强阴影，确保层叠效果明显
                        blurRadius: 4.0,
                        offset: Offset(0, 2),
                        spreadRadius: 1.0,
                      ),
                    ],
                  ),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final availableWidth = constraints.maxWidth;
                      
                      // 在极小屏幕上使用特殊布局
                      if (isExtraSmall && availableWidth < 320) {
                        return _buildExtraSmallMainRow();
                      }
                      
                      // 智能计算各组件的宽度分配（带缓存）
                      final layoutConfig = _calculateSmartLayout(availableWidth, screenWidth);
                      
                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // 左侧区域：标题 + 游戏选择器
                          SizedBox(
                            width: layoutConfig.leftWidth,
                            child: const HeaderLeftComponent(),
                          ),
                          
                          // 左侧与中间的间隔
                          SizedBox(width: layoutConfig.leftSpacing),
                          
                          // 中间区域：现在可以用于其他内容或保持空白
                          SizedBox(
                            width: layoutConfig.centerWidth,
                            child: Container(), // 暂时保持空白
                          ),
                          
                          // 中间与右侧的间隔
                          SizedBox(width: layoutConfig.rightSpacing),
                      
                          // 右侧区域：状态控制按钮 + 用户操作按钮
                          SizedBox(
                            width: layoutConfig.rightWidth,
                            child: Align(
                              alignment: Alignment.centerRight,
                              child: HeaderRightComponent(
                                showStatusInfo: _showStatusInfo,
                                onRefresh: _handleRefresh,
                                onReconnect: _handleReconnect, // 添加重连回调
                                onLogout: _handleLogout,
                                onVersionUpdate: _handleVersionUpdate,
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
  
  /// 构建极小屏幕主要控制行布局
  /// 注意：此布局已经包含在主容器的装饰中，继承了矩形容器样式
  Widget _buildExtraSmallMainRow() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final safetyMargin = 6.0; // 安全边距
        final safeWidth = (availableWidth - safetyMargin).clamp(50.0, availableWidth);
        
        // 计算各组件的宽度分配
        final leftWidth = (safeWidth * 0.35).clamp(30.0, 80.0);
        final rightWidth = (safeWidth * 0.35).clamp(30.0, 70.0);
        final spacing = 6.0; // 增加最小间距，让布局更舒适
        final centerWidth = safeWidth - leftWidth - rightWidth - (spacing * 2);
        
        return Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 左侧：简化的左侧组件
            SizedBox(
              width: leftWidth,
              child: const HeaderLeftComponent(),
            ),
            
            SizedBox(width: spacing),
            
            // 中间：现在保持空白
            SizedBox(width: centerWidth),
            
            SizedBox(width: spacing),
            
            // 右侧：紧凑按钮
            SizedBox(
              width: rightWidth,
              child: Align(
                alignment: Alignment.centerRight,
                child: HeaderRightComponent(
                  showStatusInfo: _showStatusInfo,
                  onRefresh: _handleRefresh,
                  onReconnect: _handleReconnect, // 添加重连回调
                  onLogout: _handleLogout,
                  onVersionUpdate: _handleVersionUpdate,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
  
  /// 构建装饰样式
  BoxDecoration _buildDecoration() {
    return const BoxDecoration(
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: HeaderConstants.shadowColor,
          blurRadius: HeaderConstants.shadowBlurRadius,
          offset: HeaderConstants.shadowOffset,
        ),
      ],
    );
  }
}

