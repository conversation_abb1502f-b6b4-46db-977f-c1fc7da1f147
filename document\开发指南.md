# BlWeb 开发指南

本指南为BlWeb项目的新开发者提供快速上手指导，包含开发环境搭建、代码规范、开发流程等重要信息。

## 🚀 快速开始

### 环境要求
- **Flutter SDK**: 3.0+
- **Dart SDK**: 3.0+
- **IDE**: VS Code / Android Studio
- **操作系统**: Windows / macOS / Linux

### 项目克隆与设置
```bash
# 克隆项目
git clone <repository-url>
cd BlWeb

# 安装依赖
flutter pub get

# 运行项目
flutter run
```

### 依赖包说明
```yaml
# 核心依赖
provider: ^6.0.0          # 状态管理
web_socket_channel: ^2.0.0 # WebSocket通信
shared_preferences: ^2.0.0 # 本地存储
getwidget: ^3.0.0         # UI组件库

# 开发依赖
flutter_test: ^1.0.0      # 测试框架
```

## 🏗️ 项目架构

### MVC架构模式
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Model       │    │   Controller    │    │      View       │
│   (数据层)       │◄──►│   (控制层)       │◄──►│    (视图层)      │
│                 │    │                 │    │                 │
│ • 数据存储      │    │ • 业务逻辑      │    │ • UI渲染        │
│ • 状态管理      │    │ • 用户交互      │    │ • 用户交互      │
│ • 数据验证      │    │ • 数据流控制    │    │ • 状态监听      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 目录结构规范
```
lib/
├── models/           # 数据模型 - 继承ChangeNotifier
├── controllers/      # 控制器 - 处理业务逻辑
├── views/           # 视图页面 - UI界面
├── component/       # 通用组件 - 可复用UI组件
├── PageComponents/  # 页面组件 - 特定页面组件
├── services/        # 服务层 - 外部接口
├── utils/          # 工具类 - 通用工具
└── main.dart       # 应用入口
```

## 📝 代码规范

### 命名规范
```dart
// 文件命名：小写+下划线
login_controller.dart
user_model.dart

// 类命名：大驼峰
class LoginController extends ChangeNotifier {}
class UserModel {}

// 变量命名：小驼峰
String userName;
bool isLoggedIn;

// 常量命名：大写+下划线
static const String API_BASE_URL = 'ws://localhost:8080';

// 私有变量：下划线开头
String _privateVariable;
```

### 代码组织
```dart
class ExampleController extends ChangeNotifier {
  // 1. 常量定义
  static const String TAG = 'ExampleController';
  
  // 2. 私有变量
  String _data = '';
  bool _isLoading = false;
  
  // 3. 公共getter
  String get data => _data;
  bool get isLoading => _isLoading;
  
  // 4. 公共setter
  set data(String value) {
    if (_data != value) {
      _data = value;
      notifyListeners();
    }
  }
  
  // 5. 构造函数
  ExampleController();
  
  // 6. 公共方法
  Future<void> loadData() async {
    // 实现逻辑
  }
  
  // 7. 私有方法
  void _updateState() {
    // 私有逻辑
  }
}
```

### 注释规范
```dart
/// 用户登录控制器
/// 
/// 负责处理用户登录相关的业务逻辑，包括：
/// - 用户凭据验证
/// - 登录状态管理
/// - 错误处理
class LoginController extends ChangeNotifier {
  
  /// 执行用户登录
  /// 
  /// [username] 用户名
  /// [password] 密码
  /// 
  /// 返回登录是否成功
  Future<bool> login(String username, String password) async {
    // 实现逻辑
  }
}
```

## 🔄 开发流程

### 新功能开发流程
1. **需求分析** → 理解功能需求和用户场景
2. **设计阶段** → 设计数据模型和界面布局
3. **Model开发** → 创建数据模型，定义状态和方法
4. **Controller开发** → 实现业务逻辑和数据处理
5. **View开发** → 创建UI界面，绑定数据和事件
6. **测试验证** → 功能测试和用户体验验证
7. **文档更新** → 更新相关技术文档

### 代码提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 类型说明
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例
feat(login): 添加记住密码功能
fix(slider): 修复移动端滑块溢出问题
docs(api): 更新登录API文档
```

## 🧩 组件开发

### 创建新组件
```dart
// 1. 创建组件文件：lib/component/example_component.dart
import 'package:flutter/material.dart';

class ExampleComponent extends StatelessWidget {
  final String title;
  final VoidCallback? onTap;
  
  const ExampleComponent({
    Key? key,
    required this.title,
    this.onTap,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16.0),
        child: Text(title),
      ),
    );
  }
}
```

### 组件使用规范
```dart
// 在页面中使用组件
class ExampleScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ExampleComponent(
        title: '示例标题',
        onTap: () {
          // 处理点击事件
        },
      ),
    );
  }
}
```

## 🔌 状态管理

### Provider使用规范
```dart
// 1. 在main.dart中注册Provider
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => ExampleModel()),
    ChangeNotifierProvider(create: (_) => ExampleController()),
  ],
  child: MyApp(),
)

// 2. 在Widget中使用Consumer
Consumer<ExampleModel>(
  builder: (context, model, child) {
    return Text(model.data);
  },
)

// 3. 在Controller中访问Model
class ExampleController extends ChangeNotifier {
  late ExampleModel _model;
  
  void initialize(ExampleModel model) {
    _model = model;
  }
  
  void updateData(String newData) {
    _model.data = newData;
  }
}
```

## 🌐 API集成

### WebSocket通信规范
```dart
// 1. 发送请求
Map<String, dynamic> request = {
  'action': 'example_action',
  'content': {
    'param1': 'value1',
    'param2': 'value2',
  },
};
serverService.sendMessage(request);

// 2. 处理响应
void handleResponse(Map<String, dynamic> response) {
  if (response['action'] == 'example_response') {
    final data = response['data'];
    // 处理响应数据
  }
}
```

### 错误处理规范
```dart
try {
  await performOperation();
} catch (e) {
  Logger.e('ExampleController', '操作失败', e.toString());
  // 显示用户友好的错误信息
  _showErrorMessage('操作失败，请重试');
}
```

## 📱 响应式设计

### 屏幕适配
```dart
// 使用LayoutBuilder进行响应式设计
LayoutBuilder(
  builder: (context, constraints) {
    final screenWidth = constraints.maxWidth;
    
    if (screenWidth < 600) {
      // 移动端布局
      return MobileLayout();
    } else {
      // 桌面端布局
      return DesktopLayout();
    }
  },
)
```

### 移动端优化
```dart
// 移动端特定优化
class ResponsiveWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    
    return Container(
      padding: EdgeInsets.all(isMobile ? 8.0 : 16.0),
      child: Text(
        '响应式文本',
        style: TextStyle(
          fontSize: isMobile ? 14.0 : 16.0,
        ),
      ),
    );
  }
}
```

## 🧪 测试指南

### 单元测试
```dart
// test/example_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:blweb/models/example_model.dart';

void main() {
  group('ExampleModel Tests', () {
    test('should update data correctly', () {
      final model = ExampleModel();
      model.data = 'test data';
      expect(model.data, equals('test data'));
    });
  });
}
```

### Widget测试
```dart
testWidgets('ExampleComponent should display title', (WidgetTester tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: ExampleComponent(title: 'Test Title'),
    ),
  );
  
  expect(find.text('Test Title'), findsOneWidget);
});
```

## 🔧 调试技巧

### 日志记录
```dart
// 使用项目的Logger工具
Logger.d('TAG', '调试信息');
Logger.i('TAG', '普通信息');
Logger.w('TAG', '警告信息');
Logger.e('TAG', '错误信息', '详细错误描述');
```

### 性能调试
```dart
// 使用Flutter Inspector
// 在VS Code中按 Ctrl+Shift+P，输入 "Flutter: Open Widget Inspector"

// 性能分析
// flutter run --profile
// 然后在DevTools中查看性能数据
```

## 📚 学习资源

### 官方文档
- [Flutter官方文档](https://flutter.dev/docs)
- [Dart语言指南](https://dart.dev/guides)
- [Provider状态管理](https://pub.dev/packages/provider)

### 项目相关
- 查看 `document/前端项目结构.md` 了解项目架构
- 查看 `api/` 目录了解接口规范
- 查看现有代码学习项目规范

---

> **开发指南说明**: 本指南为BlWeb项目开发提供标准化流程和规范。
> **最后更新**: 2025年1月 | **维护者**: Flutter开发团队
