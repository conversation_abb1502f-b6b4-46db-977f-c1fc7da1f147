import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// 射击设置模型 - 管理射击相关的数据
class FireModel extends ChangeNotifier {
  // 射击参数 - 根据数据库结构
  int _rifleSleep = 100;       // 步枪休眠
  int _rifleInterval = 200;    // 步枪间隔
  int _sniperSleep = 300;      // 狙击休眠
  int _sniperInterval = 500;   // 狙击间隔
  int _weaponSwitchSleep = 50; // 切枪睡眠间隔

  // 元数据
  String _username = 'admin';
  String _gameName = 'csgo2';
  String _createdAt = '';
  String _updatedAt = '';

  // 默认值 - 用于重置
  final Map<String, dynamic> _defaults = {
    'rifleSleep': 100,
    'rifleInterval': 200,
    'sniperSleep': 300,
    'sniperInterval': 500,
    'weaponSwitchSleep': 50,
  };
  
  // Getters
  int get rifleSleep => _rifleSleep;
  int get rifleInterval => _rifleInterval;
  int get sniperSleep => _sniperSleep;
  int get sniperInterval => _sniperInterval;
  int get weaponSwitchSleep => _weaponSwitchSleep;
  String get username => _username;
  String get gameName => _gameName;
  String get createdAt => _createdAt;
  String get updatedAt => _updatedAt;
  
  // Setters
  set rifleSleep(int value) {
    if (_rifleSleep != value) {
      _rifleSleep = value;
      _updateTimestamp();
      // 延迟通知监听器，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
  
  set rifleInterval(int value) {
    if (_rifleInterval != value) {
      _rifleInterval = value;
      _updateTimestamp();
      // 延迟通知监听器，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
  

  
  set sniperSleep(int value) {
    if (_sniperSleep != value) {
      _sniperSleep = value;
      _updateTimestamp();
      // 延迟通知监听器，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
  
  set sniperInterval(int value) {
    if (_sniperInterval != value) {
      _sniperInterval = value;
      _updateTimestamp();
      // 延迟通知监听器，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  set weaponSwitchSleep(int value) {
    if (_weaponSwitchSleep != value) {
      _weaponSwitchSleep = value;
      _updateTimestamp();
      // 延迟通知监听器，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
  
  set username(String value) {
    if (_username != value) {
      _username = value;
      // 延迟通知监听器，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
  
  set gameName(String value) {
    if (_gameName != value) {
      _gameName = value;
      // 延迟通知监听器，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
  
  /// 构造函数
  FireModel() {
    // 移除直接调用loadSettings()，改为延迟初始化
    _initializeAsync();
  }
  
  /// 异步初始化
  void _initializeAsync() {
    // 使用microtask延迟执行，避免在构造函数中直接调用notifyListeners
    Future.microtask(() async {
      await loadSettings();
    });
  }
  
  /// 更新时间戳
  void _updateTimestamp() {
    _updatedAt = DateTime.now().toIso8601String();
  }
  
  /// 重置为默认值
  void resetToDefaults() {
    _rifleSleep = _defaults['rifleSleep'];
    _rifleInterval = _defaults['rifleInterval'];
    _sniperSleep = _defaults['sniperSleep'];
    _sniperInterval = _defaults['sniperInterval'];
    _weaponSwitchSleep = _defaults['weaponSwitchSleep'];
    _updateTimestamp();
    notifyListeners();
  }
  
  /// 从配置中加载设置
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 加载射击参数
    _rifleSleep = prefs.getInt('fire_rifleSleep') ?? _defaults['rifleSleep'];
    _rifleInterval = prefs.getInt('fire_rifleInterval') ?? _defaults['rifleInterval'];
    _sniperSleep = prefs.getInt('fire_sniperSleep') ?? _defaults['sniperSleep'];
    _sniperInterval = prefs.getInt('fire_sniperInterval') ?? _defaults['sniperInterval'];
    _weaponSwitchSleep = prefs.getInt('fire_weaponSwitchSleep') ?? _defaults['weaponSwitchSleep'];
    
    // 加载元数据
    _username = prefs.getString('fire_username') ?? _username;
    _gameName = prefs.getString('fire_gameName') ?? _gameName;
    _createdAt = prefs.getString('fire_createdAt') ?? '';
    _updatedAt = prefs.getString('fire_updatedAt') ?? '';
    
    // 如果是首次加载，创建时间戳
    if (_createdAt.isEmpty) {
      _createdAt = DateTime.now().toIso8601String();
      _updatedAt = _createdAt;
    }
    
    // 延迟通知监听器，避免在构建期间调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
  
  /// 保存设置到持久化存储
  Future<void> saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 保存射击参数
    await prefs.setInt('fire_rifleSleep', _rifleSleep);
    await prefs.setInt('fire_rifleInterval', _rifleInterval);
    await prefs.setInt('fire_sniperSleep', _sniperSleep);
    await prefs.setInt('fire_sniperInterval', _sniperInterval);
    await prefs.setInt('fire_weaponSwitchSleep', _weaponSwitchSleep);
    
    // 保存元数据
    await prefs.setString('fire_username', _username);
    await prefs.setString('fire_gameName', _gameName);
    await prefs.setString('fire_createdAt', _createdAt);
    await prefs.setString('fire_updatedAt', _updatedAt);
  }
  
  /// 更新用户和游戏信息
  void updateUserGameInfo(String username, String gameName) {
    _username = username;
    _gameName = gameName;
    _updatedAt = DateTime.now().toIso8601String();
    // 延迟通知监听器，避免在构建期间调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
  
  /// 从JSON获取配置
  void fromJson(Map<String, dynamic> json) {
    if (json['content'] != null) {
      final content = json['content'];
      
      // 使用安全解析方法设置参数
      if (content['rifleSleep'] != null) {
        _rifleSleep = _parseIntValue(content['rifleSleep']);
      }
      
      if (content['rifleInterval'] != null) {
        _rifleInterval = _parseIntValue(content['rifleInterval']);
      }
      

      
      if (content['sniperSleep'] != null) {
        _sniperSleep = _parseIntValue(content['sniperSleep']);
      }
      
      if (content['sniperInterval'] != null) {
        _sniperInterval = _parseIntValue(content['sniperInterval']);
      }

      if (content['weaponSwitchSleep'] != null) {
        _weaponSwitchSleep = _parseIntValue(content['weaponSwitchSleep']);
      }
      
      // 设置元数据
      if (content['username'] != null) _username = content['username'].toString();
      if (content['gameName'] != null) _gameName = content['gameName'].toString();
      if (content['createdAt'] != null) _createdAt = content['createdAt'].toString();
      if (content['updatedAt'] != null) _updatedAt = content['updatedAt'].toString();
    }
    
    // 延迟通知监听器，避免在构建期间调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
  
  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'action': 'fire_modify',
      'content': {
        'username': _username,
        'gameName': _gameName,
        'rifleSleep': _rifleSleep,
        'rifleInterval': _rifleInterval,
        'sniperSleep': _sniperSleep,
        'sniperInterval': _sniperInterval,
        'weaponSwitchSleep': _weaponSwitchSleep,
        'createdAt': _createdAt,
        'updatedAt': _updatedAt,
      }
    };
  }
  
  /// 获取JSON字符串
  String toJsonString() {
    return jsonEncode(toJson());
  }
  
  /// 安全解析int值，处理各种可能的输入类型
  int _parseIntValue(dynamic value) {
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        try {
          return double.parse(value).toInt();
        } catch (e) {
          return 0;
        }
      }
    }
    return 0;
  }
} 