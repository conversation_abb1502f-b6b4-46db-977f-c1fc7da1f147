// ignore_for_file: use_build_context_synchronously, unused_import

import 'package:flutter/material.dart';
import '../models/fire_model.dart';
import '../models/auth_model.dart';
import '../models/game_model.dart';
import '../services/server_service.dart';
import 'package:provider/provider.dart';
import '../utils/logger.dart';
import 'dart:convert';

/// 射击控制器，负责管理射击参数和相关的业务逻辑
class FireController extends ChangeNotifier {
  // 依赖引用
  final ServerService serverService;
  final AuthModel authModel;
  final GameModel gameModel;
  final FireModel _fireModel;
  
  // 日志记录器
  final _logger = Logger();
  static const String _tag = 'FireController';
  
  // 构造函数
  FireController({
    required this.serverService,
    required this.authModel,
    required this.gameModel,
    required FireModel fireModel,
  }) : _fireModel = fireModel {
    print('🔥 FireController构造函数被调用！'); // 强制输出，确保能看到
    _logger.i(_tag, 'FireController初始化开始', {
      'authModel': authModel.hashCode,
      'gameModel': gameModel.hashCode,
      'fireModel': fireModel.hashCode,
      'serverService': serverService.hashCode,
    });

    // 添加WebSocket消息监听器
    _setupWebSocketListener();

    // 初始化时尝试加载最新数据
    _init();

    _logger.i(_tag, 'FireController初始化完成');
  }
  
  /// 初始化
  Future<void> _init() async {
    // 如果已登录且有游戏选择，则尝试获取数据
    if (authModel.isAuthenticated && gameModel.currentGame.isNotEmpty) {
      await requestFireParams();
    }
  }

  /// 设置WebSocket消息监听
  void _setupWebSocketListener() {
    final listener = serverService.addMessageListener(_handleWebSocketMessage);
    _logger.i(_tag, 'WebSocket消息监听器已设置', {
      'listener': listener.hashCode,
      'serverConnected': serverService.isConnected,
    });
  }
  
  /// 处理接收到的WebSocket消息
  void _handleWebSocketMessage(dynamic message) {
    print('🔥 FireController收到消息: ${message.toString().substring(0, 100)}...'); // 强制输出

    if (message is! String) {
      _logger.d(_tag, '收到非字符串消息，类型: ${message.runtimeType}');
      return;
    }

    try {
      final Map<String, dynamic> data = json.decode(message);
      final action = data['action'] ?? '';
      final status = data['status'] ?? '';

      print('🔥 FireController解析消息: action=$action, status=$status'); // 强制输出

      _logger.i(_tag, '处理WebSocket消息', {
        'action': action,
        'status': status,
        'hasData': data.containsKey('data'),
      });

      // 检查是否是射击参数响应
      if (action == 'fire_read_response') {
        if (status == 'success') {
          _logger.i(_tag, '接收到射击参数读取响应');
          if (data['data'] != null) {
            _logger.i(_tag, '更新射击参数数据', {'data': data['data']});
            _fireModel.fromJson({'content': data['data']});
            notifyListeners();
          } else {
            _logger.w(_tag, '射击参数读取响应中没有数据');
          }
        } else {
          final errorMsg = data['message'] ?? '未知错误';
          _logger.w(_tag, '射击参数读取失败', {'error': errorMsg});
        }
      } else if (action == 'fire_modify_response') {
        if (status == 'success') {
          _logger.i(_tag, '射击参数修改成功');
          if (data['data'] != null) {
            _logger.i(_tag, '更新射击参数数据', {'data': data['data']});
            _fireModel.fromJson({'content': data['data']});
            notifyListeners();
          }
        } else {
          final errorMsg = data['message'] ?? '未知错误';
          _logger.w(_tag, '射击参数修改失败', {'error': errorMsg});
        }
      } else if (action.isNotEmpty) {
        // 记录其他action消息，但不处理
        _logger.i(_tag, '收到其他action消息', {'action': action});
      }
    } catch (e) {
      _logger.e(_tag, '处理WebSocket消息失败', {
        'error': e.toString(),
        'message': message.length > 200 ? '${message.substring(0, 200)}...' : message,
      });
    }
  }

  // 射击参数 getters
  int get rifleSleep => _fireModel.rifleSleep;
  int get rifleInterval => _fireModel.rifleInterval;
  int get sniperSleep => _fireModel.sniperSleep;
  int get sniperInterval => _fireModel.sniperInterval;
  int get weaponSwitchSleep => _fireModel.weaponSwitchSleep;
  
  // 射击参数 setters
  set rifleSleep(int value) {
    _fireModel.rifleSleep = value;
    notifyListeners();
  }
  
  set rifleInterval(int value) {
    _fireModel.rifleInterval = value;
    notifyListeners();
  }
  

  
  set sniperSleep(int value) {
    _fireModel.sniperSleep = value;
    notifyListeners();
  }
  
  set sniperInterval(int value) {
    _fireModel.sniperInterval = value;
    notifyListeners();
  }

  set weaponSwitchSleep(int value) {
    _fireModel.weaponSwitchSleep = value;
    notifyListeners();
  }
  
  /// 请求射击参数 - 从服务器获取最新配置
  Future<void> requestFireParams() async {
    try {
      _logger.i(_tag, '开始请求射击参数', {
        'username': authModel.username,
        'gameName': gameModel.currentGame,
        'serverConnected': serverService.isConnected,
      });

      // 更新用户和游戏信息
      _fireModel.username = authModel.username;
      _fireModel.gameName = gameModel.currentGame;

      // 先尝试从本地加载设置
      await _fireModel.loadSettings();
      _logger.i(_tag, '本地设置已加载');

      // 如果服务器已连接，则通过WebSocket请求服务器数据
      if (serverService.isConnected) {
        // 构造符合API文档的fire_read请求
        final Map<String, dynamic> requestData = {
          'action': 'fire_read',
          'content': {
            'username': authModel.username,
            'token': authModel.token,
            'gameName': gameModel.currentGame
          }
        };

        _logger.i(_tag, '发送fire_read请求', requestData);

        // 发送WebSocket请求
        serverService.sendMessage(json.encode(requestData));
        _logger.i(_tag, '已发送射击参数获取请求');
      } else {
        _logger.w(_tag, 'WebSocket未连接，使用本地射击参数');
      }

      notifyListeners();
    } catch (e) {
      _logger.e(_tag, '获取射击参数失败', e);
    }
  }
  
  /// 重置为默认值
  void resetToDefaults(BuildContext context) {
    try {
      _fireModel.resetToDefaults();
      notifyListeners();
      
      // 显示成功消息
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('已重置为默认设置'),
          backgroundColor: Colors.green,
        ),
      );
      
      _logger.i(_tag, '已重置射击参数为默认值');
    } catch (e) {
      _logger.e(_tag, '重置射击参数失败', e);
      
      // 显示错误消息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('重置设置失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  /// 保存射击配置
  Future<void> saveFireConfig(BuildContext context) async {
    try {
      // 更新用户和游戏信息
      _fireModel.username = authModel.username;
      _fireModel.gameName = gameModel.currentGame;
      
      // 保存到本地存储
      await _fireModel.saveSettings();
      
      // 如果服务器已连接，发送fire_modify请求
      if (serverService.isConnected) {
        // 构造符合API文档的fire_modify请求
        final Map<String, dynamic> requestContent = {
          'action': 'fire_modify',
          'content': {
            'username': authModel.username,
            'gameName': gameModel.currentGame,
            'rifleSleep': rifleSleep,
            'rifleInterval': rifleInterval,
            'sniperSleep': sniperSleep,
            'sniperInterval': sniperInterval,
            'weaponSwitchSleep': weaponSwitchSleep,
            'updatedAt': DateTime.now().toIso8601String(),
          }
        };
        
        // 发送请求
        serverService.sendMessage(json.encode(requestContent));
        _logger.i(_tag, '已发送射击配置修改请求');
      } else {
        _logger.w(_tag, 'WebSocket未连接，仅保存到本地存储');
      }
      
      // 显示成功消息
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('设置已保存'),
          backgroundColor: Colors.green,
        ),
      );
      
      _logger.i(_tag, '已保存射击配置');
    } catch (e) {
      _logger.e(_tag, '保存射击配置失败', e);
      
      // 显示错误消息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('保存配置失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  /// 处理参数变更
  void handleParameterChanged(String paramName, dynamic value, BuildContext context) {
    try {
      // 记录参数变更
      _logger.d(_tag, '射击参数已变更: $paramName = $value');
      
      // 在滑块调整结束时发送WebSocket请求更新参数
      if (serverService.isConnected) {
        // 构造符合API文档的fire_modify请求
        final Map<String, dynamic> requestContent = {
          'action': 'fire_modify',
          'content': {
            'username': authModel.username,
            'gameName': gameModel.currentGame,
            'rifleSleep': rifleSleep,
            'rifleInterval': rifleInterval,
            'sniperSleep': sniperSleep,
            'sniperInterval': sniperInterval,
            'weaponSwitchSleep': weaponSwitchSleep,
            'updatedAt': DateTime.now().toIso8601String(),
          }
        };
        
        // 发送请求
        serverService.sendMessage(json.encode(requestContent));
        _logger.d(_tag, '参数变更后发送射击配置修改请求');
      } else {
        _logger.w(_tag, 'WebSocket未连接，参数变更未发送到服务器');
      }
    } catch (e) {
      _logger.e(_tag, '处理参数变更失败', e);
    }
  }
  


  @override
  void dispose() {
    _logger.i(_tag, 'FireController正在销毁，移除WebSocket监听器');
    // 清理WebSocket监听器，防止内存泄漏
    serverService.removeMessageListener(_handleWebSocketMessage);
    super.dispose();
  }
} 