import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/config_management_controller.dart';
import '../models/config_management_model.dart';

import '../component/Button_component.dart';
import '../utils/web_environment_config.dart';

/// 配置管理页面 - 提供配置的导入导出功能
class ConfigManagementScreen extends StatefulWidget {
  const ConfigManagementScreen({Key? key}) : super(key: key);

  @override
  State<ConfigManagementScreen> createState() => _ConfigManagementScreenState();
}

class _ConfigManagementScreenState extends State<ConfigManagementScreen> {
  String _selectedConfigType = 'all';
  
  /// 构建环境信息显示区域
  Widget _buildEnvironmentInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.blue),
              SizedBox(width: 6),
              Text(
                '环境信息',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildInfoRow('运行环境', WebEnvironmentConfig.environmentDescription),
          _buildInfoRow('剪切板支持', WebEnvironmentConfig.clipboardSupportDescription),
          _buildInfoRow('推荐操作', WebEnvironmentConfig.recommendedAction),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Consumer<ConfigManagementController>(
      builder: (context, controller, child) {
        return Scaffold(
          backgroundColor: Colors.grey[50],
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPageHeader(),
                const SizedBox(height: 20),
                _buildConfigTypeSelector(),
                const SizedBox(height: 20),
                _buildExportSection(controller),
                const SizedBox(height: 20),
                _buildImportSection(controller),
                const SizedBox(height: 20),
                _buildHistorySection(controller),
              ],
            ),
          ),
        );
      },
    );
  }
  
  /// 构建页面标题
  Widget _buildPageHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.import_export,
              size: 32,
              color: Colors.blue[600],
            ),
            const SizedBox(width: 12),
            Text(
              '配置管理',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          '导入和导出您的应用配置，方便备份和分享设置',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
  
  /// 构建配置类型选择器
  Widget _buildConfigTypeSelector() {
    return _buildCard(
      title: '选择配置类型',
      child: Column(
        children: [
          const Text(
            '请选择要导入或导出的配置类型：',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 8,
            children: ConfigManagementModel.configTypes.entries.map((entry) {
              final isSelected = _selectedConfigType == entry.key;
              return FilterChip(
                label: Text(entry.value),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedConfigType = entry.key;
                    });
                  }
                },
                selectedColor: Colors.blue[100],
                checkmarkColor: Colors.blue[700],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
  
  /// 构建导出区域
  Widget _buildExportSection(ConfigManagementController controller) {
    return _buildCard(
      title: '导出配置',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '将当前配置导出为JSON文件，可用于备份或分享给其他用户。',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 16),

          // 文件名输入框
          _buildFileNameInput(controller),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: Button.create(ButtonConfig.textWithIcon(
                  '导出到文件',
                  Icons.file_download,
                  onPressed: controller.isExporting
                      ? null
                      : () => controller.exportConfig(context, _selectedConfigType),
                  type: ButtonType.primary,
                )),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Button.create(ButtonConfig.textWithIcon(
                  '复制到剪切板',
                  Icons.content_copy,
                  onPressed: controller.isExporting
                      ? null
                      : () => controller.exportToClipboard(context, _selectedConfigType),
                  type: ButtonType.secondary,
                )),
              ),
            ],
          ),
          if (controller.isExporting) ...[
            const SizedBox(height: 12),
            const Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 8),
                Text('正在导出配置...', style: TextStyle(color: Colors.blue)),
              ],
            ),
          ],
          if (controller.lastExportPath.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '最近导出成功',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          controller.lastExportPath,
                          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  /// 构建导入区域
  Widget _buildImportSection(ConfigManagementController controller) {
    return _buildCard(
      title: '导入配置',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '从JSON文件或剪切板导入配置，这将覆盖当前的相关设置。',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.warning, color: Colors.orange[600], size: 20),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '注意：导入配置将覆盖当前设置，建议先导出当前配置作为备份。',
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Button.create(ButtonConfig.textWithIcon(
                  '从文件导入',
                  Icons.file_upload,
                  onPressed: controller.isImporting
                      ? null
                      : () => controller.importConfig(context),
                  type: ButtonType.secondary,
                )),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Button.create(ButtonConfig.textWithIcon(
                  '从剪切板导入',
                  Icons.content_paste,
                  onPressed: controller.isImporting
                      ? null
                      : () => controller.importFromClipboard(context),
                  type: ButtonType.info,
                )),
              ),
            ],
          ),
          if (controller.isImporting) ...[
            const SizedBox(height: 12),
            const Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 8),
                Text('正在导入配置...', style: TextStyle(color: Colors.blue)),
              ],
            ),
          ],

          // 环境信息显示
          const SizedBox(height: 16),
          _buildEnvironmentInfo(),


          if (controller.lastImportPath.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '最近导入成功',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          controller.lastImportPath,
                          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  /// 构建历史记录区域
  Widget _buildHistorySection(ConfigManagementController controller) {
    return _buildCard(
      title: '操作历史',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '清除导入导出的历史记录。',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Button.create(ButtonConfig.textWithIcon(
                  '清除历史',
                  Icons.clear_all,
                  onPressed: () => controller.clearHistory(),
                  type: ButtonType.danger,
                  size: ButtonSize.small,
                )),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建卡片容器
  Widget _buildCard({
    required String title,
    required Widget child,
  }) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  /// 构建文件名输入框
  Widget _buildFileNameInput(ConfigManagementController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '文件名',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(width: 8),
            Tooltip(
              message: '留空将使用默认文件名（包含时间戳）',
              child: Icon(
                Icons.help_outline,
                size: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
            color: Colors.white,
          ),
          child: Consumer<ConfigManagementController>(
            builder: (context, ctrl, child) {
              return TextField(
                controller: ctrl.fileNameController,
                onChanged: (value) {
                  // 触发重建以更新清空按钮的显示
                  ctrl.onFileNameChanged(value);
                },
                decoration: InputDecoration(
                  hintText: '输入自定义文件名（可选）',
                  hintStyle: TextStyle(color: Colors.grey[500]),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                  suffixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 清空按钮
                      if (ctrl.fileNameController.text.isNotEmpty)
                        IconButton(
                          icon: Icon(Icons.clear, size: 18, color: Colors.grey[600]),
                          onPressed: () => ctrl.clearFileName(),
                          tooltip: '清空',
                        ),
                      // 生成默认文件名按钮
                      IconButton(
                        icon: Icon(Icons.auto_awesome, size: 18, color: Colors.blue[600]),
                        onPressed: () {
                          final defaultName = ctrl.generateDefaultFileName(_selectedConfigType);
                          ctrl.setFileName(defaultName);
                        },
                        tooltip: '生成默认文件名',
                      ),
                    ],
                  ),
                ),
                style: const TextStyle(fontSize: 14),
                maxLines: 1,
              );
            },
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '提示：文件将自动添加 .json 扩展名',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
