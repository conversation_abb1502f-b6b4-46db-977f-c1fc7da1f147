// ignore_for_file: use_super_parameters, library_private_types_in_public_api, invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member, unused_import, avoid_print, deprecated_member_use, unnecessary_string_escapes

import 'package:flutter/material.dart';
import '../controllers/data_collection_controller.dart';
import 'package:provider/provider.dart';
import '../component/Button_component.dart';
import '../component/input_component.dart';
import '../component/message_component.dart';
import '../component/status_display_component.dart';
import '../component/resource_label_component.dart';
import 'package:getwidget/getwidget.dart';
import 'package:intl/intl.dart';

/// 数据收集设置屏幕
class DataCollectionScreen extends StatefulWidget {
  const DataCollectionScreen({super.key});

  @override
  State<DataCollectionScreen> createState() => _DataCollectionScreenState();
}

class _DataCollectionScreenState extends State<DataCollectionScreen> {
  // 常量定义
  static const double _cardElevation = 2.0;
  static const double _borderRadius = 8.0;
  static const double _estimatedCardHeight = 280.0;
  static const EdgeInsets _cardPadding = EdgeInsets.all(16.0);
  static const EdgeInsets _screenPadding = EdgeInsets.all(12.0);
  static const double _responsiveBreakpoint = 600.0;

  // 文本编辑控制器
  final TextEditingController _nameController = TextEditingController();
  
  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer<DataCollectionController>(
      builder: (context, controller, child) {
        _syncNameController(controller);
        
        return Scaffold(
          appBar: _buildAppBar(controller),
          body: _buildBody(controller),
        );
      }
    );
  }
  
  // UI构建方法
  AppBar _buildAppBar(DataCollectionController controller) {
    return AppBar(
      title: const Text('数据收集设置'),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: _buildSaveButton(controller),
        ),
      ],
    );
  }

  Widget _buildSaveButton(DataCollectionController controller) {
    return Tooltip(
      message: '保存当前设置',
      child: Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(6),
            onTap: () => _handleSaveSettings(controller),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.save, color: Colors.white, size: 18),
                SizedBox(width: 6),
                Text(
                  '保存',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody(DataCollectionController controller) {
    return SingleChildScrollView(
      padding: _screenPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusArea(controller),
          const SizedBox(height: 16),
          _buildSettingsCard(controller),
          const SizedBox(height: 16),
          _buildDataOperationCard(controller),
          const SizedBox(height: 16),
          _buildDataDisplayArea(controller),
          const SizedBox(height: 16),
          _buildActionButtonsRow(controller),
        ],
      ),
    );
  }

  Widget _buildSettingsCard(DataCollectionController controller) {
    return Card(
      elevation: _cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      child: Padding(
        padding: _cardPadding,
        child: _buildSettingsGrid(controller),
      ),
    );
  }

  Widget _buildDataOperationCard(DataCollectionController controller) {
    return Card(
      elevation: _cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      child: Padding(
        padding: _cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('数据操作', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildDataOperationButtons(controller),
            
            if (controller.isUploading || controller.uploadStatus.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: _buildUploadStatusArea(controller),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataDisplayArea(DataCollectionController controller) {
    if (controller.hasFetchedData) {
      return _buildCollectionsDisplay(controller);
    } else {
      return StatusDisplayComponent.create(
        message: '没有数据信息',
        type: StatusDisplayType.neutral,
        icon: const Icon(Icons.info_outline, size: 20),
        description: '请点击"获取数据"按钮获取最新的数据集合信息',
        borderRadius: _borderRadius,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      );
    }
  }

  // 事件处理方法
  void _handleSaveSettings(DataCollectionController controller) {
    controller.saveSettings(context);
    MessageComponent.showIconToast(
      context: context,
      message: '保存数据收集设置...',
      type: MessageType.info,
      duration: const Duration(seconds: 1),
    );
  }

  void _handleFetchData(DataCollectionController controller) {
    controller.fetchData(context);
    MessageComponent.showIconToast(
      context: context,
      message: '正在获取数据信息...',
      type: MessageType.info,
      duration: const Duration(seconds: 1),
    );
  }

  void _handleUploadData(DataCollectionController controller) {
    controller.uploadData(context);
  }

  // 辅助方法
  void _syncNameController(DataCollectionController controller) {
    if (_nameController.text != controller.baseName) {
      _nameController.text = controller.baseName;
    }
  }

  bool _isNarrowScreen(double width) => width < _responsiveBreakpoint;

  String _formatTimestamp(String timestamp) {
    if (timestamp.isEmpty) return '';
    
    try {
      final dateTime = DateTime.parse(timestamp);
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    } catch (e) {
      return timestamp;
    }
  }

  // 状态区域构建
  Widget _buildStatusArea(DataCollectionController controller) {
    // 优先显示错误状态
    if (controller.lastError.isNotEmpty) {
      return Column(
        children: [
          StatusDisplayComponent.create(
            message: '操作失败',
            type: StatusDisplayType.error,
            description: controller.lastError,
            borderRadius: _borderRadius,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            trailing: IconButton(
              onPressed: () => controller.clearError(),
              icon: const Icon(Icons.close, size: 16),
              tooltip: '关闭错误提示',
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              style: IconButton.styleFrom(
                foregroundColor: Colors.red,
              ),
            ),
          ),
          const SizedBox(height: 8),
          _buildNormalStatusArea(controller),
        ],
      );
    }

    // 显示加载状态
    if (controller.isLoading) {
      return StatusDisplayComponent.create(
        message: '正在处理...',
        type: StatusDisplayType.info,
        description: '请稍候，正在处理您的请求',
        borderRadius: _borderRadius,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        icon: const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }

    return _buildNormalStatusArea(controller);
  }

  Widget _buildNormalStatusArea(DataCollectionController controller) {
    final isEnabled = controller.isEnabled;
    final statusType = isEnabled ? StatusDisplayType.success : StatusDisplayType.warning;
    final statusMessage = isEnabled ? '数据收集已开启' : '数据收集已关闭';

    final displayName = _buildDisplayName(controller);
    final description = isEnabled
        ? '当前数据收集名称：$displayName'
        : '当前数据收集名称：$displayName\n\n💡 提示：请先开启数据收集开关，然后配置相关设置并保存';

    return StatusDisplayComponent.create(
      message: statusMessage,
      type: statusType,
      description: description,
      borderRadius: _borderRadius,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
    );
  }

  String _buildDisplayName(DataCollectionController controller) {
    final gameName = controller.gameName;
    final collectionName = controller.baseName;
    final teamSuffix = controller.getTeamSuffix(controller.teamSide);
    
    return collectionName.isNotEmpty 
        ? '$gameName\_$collectionName$teamSuffix' 
        : '$gameName$teamSuffix';
  }

  // 设置区域构建
  Widget _buildSettingsGrid(DataCollectionController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildEnableSwitch(controller),
        const Divider(),
        const Text('基本信息', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 12),
        _buildBasicInfoSection(controller),
        const SizedBox(height: 16),
        const Divider(),
        const Text('热键设置', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 12),
        _buildHotkeySection(controller),
      ],
    );
  }

  Widget _buildEnableSwitch(DataCollectionController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: controller.isEnabled ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(_borderRadius),
        border: Border.all(
          color: controller.isEnabled ? Colors.green.withOpacity(0.3) : Colors.orange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            controller.isEnabled ? Icons.check_circle : Icons.warning,
            color: controller.isEnabled ? Colors.green : Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '启用数据收集',
                  style: TextStyle(
                    fontSize: 15, 
                    fontWeight: FontWeight.w500,
                    color: controller.isEnabled ? Colors.green : Colors.orange,
                  ),
                ),
                if (!controller.isEnabled)
                  const Text(
                    '请开启此开关以启用数据收集功能',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
              ],
            ),
          ),
          Switch(
            value: controller.isEnabled,
            activeColor: Colors.green,
            onChanged: (value) {
              controller.isEnabled = value;
              controller.notifyListeners();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection(DataCollectionController controller) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_isNarrowScreen(constraints.maxWidth)) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildNameInput(controller),
              const SizedBox(height: 12),
              _buildTeamDropdown(controller),
            ],
          );
        } else {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: 6, child: _buildNameInput(controller)),
              const SizedBox(width: 16),
              Expanded(flex: 4, child: _buildTeamDropdown(controller)),
            ],
          );
        }
      }
    );
  }

  Widget _buildHotkeySection(DataCollectionController controller) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_isNarrowScreen(constraints.maxWidth)) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMapHotkeyDropdown(controller),
              const SizedBox(height: 12),
              _buildTargetHotkeyDropdown(controller),
            ],
          );
        } else {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: _buildMapHotkeyDropdown(controller)),
              const SizedBox(width: 16),
              Expanded(child: _buildTargetHotkeyDropdown(controller)),
            ],
          );
        }
      }
    );
  }

  Widget _buildNameInput(DataCollectionController controller) {
    return InputComponent.createInput(
      label: '数据收集名称',
      hint: '输入此次数据收集的名称',
      icon: Icons.edit,
      controller: _nameController,
      onChanged: (value) => controller.collectionName = value,
    );
  }

  Widget _buildTeamDropdown(DataCollectionController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('选择阵营', style: TextStyle(fontWeight: FontWeight.w500, fontSize: 13)),
        const SizedBox(height: 4),
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            hintText: '选择阵营',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          ),
          value: controller.teamSide,
          style: const TextStyle(fontSize: 14, color: Colors.black87),
          onChanged: (String? newValue) {
            if (newValue != null) {
              controller.teamSide = newValue;
            }
          },
          items: controller.availableTeams.map<DropdownMenuItem<String>>((String value) {
            final teamConfig = _getTeamConfig(value);
            return DropdownMenuItem<String>(
              value: value,
              child: Row(
                children: [
                  Icon(teamConfig['icon'], size: 16, color: teamConfig['color']),
                  const SizedBox(width: 8),
                  Text(value),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildMapHotkeyDropdown(DataCollectionController controller) {
    return _buildHotkeyDropdown(
      controller, 
      '地图数据收集热键', 
      '用于收集当前地图信息和游戏场景数据',
      controller.mapHotkeyValue,
      (value) {
        if (value != null) {
          controller.mapHotkeyValue = value;
          controller.notifyListeners();
        }
      },
    );
  }

  Widget _buildTargetHotkeyDropdown(DataCollectionController controller) {
    return _buildHotkeyDropdown(
      controller, 
      '目标数据收集热键', 
      '用于收集游戏中目标和敌人信息',
      controller.targetHotkeyValue,
      (value) {
        if (value != null) {
          controller.targetHotkeyValue = value;
          controller.notifyListeners();
        }
      },
    );
  }

  Widget _buildHotkeyDropdown(
    DataCollectionController controller,
    String label,
    String description,
    String currentValue,
    Function(String?) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 13)),
        const SizedBox(height: 2),
        Text(description, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        const SizedBox(height: 4),
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            hintText: '选择热键',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          ),
          value: currentValue,
          style: const TextStyle(fontSize: 14, color: Colors.black87),
          onChanged: onChanged,
          items: controller.availableHotkeys.map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Row(
                children: [
                  Icon(_getHotkeyIcon(value), size: 16),
                  const SizedBox(width: 8),
                  Text(value),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  // 数据操作区域构建
  Widget _buildDataOperationButtons(DataCollectionController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Button.create(ButtonConfig.textWithIcon(
          '获取数据',
          Icons.download,
          onPressed: () => _handleFetchData(controller),
          type: ButtonType.info,
          size: ButtonSize.medium,
        )),
        const SizedBox(width: 12),
        controller.isUploading 
          ? Button.create(ButtonConfig.textWithIcon(
              '上传中...',
              Icons.hourglass_empty,
              onPressed: null,
              type: ButtonType.secondary,
              size: ButtonSize.medium,
              disabled: true,
            ))
          : Button.create(ButtonConfig.textWithIcon(
              '上传数据',
              Icons.upload,
              onPressed: () => _handleUploadData(controller),
              type: ButtonType.success,
              size: ButtonSize.medium,
            )),
      ],
    );
  }

  Widget _buildUploadStatusArea(DataCollectionController controller) {
    final statusConfig = _getUploadStatusConfig(controller);
    
    final closeButton = controller.isUploading 
        ? null 
        : IconButton(
            icon: const Icon(Icons.close, size: 16),
            onPressed: () => controller.clearUploadStatus(),
            tooltip: '关闭',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          );
    
    return StatusDisplayComponent.create(
      message: statusConfig['message'],
      type: statusConfig['type'],
      description: statusConfig['description'],
      borderRadius: _borderRadius,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      icon: closeButton != null ? null : const Icon(Icons.info, size: 16),
      trailing: closeButton,
    );
  }

  // 集合显示区域构建
  Widget _buildCollectionsDisplay(DataCollectionController controller) {
    final collections = controller.collections;
    
    if (collections.isEmpty) {
      return StatusDisplayComponent.create(
        message: '没有数据集合',
        type: StatusDisplayType.warning,
        icon: const Icon(Icons.warning, size: 20),
        description: '未找到任何数据集合。请检查后端服务器是否正常工作，或者先上传一些数据。',
        borderRadius: _borderRadius,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      );
    }
    
    if (collections.length > 1) {
      return _buildMultiCollectionTabs(collections);
    } else {
      return _buildCollectionInfoCard(collections[0]);
    }
  }

  Widget _buildMultiCollectionTabs(List<Map<String, dynamic>> collections) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(_borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      child: DefaultTabController(
        length: collections.length,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            TabBar(
              tabs: collections.map((collection) {
                final name = collection['name'] as String? ?? '未命名';
                return Tab(
                  child: Text(
                    name,
                    style: const TextStyle(color: Colors.blue),
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              isScrollable: true,
              labelColor: Colors.blue,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.blue,
            ),
            LimitedBox(
              maxHeight: _estimatedCardHeight,
              child: TabBarView(
                children: collections.map((collection) {
                  return SingleChildScrollView(
                    child: _buildCollectionInfoCard(collection),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCollectionInfoCard(Map<String, dynamic> collection) {
    try {
      final collectionInfo = _extractCollectionInfo(collection);
      
      return Padding(
        padding: const EdgeInsets.only(top: 8.0),
        child: StatusDisplayComponent.create(
          message: '数据集合: ${collectionInfo['name']}',
          type: StatusDisplayType.info,
          icon: const Icon(Icons.folder, size: 20),
          description: collectionInfo['description'],
          borderRadius: _borderRadius,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
      );
    } catch (e, stackTrace) {
      print('构建集合信息卡片时出错: $e\n$stackTrace');
      return StatusDisplayComponent.create(
        message: '数据解析错误',
        type: StatusDisplayType.error,
        icon: const Icon(Icons.error_outline, size: 20),
        description: '解析集合数据时出错: $e\n\n请尝试刷新数据或联系开发人员。',
        borderRadius: _borderRadius,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      );
    }
  }

  Widget _buildActionButtonsRow(DataCollectionController controller) {
    return Center(
      child: Button.create(ButtonConfig.textWithIcon(
        '重置为默认值',
        Icons.refresh,
        onPressed: () => controller.resetToDefaults(),
        type: ButtonType.secondary,
        size: ButtonSize.medium,
      )),
    );
  }

  // 配置和辅助方法
  Map<String, dynamic> _getTeamConfig(String team) {
    switch (team) {
      case '警方':
        return {'icon': Icons.shield, 'color': Colors.blue};
      case '匪方':
        return {'icon': Icons.flash_on, 'color': Colors.deepOrange};
      default:
        return {'icon': Icons.remove_circle_outline, 'color': Colors.grey};
    }
  }

  IconData _getHotkeyIcon(String hotkey) {
    switch (hotkey) {
      case '无':
        return Icons.block;
      default:
        return Icons.mouse;
    }
  }

  Map<String, dynamic> _getUploadStatusConfig(DataCollectionController controller) {
    if (controller.isUploading) {
      return {
        'type': StatusDisplayType.loading,
        'message': '正在上传数据...',
        'description': '正在将数据上传至服务器，请稍候...',
      };
    } else if (controller.uploadStatus == 'success') {
      final details = controller.uploadDetails;
      final uploadComplete = details['uploadComplete'] as bool? ?? false;
      final filesDeleted = details['filesDeleted'] as bool? ?? false;
      final customMessage = details['message'] as String? ?? '';
      
      final description = customMessage.isNotEmpty 
          ? customMessage 
          : '${uploadComplete ? '数据上传已完成' : '数据上传未完成'}\n${filesDeleted ? '本地文件已删除' : '本地文件保留'}';
      
      return {
        'type': StatusDisplayType.success,
        'message': '数据上传成功',
        'description': description,
      };
    } else if (controller.uploadStatus == 'failed' || controller.uploadStatus == 'error') {
      return {
        'type': StatusDisplayType.error,
        'message': '数据上传失败',
        'description': controller.uploadError.isNotEmpty 
            ? '错误: ${controller.uploadError}'
            : '上传过程中发生错误，请重试',
      };
    } else {
      return {
        'type': StatusDisplayType.info,
        'message': '上传状态',
        'description': '状态: ${controller.uploadStatus}',
      };
    }
  }

  Map<String, String> _extractCollectionInfo(Map<String, dynamic> collection) {
    final name = collection['name'] as String? ?? '未命名';
    final path = collection['path'] as String? ?? '未知路径';
    
    final lastUpdated = collection['lastUpdated'] as String? ?? '';
    final formattedLastUpdated = _formatTimestamp(lastUpdated);
    
    final folders = collection['folders'] as List? ?? [];
    final detailedStats = collection['detailedStats'] as Map<String, dynamic>? ?? {};
    
    final stats = _calculateCollectionStats(folders, detailedStats, collection);
    
    final description = _buildCollectionDescription(
      name, path, stats, formattedLastUpdated, folders, detailedStats
    );
    
    return {'name': name, 'description': description};
  }

  Map<String, int> _calculateCollectionStats(
    List folders, 
    Map<String, dynamic> detailedStats, 
    Map<String, dynamic> collection
  ) {
    int totalFiles = 0;
    int totalImages = 0;
    int totalLabels = 0;
    
    for (final folder in folders) {
      if (folder is Map<String, dynamic>) {
        totalFiles += (folder['fileCount'] as int? ?? 0);
        totalImages += (folder['imageCount'] as int? ?? 0);
        totalLabels += (folder['labelCount'] as int? ?? 0);
      }
    }
    
    if (totalFiles == 0 && detailedStats.isNotEmpty) {
      detailedStats.forEach((category, stats) {
        if (stats is Map<String, dynamic>) {
          totalFiles += (stats['total'] as int? ?? 0);
          totalImages += (stats['images'] as int? ?? 0);
          totalLabels += (stats['labels'] as int? ?? 0);
        } else if (stats is int) {
          totalFiles += stats;
        }
      });
    }
    
    totalFiles = collection['totalFiles'] as int? ?? totalFiles;
    totalImages = collection['totalImages'] as int? ?? totalImages;
    totalLabels = collection['totalLabels'] as int? ?? totalLabels;
    
    return {
      'totalFiles': totalFiles,
      'totalImages': totalImages,
      'totalLabels': totalLabels,
    };
  }

  String _buildCollectionDescription(
    String name,
    String path,
    Map<String, int> stats,
    String formattedLastUpdated,
    List folders,
    Map<String, dynamic> detailedStats,
  ) {
    final buffer = StringBuffer();
    buffer.write('集合名称: $name\n');
    buffer.write('存储路径: $path\n');
    buffer.write('总文件数: ${stats['totalFiles']} (图像: ${stats['totalImages']}, 标签: ${stats['totalLabels']})\n');
    
    if (formattedLastUpdated.isNotEmpty) {
      buffer.write('最后更新: $formattedLastUpdated\n');
    }
    
    if (detailedStats.isNotEmpty) {
      buffer.write('\n分类统计信息:');
      detailedStats.forEach((category, stats) {
        if (stats is Map<String, dynamic>) {
          final images = stats['images'] as int? ?? 0;
          final labels = stats['labels'] as int? ?? 0;
          final total = stats['total'] as int? ?? 0;
          buffer.write('\n$category: 总文件数 $total (图像: $images, 标签: $labels)');
        } else if (stats is int) {
          buffer.write('\n$category: $stats');
        }
      });
    }
    
    if (folders.isNotEmpty) {
      buffer.write('\n\n文件夹信息:');
      for (final folder in folders) {
        if (folder is Map<String, dynamic>) {
          final folderName = folder['name'] as String? ?? '未知';
          final fileCount = folder['fileCount'] as int? ?? 0;
          final imageCount = folder['imageCount'] as int? ?? 0;
          final labelCount = folder['labelCount'] as int? ?? 0;
          buffer.write('\n$folderName: 总文件数 $fileCount (图像: $imageCount, 标签: $labelCount)');
        }
      }
    }
    
    return buffer.toString();
  }
} 