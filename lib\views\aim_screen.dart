// ignore_for_file: use_super_parameters, library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:provider/provider.dart';
import '../component/Slider_component.dart';
import '../controllers/aim_controller.dart';

/// 瞄准设置页面
class AimScreen extends StatelessWidget {
  const AimScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 使用Consumer<AimController>替代Provider.of获取AimController
    return Consumer<AimController>(
      builder: (context, controller, child) {
        return Scaffold(
          body: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部标题栏和按钮
                _buildHeader(controller, context),
                
                const SizedBox(height: 16),
                
                // 页面内容描述
                const GFCard(
                  color: Colors.white,
                  content: Text(
                    '瞄准参数设置，调整游戏射击时的自动瞄准区域和范围。不同的参数会影响瞄准的精准度和效果。',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // 瞄准参数调整区域
                Expanded(
                  child: SingleChildScrollView(
                    child: _buildAllAimSliders(controller, context),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建顶部标题和按钮
  Widget _buildHeader(AimController controller, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 页面标题
        const Flexible(
          child: Text(
            '瞄准设置',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        
        // 按钮组
        Row(
          children: [
            // 刷新按钮
            Tooltip(
              message: '刷新参数',
              child: Container(
                height: 32, // 与ButtonSize.small的高度一致
                width: 32,  // 保持圆形比例
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF4CAF50), // 浅绿色
                      Color(0xFF2E7D32), // 深绿色
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2E7D32).withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: () => controller.requestAimParams(),
                    child: Container(
                      alignment: Alignment.center,
                      child: const Icon(
                        Icons.refresh,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // 保存按钮
            Tooltip(
              message: '保存当前设置',
              child: Container(
                height: 32,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(6),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(6),
                    onTap: () => controller.saveAimConfig(context),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.save,
                          color: Colors.white,
                          size: 18,
                        ),
                        const SizedBox(width: 6),
                        const Text(
                          '保存',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 创建所有瞄准参数的滑块组
  Widget _buildAllAimSliders(AimController controller, BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 基于屏幕宽度确定布局方式
        bool isWideScreen = constraints.maxWidth > 768;
        
        // 瞄准范围参数组
        Widget aimRangeCard = createSlider(
          cardTitle: '瞄准范围参数',
          configs: [
            SliderConfig(
              title: '瞄准范围',
              value: controller.aimRange,
              min: 0.0,
              max: 416.0,
              step: 1.0,
              onChanged: (value) => controller.aimRange = value,
              onChangeEnd: (_) => controller.handleParameterChanged('aimRange', controller.aimRange, context),
              decimalPlaces: 1,
            ),
            SliderConfig(
              title: '跟踪范围',
              value: controller.trackRange,
              min: 0.001,
              max: 15.0,
              step: 0.1,
              onChanged: (value) => controller.trackRange = value,
              onChangeEnd: (_) => controller.handleParameterChanged('trackRange', controller.trackRange, context),
              decimalPlaces: 1,
            ),
          ],
        );
        
        // 身体部位高度参数组
        Widget bodyHeightCard = createSlider(
          cardTitle: '身体部位高度',
          configs: [
            SliderConfig(
              title: '头部高度',
              value: controller.headHeight,
              min: -100.0,
              max: 100.0,
              step: 1.0,
              onChanged: (value) => controller.headHeight = value,
              onChangeEnd: (_) => controller.handleParameterChanged('headHeight', controller.headHeight, context),
              decimalPlaces: 2,
            ),
            SliderConfig(
              title: '颈部高度',
              value: controller.neckHeight,
              min: -100.0,
              max: 100.0,
              step: 1.0,
              onChanged: (value) => controller.neckHeight = value,
              onChangeEnd: (_) => controller.handleParameterChanged('neckHeight', controller.neckHeight, context),
              decimalPlaces: 2,
            ),
            SliderConfig(
              title: '胸部高度',
              value: controller.chestHeight,
              min: -100.0,
              max: 100.0,
              step: 1.0,
              onChanged: (value) => controller.chestHeight = value,
              onChangeEnd: (_) => controller.handleParameterChanged('chestHeight', controller.chestHeight, context),
              decimalPlaces: 2,
            ),
          ],
        );
        
        // 头部范围参数组
        Widget headRangeCard = createSlider(
          cardTitle: '头部范围',
          configs: [
            SliderConfig(
              title: '头部X范围',
              value: controller.headRangeX,
              min: 0.00,
              max: 50.0,
              step: 0.1,
              onChanged: (value) => controller.headRangeX = value,
              onChangeEnd: (_) => controller.handleParameterChanged('headRangeX', controller.headRangeX, context),
              decimalPlaces: 3,
            ),
            SliderConfig(
              title: '头部Y范围',
              value: controller.headRangeY,
              min: 0.00,
              max: 50.0,
              step: 0.1,
              onChanged: (value) => controller.headRangeY = value,
              onChangeEnd: (_) => controller.handleParameterChanged('headRangeY', controller.headRangeY, context),
              decimalPlaces: 3,
            ),
          ],
        );
        
        // 颈部和胸部范围参数组
        Widget neckChestRangeCard = createSlider(
          cardTitle: '颈部和胸部范围',
          configs: [
            SliderConfig(
              title: '颈部X范围',
              value: controller.neckRangeX,
              min: 0.0,
              max: 50.0,
              step: 0.1,
              onChanged: (value) => controller.neckRangeX = value,
              onChangeEnd: (_) => controller.handleParameterChanged('neckRangeX', controller.neckRangeX, context),
              decimalPlaces: 2,
            ),
            SliderConfig(
              title: '颈部Y范围',
              value: controller.neckRangeY,
              min: 0.0,
              max: 50.0,
              step: 0.1,
              onChanged: (value) => controller.neckRangeY = value,
              onChangeEnd: (_) => controller.handleParameterChanged('neckRangeY', controller.neckRangeY, context),
              decimalPlaces: 2,
            ),
            SliderConfig(
              title: '胸部X范围',
              value: controller.chestRangeX,
              min: 0.0,
              max: 50.0,
              step: 0.1,
              onChanged: (value) => controller.chestRangeX = value,
              onChangeEnd: (_) => controller.handleParameterChanged('chestRangeX', controller.chestRangeX, context),
              decimalPlaces: 2,
            ),
            SliderConfig(
              title: '胸部Y范围',
              value: controller.chestRangeY,
              min: 0.0,
              max: 50.0,
              step: 0.1,
              onChanged: (value) => controller.chestRangeY = value,
              onChangeEnd: (_) => controller.handleParameterChanged('chestRangeY', controller.chestRangeY, context),
              decimalPlaces: 2,
            ),
          ],
        );
        
        // 根据屏幕宽度选择布局方式
        return Column(
          children: [
            // 第一行卡片
            isWideScreen
                ? Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child: aimRangeCard),
                      const SizedBox(width: 16),
                      Expanded(child: bodyHeightCard),
                    ],
                  )
                : Column(
                    children: [
                      aimRangeCard,
                      const SizedBox(height: 16),
                      bodyHeightCard,
                    ],
                  ),
            
            const SizedBox(height: 16),
            
            // 第二行卡片
            isWideScreen
                ? Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child: headRangeCard),
                      const SizedBox(width: 16),
                      Expanded(child: neckChestRangeCard),
                    ],
                  )
                : Column(
                    children: [
                      headRangeCard,
                      const SizedBox(height: 16),
                      neckChestRangeCard,
                    ],
                  ),
            
            const SizedBox(height: 20),
            
            // 重置按钮
            Center(
              child: GFButton(
                onPressed: () => controller.resetToDefaults(context),
                text: '恢复默认值',
                icon: const Icon(Icons.restore, color: Colors.white),
                color: GFColors.SECONDARY,
                size: GFSize.MEDIUM,
                shape: GFButtonShape.standard,
              ),
            ),
          ],
        );
      },
    );
  }
} 