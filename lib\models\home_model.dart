import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'game_model.dart';
import 'auth_model.dart';

/// 首页配置模型 - 管理首页相关的配置数据
class HomeModel extends ChangeNotifier {
  // 依赖的模型
  GameModel? _gameModel;
  AuthModel? _authModel;
  
  // 元数据
  String _username = 'admin';
  String _gameName = 'csgo2';
  String _createdAt = '';
  String _updatedAt = '';
  
  // Getters
  String get username => _username;
  String get gameName => _gameName;
  String get createdAt => _createdAt;
  String get updatedAt => _updatedAt;
  
  // 从依赖模型获取数据的Getters
  String get currentGame => _gameModel?.currentGame ?? 'csgo2';
  String get cardKey => _gameModel?.cardKey ?? '';
  String get cardKeyExpireTime => _gameModel?.cardKeyExpireTime ?? '';
  int get heartbeatInterval => _gameModel?.heartbeatInterval ?? 30;
  
  /// 初始化方法，设置依赖的模型
  void initialize(BuildContext context) {
    _gameModel = Provider.of<GameModel>(context, listen: false);
    _authModel = Provider.of<AuthModel>(context, listen: false);
    
    if (_authModel != null) {
      _username = _authModel!.username;
    }
    
    if (_gameModel != null) {
      _gameName = _gameModel!.currentGame;
    }
    
    _updateTimestamp();
  }
  
  /// 更新时间戳
  void _updateTimestamp() {
    _updatedAt = DateTime.now().toIso8601String();
  }
  
  /// 从JSON加载配置
  void fromJson(Map<String, dynamic> json) {
    try {
      final content = json['content'];
      if (content == null) return;
      
      if (content['username'] != null) {
        _username = content['username'];
      }
      
      if (content['gameName'] != null) {
        _gameName = content['gameName'];
        // 同步到GameModel
        _gameModel?.updateCurrentGame(_gameName);
      }
      
      if (content['cardKey'] != null) {
        // 同步到GameModel
        _gameModel?.updateCardKey(content['cardKey']);
      }

      if (content['cardKeyExpireTime'] != null) {
        // 同步到GameModel（仅用于显示）
        _gameModel?.updateCardKeyExpireTime(content['cardKeyExpireTime']);
      }
      
      if (content['heartbeatInterval'] != null) {
        // 同步到GameModel
        final interval = _parseIntValue(content['heartbeatInterval']);
        _gameModel?.updateHeartbeatInterval(interval);
      }
      
      if (content['createdAt'] != null) {
        _createdAt = content['createdAt'];
      }
      
      if (content['updatedAt'] != null) {
        _updatedAt = content['updatedAt'];
      }
      
      _updateTimestamp();
      notifyListeners();
    } catch (e) {
      debugPrint('HomeModel fromJson error: $e');
    }
  }
  
  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'action': 'home_modify',
      'content': {
        'username': _username,
        'gameName': currentGame,
        'cardKey': cardKey,
        'heartbeatInterval': heartbeatInterval,
        'createdAt': _createdAt,
        'updatedAt': _updatedAt,
      }
    };
  }
  
  /// 解析整数值
  int _parseIntValue(dynamic value) {
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }
  
  /// 获取JSON字符串
  String toJsonString() {
    return toJson().toString();
  }
  
  /// 重置为默认值
  void resetToDefaults() {
    _gameModel?.updateCurrentGame('csgo2');
    _gameModel?.updateCardKey('');
    _gameModel?.updateHeartbeatInterval(30);
    _updateTimestamp();
    notifyListeners();
  }
  
  /// 获取配置摘要
  Map<String, dynamic> getSummary() {
    return {
      'currentGame': currentGame,
      'hasCardKey': cardKey.isNotEmpty,
      'heartbeatInterval': heartbeatInterval,
      'lastUpdated': _updatedAt,
    };
  }

  /// 保存到本地存储
  Future<void> saveToPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 保存首页配置到本地存储
      await prefs.setString('home_username', _username);
      await prefs.setString('home_gameName', _gameName);
      await prefs.setString('home_createdAt', _createdAt);
      await prefs.setString('home_updatedAt', _updatedAt);

      debugPrint('HomeModel: 配置已保存到本地存储');
    } catch (e) {
      debugPrint('HomeModel: 保存配置到本地存储失败: $e');
    }
  }

  /// 从本地存储加载
  Future<void> loadFromPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _username = prefs.getString('home_username') ?? 'admin';
      _gameName = prefs.getString('home_gameName') ?? 'csgo2';
      _createdAt = prefs.getString('home_createdAt') ?? '';
      _updatedAt = prefs.getString('home_updatedAt') ?? '';

      notifyListeners();
      debugPrint('HomeModel: 配置已从本地存储加载');
    } catch (e) {
      debugPrint('HomeModel: 从本地存储加载配置失败: $e');
    }
  }
}
