import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import '../utils/logger.dart';

/// 配置管理模型 - 管理配置的导入导出功能
class ConfigManagementModel extends ChangeNotifier {
  final Logger _logger = Logger();
  static const String _tag = 'ConfigManagementModel';
  
  // 配置类型枚举
  static const Map<String, String> configTypes = {
    'all': '全部配置',
    'home': '首页设置',
    'function': '功能设置',
    'pid': 'PID设置',
    'fov': '视野设置',
    'aim': '瞄准设置',
    'fire': '射击设置',
    'data_collection': '数据收集',
  };
  
  // 状态变量
  bool _isExporting = false;
  bool _isImporting = false;

  // 错误信息
  String? _lastError;
  String _lastExportPath = '';
  String _lastImportPath = '';
  Map<String, dynamic> _lastExportedConfig = {};
  Map<String, dynamic> _lastImportedConfig = {};
  
  // Getters
  bool get isExporting => _isExporting;
  bool get isImporting => _isImporting;
  String? get lastError => _lastError;
  String get lastExportPath => _lastExportPath;
  String get lastImportPath => _lastImportPath;
  Map<String, dynamic> get lastExportedConfig => Map.from(_lastExportedConfig);
  Map<String, dynamic> get lastImportedConfig => Map.from(_lastImportedConfig);
  
  /// 导出配置到文件
  Future<bool> exportConfig({
    required Map<String, dynamic> configData,
    required String configType,
    String? customFileName,
  }) async {
    try {
      _isExporting = true;
      notifyListeners();
      
      _logger.i(_tag, '开始导出配置', {
        'configType': configType,
        'customFileName': customFileName,
      });
      
      // 生成文件名
      String fileName;
      if (customFileName != null && customFileName.isNotEmpty) {
        // 确保自定义文件名有.json扩展名
        fileName = customFileName.endsWith('.json')
            ? customFileName
            : '$customFileName.json';
      } else {
        fileName = _generateFileName(configType);
      }
      
      // 准备导出数据
      final exportData = {
        'version': '1.0.0',
        'exportTime': DateTime.now().toIso8601String(),
        'configType': configType,
        'configData': configData,
        'metadata': {
          'appName': 'BlWeb',
          'exportedBy': 'ConfigManagementSystem',
        }
      };
      
      // 选择保存位置
      final result = await FilePicker.platform.saveFile(
        dialogTitle: '导出配置文件',
        fileName: fileName,
        type: FileType.custom,
        allowedExtensions: ['json'],
      );
      
      if (result != null) {
        final file = File(result);
        await file.writeAsString(
          const JsonEncoder.withIndent('  ').convert(exportData),
          encoding: utf8,
        );
        
        _lastExportPath = result;
        _lastExportedConfig = exportData;
        
        _logger.i(_tag, '配置导出成功', {
          'filePath': result,
          'fileSize': await file.length(),
        });
        
        return true;
      } else {
        _logger.w(_tag, '用户取消了导出操作');
        return false;
      }
    } catch (e, stackTrace) {
      _logger.e(_tag, '导出配置失败', e, stackTrace);
      return false;
    } finally {
      _isExporting = false;
      notifyListeners();
    }
  }
  
  /// 从文件导入配置
  Future<Map<String, dynamic>?> importConfig() async {
    try {
      _isImporting = true;
      _lastError = null; // 清除之前的错误
      notifyListeners();

      _logger.i(_tag, '开始导入配置');

      // 选择文件
      final result = await FilePicker.platform.pickFiles(
        dialogTitle: '选择配置文件',
        type: FileType.custom,
        allowedExtensions: ['json'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final platformFile = result.files.first;

        // 读取文件内容 - Web环境兼容处理
        String content;
        try {
          if (kIsWeb) {
            // Web环境：使用bytes读取
            _logger.i(_tag, 'Web环境：使用bytes方式读取文件', {
              'fileName': platformFile.name,
              'fileSize': platformFile.size,
              'hasBytes': platformFile.bytes != null,
              'bytesLength': platformFile.bytes?.length,
            });

            if (platformFile.bytes == null) {
              _lastError = 'Web环境下无法读取文件数据';
              _logger.e(_tag, _lastError!);
              return null;
            }
            content = utf8.decode(platformFile.bytes!);
            _logger.i(_tag, 'Web环境：文件内容读取成功', {
              'contentLength': content.length,
              'contentPreview': content.length > 100 ? '${content.substring(0, 100)}...' : content,
            });
          } else {
            // 原生环境：使用path读取
            _logger.i(_tag, '原生环境：使用path方式读取文件', {
              'fileName': platformFile.name,
              'filePath': platformFile.path,
              'fileSize': platformFile.size,
            });

            if (platformFile.path == null) {
              _lastError = '无法获取文件路径';
              _logger.e(_tag, _lastError!);
              return null;
            }
            final file = File(platformFile.path!);

            // 检查文件是否存在
            if (!await file.exists()) {
              _lastError = '选择的文件不存在';
              _logger.e(_tag, _lastError!);
              return null;
            }

            content = await file.readAsString(encoding: utf8);
            _logger.i(_tag, '原生环境：文件内容读取成功', {
              'contentLength': content.length,
            });
          }
        } catch (e) {
          _lastError = '无法读取文件内容，请检查文件是否损坏';
          _logger.e(_tag, _lastError!, e);
          return null;
        }

        // 检查文件是否为空
        if (content.trim().isEmpty) {
          _lastError = '配置文件为空';
          _logger.e(_tag, _lastError!);
          return null;
        }

        // 解析JSON
        Map<String, dynamic> importData;
        try {
          importData = json.decode(content) as Map<String, dynamic>;
        } catch (e) {
          _lastError = '配置文件格式错误，不是有效的JSON格式';
          _logger.e(_tag, _lastError!, e);
          return null;
        }

        // 验证配置文件格式
        final validationResult = _validateConfigFileDetailed(importData);
        if (!validationResult['isValid']) {
          _lastError = '${validationResult['reason']}: ${validationResult['details']}';
          _logger.e(_tag, '配置文件格式无效', {
            'reason': validationResult['reason'],
            'details': validationResult['details']
          });
          return null;
        }

        // 记录导入路径 - Web环境兼容处理
        if (kIsWeb) {
          _lastImportPath = platformFile.name; // Web环境使用文件名
        } else {
          _lastImportPath = platformFile.path!; // 原生环境使用完整路径
        }

        _lastImportedConfig = importData;
        _lastError = null; // 成功时清除错误

        _logger.i(_tag, '配置导入成功', {
          'filePath': _lastImportPath,
          'fileName': platformFile.name,
          'fileSize': kIsWeb ? platformFile.bytes?.length : platformFile.size,
          'configType': importData['configType'],
          'version': importData['version'],
        });

        return importData;
      } else {
        _logger.w(_tag, '用户取消了导入操作');
        return null; // 用户取消，不设置错误信息
      }
    } catch (e, stackTrace) {
      _lastError = '导入配置时发生未知错误: ${e.toString()}';
      _logger.e(_tag, '导入配置失败', e, stackTrace);
      return null;
    } finally {
      _isImporting = false;
      notifyListeners();
    }
  }
  
  /// 生成文件名
  String _generateFileName(String configType) {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final timeStr = '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
    
    final typeName = configTypes[configType] ?? configType;
    return 'BlWeb_${typeName}_${dateStr}_$timeStr.json';
  }
  
  /// 验证配置文件格式 - 详细版本
  Map<String, dynamic> _validateConfigFileDetailed(Map<String, dynamic> data) {
    try {
      // 检查必需字段
      if (!data.containsKey('version')) {
        return {
          'isValid': false,
          'reason': '缺少版本信息',
          'details': '配置文件必须包含version字段'
        };
      }

      if (!data.containsKey('configType')) {
        return {
          'isValid': false,
          'reason': '缺少配置类型',
          'details': '配置文件必须包含configType字段'
        };
      }

      if (!data.containsKey('configData')) {
        return {
          'isValid': false,
          'reason': '缺少配置数据',
          'details': '配置文件必须包含configData字段'
        };
      }

      // 检查版本兼容性
      final version = data['version'] as String?;
      if (version == null) {
        return {
          'isValid': false,
          'reason': '版本信息无效',
          'details': 'version字段必须是字符串类型'
        };
      }

      if (!_isVersionCompatible(version)) {
        return {
          'isValid': false,
          'reason': '版本不兼容',
          'details': '当前只支持1.x.x版本，您的文件版本是: $version'
        };
      }

      // 检查配置类型
      final configType = data['configType'] as String?;
      if (configType == null) {
        return {
          'isValid': false,
          'reason': '配置类型无效',
          'details': 'configType字段必须是字符串类型'
        };
      }

      if (!configTypes.containsKey(configType)) {
        return {
          'isValid': false,
          'reason': '不支持的配置类型',
          'details': '配置类型"$configType"不受支持，支持的类型: ${configTypes.keys.join(", ")}'
        };
      }

      // 检查配置数据是否为空
      final configData = data['configData'];
      if (configData == null || (configData is Map && configData.isEmpty)) {
        return {
          'isValid': false,
          'reason': '配置数据为空',
          'details': '配置文件中没有有效的配置数据'
        };
      }

      return {
        'isValid': true,
        'reason': '验证通过',
        'details': '配置文件格式正确'
      };
    } catch (e) {
      _logger.e(_tag, '验证配置文件时出错', e);
      return {
        'isValid': false,
        'reason': '验证过程出错',
        'details': '验证配置文件时发生异常: ${e.toString()}'
      };
    }
  }

  /// 验证配置文件格式 - 简化版本（保持向后兼容）
  bool _validateConfigFile(Map<String, dynamic> data) {
    final result = _validateConfigFileDetailed(data);
    return result['isValid'] as bool;
  }
  
  /// 检查版本兼容性
  bool _isVersionCompatible(String version) {
    // 简单的版本兼容性检查
    // 目前只支持1.x.x版本
    return version.startsWith('1.');
  }
  
  /// 清除导入导出历史
  void clearHistory() {
    _lastExportPath = '';
    _lastImportPath = '';
    _lastExportedConfig.clear();
    _lastImportedConfig.clear();
    notifyListeners();
    
    _logger.i(_tag, '已清除导入导出历史');
  }
  
  /// 获取配置文件信息
  Map<String, dynamic> getConfigFileInfo(Map<String, dynamic> configData) {
    return {
      'version': configData['version'] ?? 'Unknown',
      'exportTime': configData['exportTime'] ?? 'Unknown',
      'configType': configData['configType'] ?? 'Unknown',
      'configTypeName': configTypes[configData['configType']] ?? 'Unknown',
      'hasMetadata': configData.containsKey('metadata'),
      'dataSize': configData['configData']?.length ?? 0,
    };
  }
}
