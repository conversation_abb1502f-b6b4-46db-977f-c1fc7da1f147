// ignore_for_file: use_super_parameters, avoid_unnecessary_containers, prefer_if_null_operators, unused_element, unused_local_variable

import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';

class IconDropdownItem {
  final String value;
  final String text;
  final IconData? icon;
  final Widget? image;

  IconDropdownItem({
    required this.value,
    required this.text,
    this.icon,
    this.image,
  }) : assert(icon != null || image != null, '必须提供icon或image中的一个');
}

class IconDropdown extends StatelessWidget {
  final String? value;
  final List<IconDropdownItem> items;
  final Function(String?) onChanged;
  final IconData? dropdownIcon;
  final Widget? dropdownImage;
  final double height;
  final double? width;
  final EdgeInsetsGeometry? margin;
  final EdgeInsets padding;
  final BorderRadius? borderRadius;
  final BorderSide border;
  final Color? dropdownButtonColor;
  final TextStyle? textStyle;
  final double iconSize;
  final bool showTextOnSmallScreens;
  final double smallScreenWidth;
  final double? dropdownMaxHeight;
  final double? dropdownOffset;

  const IconDropdown({
    Key? key,
    required this.value,
    required this.items,
    required this.onChanged,
    this.dropdownIcon,
    this.dropdownImage,
    this.height = 50,
    this.width,
    this.margin,
    this.padding = const EdgeInsets.all(15),
    this.borderRadius,
    this.border = const BorderSide(color: Colors.black12, width: 1),
    this.dropdownButtonColor,
    this.textStyle,
    this.iconSize = 24,
    this.showTextOnSmallScreens = true,
    this.smallScreenWidth = 600,
    this.dropdownMaxHeight,
    this.dropdownOffset,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 检测是否为小屏幕
    final isSmallScreen = MediaQuery.of(context).size.width < smallScreenWidth;
    
    // 安全检查：确保items不为空
    if (items.isEmpty) {
      return Container(
        height: height,
        width: width,
        margin: margin,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: borderRadius ?? BorderRadius.circular(10),
        ),
        child: const Center(
          child: Text('无可选项', style: TextStyle(color: Colors.grey)),
        ),
      );
    }
    
    return Container(
      height: height,
      width: width,
      margin: margin,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 计算可用宽度，确保不超出约束
          final availableWidth = constraints.maxWidth;
          final effectiveWidth = width != null 
              ? width!.clamp(50.0, availableWidth) // 最小宽度50px
              : availableWidth;
          
          // 计算屏幕剩余高度，确保下拉菜单不会超出屏幕
          final screenHeight = MediaQuery.of(context).size.height;
          final renderBox = context.findRenderObject() as RenderBox?;
          final buttonPosition = renderBox?.localToGlobal(Offset.zero) ?? Offset.zero;
          final remainingHeight = screenHeight - buttonPosition.dy - height - 100; // 预留100px底部空间
          final calculatedMaxHeight = dropdownMaxHeight ?? remainingHeight.clamp(150.0, 300.0);
          
          return Container(
            width: effectiveWidth,
            height: height,
            decoration: BoxDecoration(
              color: dropdownButtonColor ?? Colors.grey[300],
              borderRadius: borderRadius ?? BorderRadius.circular(10),
              border: Border.fromBorderSide(border),
            ),
            child: Theme(
              // 使用Theme来确保下拉菜单样式一致
              data: Theme.of(context).copyWith(
                canvasColor: dropdownButtonColor ?? Colors.grey[300],
              ),
              child: DropdownButtonHideUnderline(
                child: ButtonTheme(
                  alignedDropdown: true, // 确保下拉菜单与按钮对齐
                  child: DropdownButton<String>(
                    value: value,
                    onChanged: onChanged,
                    icon: dropdownImage ?? (dropdownIcon != null ? Icon(dropdownIcon, size: iconSize) : const Icon(Icons.arrow_drop_down)),
                    iconSize: iconSize,
                    isExpanded: true,
                    style: textStyle,
                    dropdownColor: dropdownButtonColor ?? Colors.grey[300],
                    // 设置下拉菜单的最大高度，确保不会超出屏幕
                    menuMaxHeight: calculatedMaxHeight,
                    // 设置下拉菜单的阴影
                    elevation: 8,
                    // 确保下拉菜单有足够的空间显示
                    isDense: false,
                    // 设置焦点颜色
                    focusColor: Colors.transparent,
                    items: items
                        .map((item) => DropdownMenuItem<String>(
                              value: item.value,
                              child: Container(
                                // 确保下拉项有合适的高度和内边距
                                width: double.infinity,
                                constraints: BoxConstraints(
                                  minHeight: 40.0,
                                  maxWidth: effectiveWidth - 20,
                                ),
                                padding: EdgeInsets.symmetric(
                                  horizontal: padding.horizontal / 2,
                                  vertical: 8.0,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: _buildDropdownItem(item, isSmallScreen, effectiveWidth),
                              ),
                            ))
                        .toList(),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
  
  /// 构建下拉菜单项
  Widget _buildDropdownItem(IconDropdownItem item, bool isSmallScreen, double availableWidth) {
    // 计算是否显示文本
    final showText = !isSmallScreen || showTextOnSmallScreens;
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 图标部分
        _buildLeadingWidget(item),
        
        // 文本部分（如果需要显示）
        if (showText) ...[
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              item.text,
              style: textStyle,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ],
    );
  }
  
  Widget _buildLeadingWidget(IconDropdownItem item) {
    if (item.image != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(2),
        child: SizedBox(
          width: iconSize,
          height: iconSize,
          child: FittedBox(
            fit: BoxFit.contain,
            child: item.image!,
          ),
        ),
      );
    } else if (item.icon != null) {
      return Icon(
        item.icon, 
        size: iconSize,
        color: Colors.black87,
      );
    } else {
      return SizedBox(
        width: iconSize,
        height: iconSize,
      ); // 保持一致的尺寸
    }
  }
}

class IconMultiSelectDropdown extends StatelessWidget {
  final List<String> selectedValues;
  final List<IconDropdownItem> items;
  final Function(List<dynamic>) onSelect;
  final String dropdownTitleTileText;
  final Color? dropdownTitleTileColor;
  final EdgeInsets? dropdownTitleTileMargin;
  final EdgeInsets? dropdownTitleTilePadding;
  final BorderSide? dropdownUnderlineBorder;
  final Border? dropdownTitleTileBorder;
  final BorderRadius? dropdownTitleTileBorderRadius;
  final IconData? expandedIcon;
  final IconData? collapsedIcon;
  final Widget? expandedImage;
  final Widget? collapsedImage;
  final Widget? submitButton;
  final Widget? cancelButton;
  final TextStyle? dropdownTitleTileTextStyle;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final GFCheckboxType type;
  final Color activeBgColor;
  final Color activeBorderColor;
  final Color inactiveBorderColor;
  final double iconSize;

  const IconMultiSelectDropdown({
    Key? key,
    required this.selectedValues,
    required this.items,
    required this.onSelect,
    this.dropdownTitleTileText = '请选择',
    this.dropdownTitleTileColor,
    this.dropdownTitleTileMargin,
    this.dropdownTitleTilePadding,
    this.dropdownUnderlineBorder,
    this.dropdownTitleTileBorder,
    this.dropdownTitleTileBorderRadius,
    this.expandedIcon,
    this.collapsedIcon,
    this.expandedImage,
    this.collapsedImage,
    this.submitButton,
    this.cancelButton,
    this.dropdownTitleTileTextStyle,
    this.padding,
    this.margin,
    this.type = GFCheckboxType.basic,
    this.activeBgColor = const Color(0x8000FF00), // 半透明绿色
    this.activeBorderColor = Colors.green,
    this.inactiveBorderColor = Colors.grey,
    this.iconSize = 24,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 将IconDropdownItem列表转换为GFMultiSelect需要的格式
    final List<dynamic> dropList = items
        .map((item) => {
              'value': item.value,
              'text': item.text,
              'icon': item.icon,
              'image': item.image,
            })
        .toList();

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          constraints: BoxConstraints(
            maxWidth: constraints.maxWidth,
          ),
          child: GFMultiSelect(
            items: dropList,
            onSelect: onSelect,
            dropdownTitleTileText: dropdownTitleTileText,
            dropdownTitleTileColor: dropdownTitleTileColor ?? Colors.grey[200],
            dropdownTitleTileMargin: dropdownTitleTileMargin ??
                const EdgeInsets.only(top: 22, left: 18, right: 18, bottom: 5),
            dropdownTitleTilePadding:
                dropdownTitleTilePadding ?? const EdgeInsets.all(10),
            dropdownUnderlineBorder: dropdownUnderlineBorder ??
                const BorderSide(color: Colors.transparent, width: 2),
            dropdownTitleTileBorder: dropdownTitleTileBorder ??
                Border.all(color: Colors.grey[300]!, width: 1),
            dropdownTitleTileBorderRadius:
                dropdownTitleTileBorderRadius ?? BorderRadius.circular(5),
            expandedIcon: expandedImage != null 
                ? expandedImage! 
                : expandedIcon != null
                    ? Icon(
                        expandedIcon,
                        color: Colors.black54,
                      )
                    : const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.black54,
                      ),
            collapsedIcon: collapsedImage != null
                ? collapsedImage!
                : collapsedIcon != null
                    ? Icon(
                        collapsedIcon,
                        color: Colors.black54,
                      )
                    : const Icon(
                        Icons.keyboard_arrow_up,
                        color: Colors.black54,
                      ),
            submitButton: submitButton ?? const Text('确定'),
            cancelButton: cancelButton ?? const Text('取消'),
            dropdownTitleTileTextStyle: dropdownTitleTileTextStyle ??
                const TextStyle(fontSize: 14, color: Colors.black54),
            padding: padding ?? const EdgeInsets.all(6),
            margin: margin ?? const EdgeInsets.all(6),
            type: type,
            activeBgColor: activeBgColor,
            activeBorderColor: activeBorderColor,
            inactiveBorderColor: inactiveBorderColor,
          ),
        );
      },
    );
  }
  
  Widget _buildItemLeadingWidget(dynamic item) {
    if (item['image'] != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(2),
        child: SizedBox(
          width: iconSize,
          height: iconSize,
          child: FittedBox(
            fit: BoxFit.contain,
            child: item['image'],
          ),
        ),
      );
    } else if (item['icon'] != null) {
      return Icon(
        item['icon'], 
        size: iconSize,
        color: Colors.black87,
      );
    } else {
      return SizedBox(
        width: iconSize,
        height: iconSize,
      );
    }
  }
}

/* 使用示例:

// 单选下拉菜单示例
// -------------------------------------
// 1. 定义状态变量
String selectedValue = '选项1';

// 2. 创建下拉菜单 (使用图标)
IconDropdown(
  value: selectedValue,
  items: [
    IconDropdownItem(value: '选项1', text: '选项一', icon: Icons.home),
    IconDropdownItem(value: '选项2', text: '选项二', icon: Icons.settings),
    IconDropdownItem(value: '选项3', text: '选项三', icon: Icons.person),
  ],
  onChanged: (newValue) {
    setState(() {
      selectedValue = newValue!;
    });
  },
  dropdownIcon: Icons.arrow_drop_down,
  height: 50,
  width: 300,
  margin: EdgeInsets.all(10),
  padding: EdgeInsets.all(10),
  borderRadius: BorderRadius.circular(8),
  border: BorderSide(color: Colors.grey, width: 1),
  dropdownButtonColor: Colors.white,
  textStyle: TextStyle(fontSize: 16),
  iconSize: 20,
)

// 2. 创建下拉菜单 (使用图片)
IconDropdown(
  value: selectedValue,
  items: [
    IconDropdownItem(
      value: '选项1', 
      text: '选项一', 
      image: Image.asset('assets/images/home.png', width: 24, height: 24)
    ),
    IconDropdownItem(
      value: '选项2', 
      text: '选项二', 
      image: Image.network('https://example.com/icon.png', width: 24, height: 24)
    ),
    IconDropdownItem(
      value: '选项3', 
      text: '选项三', 
      image: CircleAvatar(
        backgroundImage: NetworkImage('https://example.com/avatar.jpg'),
        radius: 12,
      )
    ),
  ],
  onChanged: (newValue) {
    setState(() {
      selectedValue = newValue!;
    });
  },
  dropdownImage: Image.asset('assets/images/dropdown.png', width: 24, height: 24),
)

// 多选下拉菜单示例
// -------------------------------------
// 1. 定义状态变量
List<String> selectedValues = [];

// 2. 创建多选下拉菜单 (混合使用图标和图片)
IconMultiSelectDropdown(
  selectedValues: selectedValues,
  items: [
    IconDropdownItem(value: '选项1', text: '选项一', icon: Icons.home),
    IconDropdownItem(
      value: '选项2', 
      text: '选项二', 
      image: Image.asset('assets/images/settings.png', width: 24, height: 24)
    ),
    IconDropdownItem(value: '选项3', text: '选项三', icon: Icons.person),
  ],
  onSelect: (values) {
    setState(() {
      selectedValues = values.cast<String>();
    });
  },
  dropdownTitleTileText: '请选择选项',
  expandedIcon: Icons.keyboard_arrow_down,
  collapsedIcon: Icons.keyboard_arrow_up,
  // 也可以使用图片
  // expandedImage: Image.asset('assets/images/down.png', width: 24, height: 24),
  // collapsedImage: Image.asset('assets/images/up.png', width: 24, height: 24),
)

*/
