# 首页页面前端说明

## 页面概述

首页是BlWeb应用的主配置页面，负责游戏选择和卡密管理。采用MVVM架构模式，使用Provider进行状态管理，支持多游戏切换、卡密输入和配置同步功能。与HeaderController实现双向同步，确保游戏选择状态的一致性。

## 文件结构

```
首页模块/
├── views/home_screen.dart           # 首页视图 (195行)
├── controllers/home_controller.dart # 首页控制器 (280行)
├── models/game_model.dart           # 游戏数据模型 (171行)
└── component/card_select_component.dart # 卡片选择组件 (216行)
```

## 核心组件

### HomeScreen (视图层)
- **功能**: 游戏卡片展示、卡密输入、响应式布局适配
- **核心方法**: `_buildHeader()`, `_buildGameSelection()`, `_buildGameCards()`, `_calculateColumns()`
- **常量配置**: 屏幕断点、间距、字体大小等15个常量

### HomeController (控制器层)
- **功能**: 游戏选择业务逻辑、卡密管理、双向同步、服务器通信、Pro状态管理
- **核心方法**: `selectGame()`, `onCardKeyChanged()`, `refreshHomeConfig()`, `saveHomeConfig()`, `_getProStatus()`
- **同步机制**: 与HeaderController双向同步游戏选择状态
- **Pro状态**: 在保存配置时自动发送用户Pro状态信息给后端

### GameModel (数据模型层)
- **功能**: 游戏状态存储、卡密数据管理、状态变化通知
- **核心属性**: `currentGame`, `cardKey`
- **方法**: `updateCurrentGame()`, `updateCardKey()`

### LoginModel (数据模型层)
- **功能**: 用户登录状态管理、Pro状态存储
- **核心属性**: `isPro`, `username`, `token`
- **Pro状态**: 存储用户是否为Pro用户的状态信息

## 游戏支持

支持8款游戏：Apex Legends、CrossFire、CrossFire HD、CS:GO 2、PUBG、生死狙击、生死狙击2、无畏契约

## 状态管理

采用Provider模式和双向同步机制：

```dart
ChangeNotifierProvider<GameModel>
ChangeNotifierProvider<LoginModel>
ChangeNotifierProxyProvider4<ServerService, AuthModel, GameModel, LoginModel, HomeController>
```

双向同步：HomeController ←→ HeaderController ←→ GameModel ←→ LoginModel ←→ 服务器状态

### Pro状态管理
- **获取**: 从LoginModel中获取用户Pro状态
- **发送**: 在所有首页配置请求中自动包含Pro状态信息
- **用途**: 服务器端权限判断和功能控制
- **存储**: Pro状态不保存到数据库，仅作为请求时的状态标识

## 响应式设计

- **断点**: 手机2列、平板3列、桌面4列
- **自适应**: 动态间距、卡片大小、底部栏布局
- **屏幕适配**: 小屏(<600px)、平板(<900px)、大屏(≥900px)

## 组件交互

### 卡密输入组件
```dart
InputComponent.createInput(
  label: '卡密',
  hint: '请输入您的卡密',
  icon: Icons.vpn_key,
  controller: controller.cardKeyController,
  onChanged: controller.onCardKeyChanged,
)
```

### 游戏选择组件
```dart
CardSelectComponent(
  cardItems: controller.getGameCards(),
  onCardSelected: controller.selectGame,
  crossAxisCount: columnsToUse,
  allowAutoScale: true,
)
```

## API通信

### 请求格式
所有首页配置请求都包含以下字段：
```json
{
  "action": "home_read|home_modify",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称", 
    "cardKey": "卡密",
    "isPro": true,
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

### Pro状态字段
- **字段名**: `isPro`
- **类型**: `boolean`
- **来源**: 从LoginModel.isPro获取
- **用途**: 服务器端权限判断，不保存到数据库
- **发送时机**: 每次保存配置和刷新配置时自动发送

### 响应处理
- 服务器响应不包含isPro字段
- 客户端仅处理username、gameName、cardKey等配置数据
- Pro状态由客户端本地维护，不依赖服务器返回

## 优化成果 (2024年)

- **代码减少**: 总计减少93行代码（16%）
- **架构改进**: 方法拆分为12个独立UI构建方法、常量提取15个配置
- **性能提升**: 响应式优化、状态管理优化、资源管理改进
- **双向同步**: 优化与HeaderController的同步机制
- **游戏选择修复**: 解决游戏选择与保存请求不一致的问题

### 游戏选择同步问题修复 (2024年最新)
**问题描述**: 用户在首页选择sjz游戏，但点击保存按钮时发送的请求中gameName却是cfhd，导致保存的游戏与选择的游戏不一致。

**根本原因**:
1. **防抖机制冲突**: GameModel中的`updateCurrentGame`方法有500ms防抖机制，快速切换游戏时会忽略请求
2. **数据源不一致**: `_buildRequestData`方法使用`_gameModel.currentGame`而不是当前选中的`_selectedGame.id`
3. **时序问题**: UI选择更新和GameModel更新之间存在时序差异

**完整解决方案**:
1. **修改数据源**: `_buildRequestData`方法改为优先使用`_selectedGame?.id`，备选使用`_gameModel.currentGame`
2. **增强日志**: 在`selectGame`方法中添加详细的调试日志，包含新旧游戏对比和两个数据源的值
3. **同步优化**: 确保UI选择立即反映到请求数据中，不依赖GameModel的防抖机制

**技术实现**:
```dart
// 修改前：只使用GameModel
'gameName': _gameModel.currentGame,

// 修改后：优先使用当前选中的游戏
final currentGameName = _selectedGame?.id ?? _gameModel.currentGame;
'gameName': currentGameName,
```

**修复效果**:
- ✅ 保存请求中的gameName与UI选择的游戏完全一致
- ✅ 消除GameModel防抖机制对保存操作的影响
- ✅ 增强调试能力，便于排查类似问题
- ✅ 提升用户体验，确保"所见即所得"的操作逻辑

## 使用示例

```dart
// 基本使用
MaterialPageRoute(builder: (context) => const HomeScreen())

// 监听游戏选择变化
Consumer<HomeController>(
  builder: (context, controller, child) {
    return Text('当前游戏: ${controller.selectedGame?.title ?? '未选择'}');
  },
)

// 监听卡密变化
Consumer<GameModel>(
  builder: (context, gameModel, child) {
    return Text('当前卡密: ${gameModel.cardKey}');
  },
)
```
