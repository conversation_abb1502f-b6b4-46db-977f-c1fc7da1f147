# 射击控制系统API

本文档描述射击控制系统的WebSocket API请求和响应格式。

## 请求和响应格式

系统使用两种主要操作：
- `fire_read`: 读取射击参数
- `fire_modify`: 修改射击参数

### 读取射击参数 (fire_read)

**客户端请求**：

```json
{
  "action": "fire_read",
  "content": {
    "username": "用户名",
    "token": "认证令牌",
    "gameName": "游戏名称"
  }
}
```

**服务器响应**：

```json
{
  "action": "fire_read_response",
  "status": "success",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "rifleSleep": 100,       // 步枪休眠
    "rifleInterval": 200,    // 步枪间隔
    "sniperSleep": 300,      // 狙击休眠
    "sniperInterval": 500,   // 狙击间隔
    "weaponSwitchSleep": 50, // 切枪睡眠间隔
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T13:30:00Z"
  }
}
```

### 修改射击参数 (fire_modify)

**客户端请求**：

```json
{
  "action": "fire_modify",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "rifleSleep": 100,       // 步枪休眠
    "rifleInterval": 200,    // 步枪间隔
    "sniperSleep": 300,      // 狙击休眠
    "sniperInterval": 500,   // 狙击间隔
    "weaponSwitchSleep": 50, // 切枪睡眠间隔
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

**服务器响应**：

```json
{
  "action": "fire_modify_response",
  "status": "success",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "rifleSleep": 100,       // 步枪休眠
    "rifleInterval": 200,    // 步枪间隔
    "sniperSleep": 300,      // 狙击休眠
    "sniperInterval": 500,   // 狙击间隔
    "weaponSwitchSleep": 50, // 切枪睡眠间隔
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

## 参数说明

| 参数名称 | 类型 | 描述 | 默认值 | 范围 |
|---------|------|------|-------|------|
| rifleSleep | int | 步枪休眠时间(ms) | 100 | 10-500 |
| rifleInterval | int | 步枪间隔时间(ms) | 200 | 50-1000 |
| sniperSleep | int | 狙击休眠时间(ms) | 300 | 10-500 |
| sniperInterval | int | 狙击间隔时间(ms) | 500 | 50-1000 |
| weaponSwitchSleep | int | 切枪睡眠间隔(ms) | 50 | 10-200 |