<execution>
  <constraint>
    ## Flutter开发客观限制
    - **平台约束**：必须遵循iOS App Store和Google Play商店规范
    - **性能边界**：移动设备的内存、CPU、电池限制
    - **框架版本**：Flutter版本兼容性和API变更影响
    - **平台API限制**：iOS和Android原生API的差异和限制
    - **第三方依赖**：插件质量、维护状态和许可证限制
    - **开发环境**：IDE支持、调试工具、模拟器性能限制
  </constraint>

  <rule>
    ## Flutter开发强制规则
    - **代码规范**：严格遵循Dart代码风格和Flutter最佳实践
    - **状态管理**：必须选择明确的状态管理方案并保持一致
    - **Widget结构**：遵循组件化开发，避免深层嵌套
    - **性能要求**：UI渲染必须维持60fps，启动时间控制在合理范围
    - **平台适配**：必须处理iOS和Android的行为差异
    - **测试覆盖**：核心业务逻辑必须有单元测试和Widget测试
    - **版本管理**：使用语义化版本控制，明确API变更影响
  </rule>

  <guideline>
    ## Flutter开发指导原则
    - **用户体验优先**：UI流畅性和响应性是首要考虑
    - **性能意识**：在开发过程中持续关注性能指标
    - **渐进式开发**：从MVP开始，逐步迭代功能
    - **代码复用**：最大化跨平台代码复用，最小化平台特定代码
    - **社区优先**：优先使用成熟的官方和社区插件
    - **文档完整**：保持代码注释和项目文档的完整性
    - **持续集成**：建立自动化测试和部署流程
  </guideline>

  <process>
    ## Flutter开发完整流程

    ### Phase 1: 项目规划与初始化 (1-2天)
    
    #### 1.1 需求分析
    ```
    功能需求梳理：
    ├── 核心功能定义
    ├── 用户流程设计
    ├── 技术需求评估
    └── 性能目标设定
    
    平台需求分析：
    ├── iOS特定需求
    ├── Android特定需求
    ├── 原生功能集成
    └── 第三方服务集成
    ```

    #### 1.2 技术架构设计
    ```
    状态管理选择：
    - 简单应用：Provider + ChangeNotifier
    - 中等复杂度：BLoC + Freezed
    - 复杂应用：Riverpod + State Notifier
    
    项目结构规划：
    lib/
    ├── core/           # 核心功能
    ├── features/       # 功能模块
    ├── shared/         # 共享组件
    └── main.dart       # 应用入口
    ```

    #### 1.3 开发环境搭建
    ```bash
    # Flutter项目初始化
    flutter create project_name
    cd project_name
    
    # 依赖管理
    flutter pub add provider
    flutter pub add http
    flutter pub add shared_preferences
    
    # 开发工具配置
    flutter pub add --dev flutter_test
    flutter pub add --dev mockito
    flutter pub add --dev build_runner
    ```

    ### Phase 2: 核心架构实现 (2-3天)

    #### 2.1 状态管理架构
    ```dart
    // Provider模式示例
    class AppState extends ChangeNotifier {
      String _title = '';
      
      String get title => _title;
      
      void updateTitle(String newTitle) {
        _title = newTitle;
        notifyListeners();
      }
    }
    
    // BLoC模式示例
    class CounterBloc extends Bloc<CounterEvent, CounterState> {
      CounterBloc() : super(CounterInitial()) {
        on<Increment>((event, emit) {
          emit(CounterUpdated(state.count + 1));
        });
      }
    }
    ```

    #### 2.2 路由系统建立
    ```dart
    // 路由配置
    class AppRouter {
      static Route<dynamic> generateRoute(RouteSettings settings) {
        switch (settings.name) {
          case '/':
            return MaterialPageRoute(builder: (_) => HomeScreen());
          case '/profile':
            return MaterialPageRoute(builder: (_) => ProfileScreen());
          default:
            return MaterialPageRoute(builder: (_) => NotFoundScreen());
        }
      }
    }
    ```

    #### 2.3 数据层架构
    ```dart
    // Repository模式
    abstract class UserRepository {
      Future<User> getUser(String id);
      Future<void> updateUser(User user);
    }
    
    class ApiUserRepository implements UserRepository {
      final HttpClient httpClient;
      
      ApiUserRepository(this.httpClient);
      
      @override
      Future<User> getUser(String id) async {
        final response = await httpClient.get('/users/$id');
        return User.fromJson(response.data);
      }
    }
    ```

    ### Phase 3: UI开发实现 (5-7天)

    #### 3.1 主题和样式系统
    ```dart
    class AppTheme {
      static ThemeData lightTheme = ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        textTheme: GoogleFonts.robotoTextTheme(),
      );
      
      static ThemeData darkTheme = ThemeData(
        brightness: Brightness.dark,
        primarySwatch: Colors.blue,
      );
    }
    ```

    #### 3.2 可复用组件开发
    ```dart
    class CustomButton extends StatelessWidget {
      final String text;
      final VoidCallback onPressed;
      final ButtonStyle? style;
      
      const CustomButton({
        Key? key,
        required this.text,
        required this.onPressed,
        this.style,
      }) : super(key: key);
      
      @override
      Widget build(BuildContext context) {
        return ElevatedButton(
          onPressed: onPressed,
          style: style ?? Theme.of(context).elevatedButtonTheme.style,
          child: Text(text),
        );
      }
    }
    ```

    #### 3.3 响应式布局实现
    ```dart
    class ResponsiveLayout extends StatelessWidget {
      final Widget mobile;
      final Widget? tablet;
      final Widget? desktop;
      
      const ResponsiveLayout({
        Key? key,
        required this.mobile,
        this.tablet,
        this.desktop,
      }) : super(key: key);
      
      @override
      Widget build(BuildContext context) {
        return LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth >= 1200) {
              return desktop ?? tablet ?? mobile;
            } else if (constraints.maxWidth >= 800) {
              return tablet ?? mobile;
            } else {
              return mobile;
            }
          },
        );
      }
    }
    ```

    ### Phase 4: 功能集成开发 (3-5天)

    #### 4.1 API集成
    ```dart
    class ApiService {
      final Dio _dio = Dio();
      
      ApiService() {
        _dio.interceptors.add(LogInterceptor());
        _dio.interceptors.add(AuthInterceptor());
      }
      
      Future<Response> get(String path) async {
        try {
          return await _dio.get(path);
        } on DioError catch (e) {
          throw ApiException.fromDioError(e);
        }
      }
    }
    ```

    #### 4.2 本地存储集成
    ```dart
    class StorageService {
      static const _keyPrefix = 'app_';
      
      Future<void> saveString(String key, String value) async {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('$_keyPrefix$key', value);
      }
      
      Future<String?> getString(String key) async {
        final prefs = await SharedPreferences.getInstance();
        return prefs.getString('$_keyPrefix$key');
      }
    }
    ```

    #### 4.3 原生功能集成
    ```dart
    class PlatformService {
      static const platform = MethodChannel('com.example.app/platform');
      
      Future<String> getPlatformVersion() async {
        try {
          final version = await platform.invokeMethod('getPlatformVersion');
          return version;
        } on PlatformException catch (e) {
          return "Failed to get platform version: '${e.message}'.";
        }
      }
    }
    ```

    ### Phase 5: 测试与质量保证 (2-3天)

    #### 5.1 单元测试
    ```dart
    void main() {
      group('Counter Tests', () {
        late CounterBloc counterBloc;
        
        setUp(() {
          counterBloc = CounterBloc();
        });
        
        tearDown(() {
          counterBloc.close();
        });
        
        test('initial state is CounterInitial', () {
          expect(counterBloc.state, CounterInitial());
        });
        
        blocTest<CounterBloc, CounterState>(
          'emits [CounterUpdated] when Increment is added',
          build: () => counterBloc,
          act: (bloc) => bloc.add(Increment()),
          expect: () => [CounterUpdated(1)],
        );
      });
    }
    ```

    #### 5.2 Widget测试
    ```dart
    void main() {
      testWidgets('Counter increments smoke test', (WidgetTester tester) async {
        await tester.pumpWidget(MyApp());
        
        expect(find.text('0'), findsOneWidget);
        expect(find.text('1'), findsNothing);
        
        await tester.tap(find.byIcon(Icons.add));
        await tester.pump();
        
        expect(find.text('0'), findsNothing);
        expect(find.text('1'), findsOneWidget);
      });
    }
    ```

    #### 5.3 性能测试
    ```dart
    void main() {
      testWidgets('ScrollView performance test', (WidgetTester tester) async {
        await tester.pumpWidget(MyScrollView());
        
        // 性能基准测试
        await tester.fling(find.byType(ListView), 
                           const Offset(0, -200), 800);
        await tester.pump();
        await tester.pump(const Duration(seconds: 1));
        
        // 验证没有性能问题
        expect(tester.binding.transientCallbackCount, 0);
      });
    }
    ```

    ### Phase 6: 平台适配与优化 (2-3天)

    #### 6.1 iOS适配
    ```dart
    // iOS特定适配
    if (Platform.isIOS) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle.light.copyWith(
          statusBarColor: Colors.transparent,
        ),
      );
    }
    
    // Cupertino组件使用
    Widget buildPlatformButton() {
      if (Platform.isIOS) {
        return CupertinoButton(
          child: Text('iOS Button'),
          onPressed: onPressed,
        );
      } else {
        return ElevatedButton(
          child: Text('Android Button'),
          onPressed: onPressed,
        );
      }
    }
    ```

    #### 6.2 Android适配
    ```dart
    // Android特定配置
    if (Platform.isAndroid) {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
      );
    }
    
    // Material组件优化
    Widget buildAndroidDialog() {
      return AlertDialog(
        title: Text('Android Dialog'),
        content: Text('This is optimized for Android'),
        actions: [
          TextButton(
            child: Text('OK'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      );
    }
    ```

    #### 6.3 性能优化
    ```dart
    // Widget优化
    class OptimizedWidget extends StatelessWidget {
      @override
      Widget build(BuildContext context) {
        return const RepaintBoundary(
          child: ExpensiveWidget(),
        );
      }
    }
    
    // 图片优化
    Widget buildOptimizedImage(String url) {
      return CachedNetworkImage(
        imageUrl: url,
        placeholder: (context, url) => CircularProgressIndicator(),
        errorWidget: (context, url, error) => Icon(Icons.error),
        memCacheWidth: 300,
        memCacheHeight: 300,
      );
    }
    ```

    ### Phase 7: 发布部署 (1-2天)

    #### 7.1 应用图标和启动页
    ```yaml
    # pubspec.yaml
    flutter_icons:
      android: "launcher_icon"
      ios: true
      image_path: "assets/icon/icon.png"
      
    flutter_native_splash:
      color: "#42a5f5"
      image: assets/splash.png
    ```

    #### 7.2 构建配置
    ```bash
    # Android Release构建
    flutter build apk --release
    flutter build appbundle --release
    
    # iOS Release构建
    flutter build ios --release
    
    # 代码混淆
    flutter build apk --obfuscate --split-debug-info=build/app/outputs/symbols
    ```

    #### 7.3 版本管理
    ```yaml
    # pubspec.yaml版本配置
    version: 1.0.0+1
    
    # 自动化版本更新
    flutter pub global activate cider
    cider bump patch
    ```
  </process>

  <criteria>
    ## Flutter开发质量标准

    ### 技术实现标准
    - ✅ 应用启动时间 < 3秒（冷启动）
    - ✅ UI渲染维持60fps流畅度
    - ✅ 内存使用控制在合理范围（< 200MB）
    - ✅ 网络请求响应时间 < 2秒
    - ✅ 单元测试覆盖率 > 80%

    ### 用户体验标准
    - ✅ 界面响应时间 < 100ms
    - ✅ 支持主流屏幕尺寸和分辨率
    - ✅ 遵循平台设计规范（Material/Cupertino）
    - ✅ 无明显的卡顿和崩溃
    - ✅ 离线功能正常工作

    ### 代码质量标准
    - ✅ 遵循Dart语言规范和Flutter最佳实践
    - ✅ 代码结构清晰，模块化程度高
    - ✅ 注释覆盖率 > 60%
    - ✅ 无未处理的异常和内存泄漏
    - ✅ 静态分析无严重问题

    ### 平台兼容标准
    - ✅ iOS和Android功能一致性
    - ✅ 不同设备型号兼容性良好
    - ✅ 系统版本向下兼容
    - ✅ 第三方插件稳定可靠
    - ✅ 应用商店审核通过率 > 95%

    ### 维护性标准
    - ✅ 项目文档完整且最新
    - ✅ 依赖项管理规范
    - ✅ 持续集成流程建立
    - ✅ 错误监控和日志记录完善
    - ✅ 版本发布流程标准化
  </criteria>
</execution> 