# 功能配置系统API

本文档描述功能配置系统的WebSocket API请求和响应格式。

## 概述

功能配置系统是游戏辅助工具的核心模块，负责管理用户的功能预设配置。系统支持多种AI模式、热键设置、锁定位置、阵营选择、切枪功能、背闪防护等配置选项，并根据用户权限进行相应限制。每个功能配置都包含独立的阵营选择设置。

## API操作类型

系统使用两种主要操作：
- `function_read`: 读取功能配置
- `function_modify`: 修改功能配置

## API详细说明

### 读取功能配置 (function_read)

**说明**：用于从服务器读取用户的功能配置数据。客户端在页面加载、刷新或切换游戏时会发送此请求。

**触发时机**：
- 功能页面初始化加载
- 用户点击刷新按钮
- 游戏切换时自动请求
- WebSocket重连后自动请求

**客户端请求**：

```json
{
  "action": "function_read",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}
```

**服务器响应 - 成功**：

```json
{
  "action": "function_read_response",
  "status": "ok",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "configs": [
      {
        "presetName": "配置1",
        "hotkey": "右键",
        "aiMode": "FOV",
        "lockPosition": "头部",
        "selectedFaction": "警方",
        "triggerSwitch": true,
        "weaponSwitch": false,
        "flashShield": false,
        "enabled": true
      },
      {
        "presetName": "配置2",
        "hotkey": "右键",
        "aiMode": "FOVPID冲锋狙",
        "lockPosition": "随机部位",
        "selectedFaction": "匪方",
        "triggerSwitch": false,
        "weaponSwitch": true,
        "flashShield": true,
        "enabled": false
      }
    ],
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T13:30:00Z"
  }
}
```

### 修改功能配置 (function_modify)

**说明**：用于保存用户修改的功能配置到服务器。当用户点击保存按钮时会发送此请求。

**触发时机**：
- 用户点击页面保存按钮
- 卡片管理器触发保存事件

**客户端请求**：

```json
{
  "action": "function_modify",
  "content": {
    "username": "用户名",
    "gameName": "游戏名称",
    "cardKey": "卡密",
    "configs": [
      {
        "presetName": "配置1",
        "hotkey": "右键",
        "aiMode": "FOV",
        "lockPosition": "头部",
        "selectedFaction": "警方",
        "triggerSwitch": true,
        "weaponSwitch": false,
        "flashShield": false,
        "enabled": true
      },
      {
        "presetName": "配置2",
        "hotkey": "左键",
        "aiMode": "PID",
        "lockPosition": "胸部",
        "selectedFaction": "匪方",
        "triggerSwitch": false,
        "weaponSwitch": true,
        "flashShield": true,
        "enabled": true
      }
    ],
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

**服务器响应 - 成功**：

```json
{
  "action": "function_modify_response",
  "status": "ok",
  "data": {
    "username": "用户名",
    "gameName": "游戏名称",
    "configs": [
      {
        "presetName": "配置1",
        "hotkey": "右键",
        "aiMode": "FOV",
        "lockPosition": "头部",
        "selectedFaction": "警方",
        "triggerSwitch": true,
        "weaponSwitch": false,
        "flashShield": false,
        "enabled": true
      },
      {
        "presetName": "配置2",
        "hotkey": "左键",
        "aiMode": "PID",
        "lockPosition": "胸部",
        "selectedFaction": "匪方",
        "triggerSwitch": false,
        "weaponSwitch": true,
        "flashShield": true,
        "enabled": true
      }
    ],
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}
```

## 用户权限系统

### 普通用户限制
1. **AI模式限制**：只能使用 `PID` 模式，所有其他模式会被强制转换为 `PID`
2. **自动扳机限制**：无法启用自动扳机功能，`triggerSwitch` 会被强制设为 `false`
3. **切枪功能限制**：无法启用切枪功能，`weaponSwitch` 会被强制设为 `false`
4. **阵营选择限制**：无法选择阵营，`selectedFaction` 会被强制设为 `"无"`
5. **背闪防护限制**：无法启用背闪防护功能，`flashShield` 会被强制设为 `false`
6. **冲锋狙模式限制**：无法使用任何冲锋狙相关模式

### Pro用户权限
1. **完整AI模式**：可以使用所有AI模式
   - `PID`: 基础PID控制模式
   - `FOV`: 视野范围模式
   - `FOVPID`: 视野范围+PID混合模式
   - `PID冲锋狙`: PID冲锋狙击模式
   - `FOV冲锋狙`: FOV冲锋狙击模式
   - `FOVPID冲锋狙`: FOVPID冲锋狙击模式
2. **自动扳机**：可以启用/禁用自动扳机功能
3. **切枪功能**：可以启用/禁用切枪功能
4. **阵营选择**：可以选择警方、匪方或无阵营
5. **背闪防护**：可以启用/禁用背闪防护功能
6. **完整功能**：享有所有高级功能权限

### 权限检查机制
- **客户端检查**：前端在用户操作时进行实时权限验证
- **服务器强制**：服务器在接收配置时强制应用权限限制
- **数据过滤**：非Pro用户的配置会被自动过滤和修正

## 数据处理规则

### 配置字段过滤
服务器返回的配置数据中可能包含额外字段，客户端会自动过滤，只保留以下核心字段：
- `presetName`: 配置名称
- `hotkey`: 热键设置
- `aiMode`: AI模式
- `lockPosition`: 锁定位置
- `selectedFaction`: 阵营选择
- `triggerSwitch`: 自动扳机状态
- `weaponSwitch`: 切枪功能状态
- `flashShield`: 背闪防护状态
- `enabled`: 配置启用状态

### 状态字段说明
- **自动扳机状态**：`triggerSwitch` 字段表示自动扳机开关状态
  - `true`: 已启用自动扳机
  - `false`: 已禁用自动扳机
- **切枪功能状态**：`weaponSwitch` 字段表示切枪功能开关状态
  - `true`: 已启用切枪功能
  - `false`: 已禁用切枪功能
- **背闪防护状态**：`flashShield` 字段表示背闪防护开关状态
  - `true`: 已启用背闪防护
  - `false`: 已禁用背闪防护
- **配置启用状态**：`enabled` 字段表示整个配置是否启用
  - `true`: 配置已启用，可以使用
  - `false`: 配置已禁用，不会生效

## 错误处理

### 服务器错误响应格式

**读取配置失败**：
```json
{
  "action": "function_read_response",
  "status": "error",
  "message": "配置读取失败：用户不存在",
  "data": null
}
```

**修改配置失败**：
```json
{
  "action": "function_modify_response",
  "status": "error",
  "message": "配置保存失败：数据格式错误",
  "data": null
}
```

### 常见错误类型
1. **权限错误**：非Pro用户尝试使用Pro功能
2. **数据格式错误**：请求数据格式不正确
3. **网络错误**：WebSocket连接断开或超时
4. **服务器错误**：后端服务异常

## 参数详细说明

### 请求参数

| 参数名称 | 类型 | 描述 | 必填 | 备注 |
|---------|------|------|------|------|
| username | string | 用户名 | 是 | 用户登录名，任意字符串 |
| gameName | string | 游戏名称 | 是 | 支持的游戏：apex, cf, cfhd, csgo2, pubg, sjz, ssjj2, wwqy |
| cardKey | string | 用户卡密 | 是 | 用户验证卡密，任意字符串 |
| updatedAt | string | 更新时间戳 | 是 | ISO8601格式时间戳 |
| createdAt | string | 创建时间戳 | 否 | ISO8601格式，仅在modify请求中需要 |

### 配置项参数

| 参数名称 | 类型 | 描述 | 可选值 | 备注 |
|---------|------|------|-------|------|
| presetName | string | 配置预设名称 | 任意字符串 | 作为配置的唯一标识，如"配置1" |
| hotkey | string | 热键设置 | 左键、右键、中键、前侧、后侧 | 触发功能的鼠标按键 |
| aiMode | string | AI模式 | PID、FOV、FOVPID、PID冲锋狙、FOV冲锋狙、FOVPID冲锋狙 | 普通用户只能使用PID |
| lockPosition | string | 锁定位置 | 头部、颈部、胸部、随机部位 | 瞄准锁定的身体部位，随机部位会在运行时随机选择 |
| selectedFaction | string | 阵营选择 | 警方、匪方、无 | Pro用户专享功能，普通用户强制为"无" |
| triggerSwitch | boolean | 自动扳机开关 | true/false | Pro用户专享功能 |
| weaponSwitch | boolean | 切枪功能开关 | true/false | Pro用户专享功能，普通用户强制为false |
| flashShield | boolean | 背闪防护开关 | true/false | Pro用户专享功能，普通用户强制为false |
| enabled | boolean | 配置启用状态 | true/false | 控制配置是否生效 |

### 响应参数

| 参数名称 | 类型 | 描述 | 备注 |
|---------|------|------|------|
| action | string | 响应动作类型 | function_read_response 或 function_modify_response |
| status | string | 请求状态 | ok/success（成功）或 error（失败） |
| message | string | 状态描述消息 | 仅在错误时返回，描述具体错误原因 |
| data | object | 响应数据 | 包含用户信息、游戏名称、配置列表等 |
| data.configs | array | 配置列表 | 包含所有功能配置项的数组 |
| data.createdAt | string | 创建时间戳 | ISO8601格式，配置首次创建时间 |
| data.updatedAt | string | 更新时间戳 | ISO8601格式，配置最后更新时间 |

## 前端实现说明

### 页面结构
功能配置页面(`lib/views/function/function_screen.dart`)包含以下主要组件：
1. **页面头部**：标题、阵营选择下拉菜单、操作按钮
2. **卡片管理器**：配置卡片的动态管理和编辑
3. **状态管理**：使用Provider进行状态管理

### 阵营选择功能
每个功能配置都包含独立的阵营选择设置，在配置卡片中显示为下拉菜单，包含以下选项：
- **警方**：使用盾牌图标 (Icons.shield)
- **匪方**：使用人员图标 (Icons.person_outline)
- **无**：使用移除图标 (Icons.remove_circle_outline)

**实现特点**：
- 使用项目统一的IconDropdown组件
- 每个配置都有独立的阵营选择
- 状态通过CardData模型的`selectedFaction`字段管理
- 默认选择"无"
- 支持不同配置选择不同阵营

### 锁定部位功能
每个功能配置都包含锁定部位设置，用于指定瞄准时锁定的身体部位：
- **头部**：锁定头部区域，精准度最高但目标较小
- **颈部**：锁定颈部区域，平衡精准度和命中率
- **胸部**：锁定胸部区域，目标较大但伤害相对较低
- **随机部位**：系统会在运行时随机选择头部、颈部或胸部之一

**随机部位特点**：
- 增加游戏的不可预测性，避免被对手识别瞄准模式
- 每次触发时会随机选择一个具体部位进行锁定
- 随机选择范围包括头部、颈部、胸部三个基础部位
- 适合需要多样化瞄准策略的高级用户

### 控制器实现
`FunctionController`负责：
1. **数据管理**：通过CardDataManager管理配置数据
2. **权限控制**：根据用户Pro状态限制功能使用
3. **网络通信**：处理WebSocket消息收发
4. **状态更新**：通知UI进行实时更新

### 权限控制实现
```dart
// AI模式权限检查
if (property == 'aiMode' && !_loginModel.isPro && newValue != 'PID') {
  newValue = 'PID'; // 强制设为PID
}

// 阵营选择权限检查
if (property == 'selectedFaction' && !_loginModel.isPro && newValue != '无') {
  newValue = '无'; // 强制设为"无"
}

// 自动扳机权限检查  
if (property == 'triggerSwitch' && newValue && !_loginModel.isPro) {
  return; // 阻止更新
}

// 切枪功能权限检查
if (property == 'weaponSwitch' && newValue && !_loginModel.isPro) {
  return; // 阻止更新
}

// 背闪防护权限检查
if (property == 'flashShield' && newValue && !_loginModel.isPro) {
  return; // 阻止更新
}
```

## 使用示例

### 场景1：页面初始化加载配置

```javascript
// 1. 用户进入功能配置页面
// 2. 系统自动发送读取请求
{
  "action": "function_read",
  "content": {
    "username": "testuser",
    "gameName": "csgo2", 
    "cardKey": "abc123",
    "updatedAt": "2023-06-01T12:00:00Z"
  }
}

// 3. 服务器返回配置数据
// 4. 客户端自动加载并显示配置
// 5. 根据用户权限过滤和限制功能
```

### 场景2：用户修改配置并保存

```javascript
// 1. 用户在UI中修改配置
// 2. 修改配置1的自动扳机为开启状态（仅Pro用户）
// 3. 点击保存按钮后发送修改请求
{
  "action": "function_modify",
  "content": {
    "username": "testuser",
    "gameName": "csgo2",
    "cardKey": "abc123",
    "configs": [
      {
        "presetName": "配置1",
        "hotkey": "右键",
        "aiMode": "FOV",
        "lockPosition": "头部", 
        "selectedFaction": "警方",
        "triggerSwitch": true,  // Pro用户可以启用
        "enabled": true
      },
      {
        "presetName": "配置2",
        "hotkey": "左键",
        "aiMode": "PID",  // 普通用户只能使用PID
        "lockPosition": "胸部",
        "selectedFaction": "匪方",
        "triggerSwitch": false,  // 普通用户强制禁用
        "enabled": true
      }
    ],
    "createdAt": "2023-06-01T12:00:00Z",
    "updatedAt": "2023-06-01T14:00:00Z"
  }
}

// 4. 服务器返回保存结果
// 5. 客户端显示保存成功提示
```

### 场景3：阵营选择功能使用

```javascript
// 1. 用户在功能配置卡片中看到阵营选择下拉菜单
// 2. 每个配置都有独立的阵营选择设置
// 3. 用户为"配置1"选择"警方"，为"配置2"选择"匪方"
// 4. 不同配置可以设置不同的阵营，满足多样化需求
// 5. 阵营选择随配置一起保存到服务器
```

## 技术特点

### 防抖动机制
- 2秒内相同游戏的重复请求会被忽略
- 避免频繁的网络请求，提升性能

### 超时处理
- 请求超时时间：10秒
- 超时后自动重置请求状态
- 显示用户友好的错误提示

### 状态管理
- 使用Provider进行状态管理
- 支持跨组件的状态共享
- 实时UI更新机制

### 错误恢复
- 自动重连机制
- 备用配置创建
- 优雅的错误处理和用户提示

## 版本历史

### v1.5.0 (2025-07-23)
- 新增随机部位锁定功能：在锁定部位选项中添加"随机部位"选项
- 随机部位会在运行时随机选择头部、颈部或胸部之一进行锁定
- 增加游戏策略的多样性和不可预测性
- 更新API文档和数据模型说明

### v1.4.3 (2025-01-03)
- 改进AI模式和阵营选择UI：普通用户现在可以看到所有选项
- Pro功能显示为"功能名 (Pro)"格式，提升功能可见性
- 优化用户体验：让用户了解Pro版本的功能价值
- 完善切枪功能的Pro权限控制

### v1.4.0 (2025-01-03)
- 新增背闪防护开关：每个配置都可以独立控制背闪防护功能
- 在功能配置卡片中添加背闪防护开关UI组件
- 更新数据库Schema支持flashShield字段
- 完善API文档和数据模型

### v1.3.0 (2025-01-03)
- 新增切枪功能开关：每个配置都可以独立控制切枪功能
- 在功能配置卡片中添加切枪开关UI组件
- 更新数据库Schema支持weaponSwitch字段
- 完善API文档和数据模型

### v1.2.0 (2025-01-03)
- 重构阵营选择功能：从全局级别改为配置级别
- 每个功能配置都有独立的阵营选择设置
- 完善用户权限控制机制
- 优化前端状态管理和卡片组件
- 增强错误处理和用户体验

### v1.1.0
- 添加冲锋狙模式支持
- 完善Pro用户权限系统
- 优化网络请求机制

### v1.0.0
- 基础功能配置API实现
- 支持基本的读取和修改操作
