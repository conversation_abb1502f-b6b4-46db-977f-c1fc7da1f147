// ignore_for_file: prefer_final_fields

import '../models/auth_model.dart';
import '../models/game_model.dart';
import '../models/login_model.dart';
import 'app_constants.dart';
import 'logger.dart';

/// API请求构建器 - 统一WebSocket请求构建逻辑
/// 
/// 这个类提供了统一的WebSocket请求构建接口，自动添加基础字段
/// 如用户名、游戏名、时间戳等，减少重复代码并确保请求格式一致性。
class ApiRequestBuilder {
  final AuthModel _authModel;
  final GameModel _gameModel;
  final LoginModel? _loginModel;
  
  static final Logger _logger = Logger();
  static const String _logTag = AppConstants.LOG_TAG_APP;

  ApiRequestBuilder(this._authModel, this._gameModel, [this._loginModel]);

  /// ========== 基础请求构建方法 ==========

  /// 构建基础请求结构
  Map<String, dynamic> buildBaseRequest(String action, Map<String, dynamic> content) {
    final now = DateTime.now().toIso8601String();
    
    final baseContent = {
      'username': _authModel.username,
      'gameName': _gameModel.currentGame,
      'updatedAt': now,
      ...content,
    };

    final request = {
      'action': action,
      'content': baseContent,
    };

    _logger.d(_logTag, '构建基础请求', {
      'action': action,
      'username': _authModel.username,
      'gameName': _gameModel.currentGame,
    });

    return request;
  }

  /// 构建简单请求（只有action）
  Map<String, dynamic> buildSimpleRequest(String action) {
    final request = {
      'action': action,
    };

    _logger.d(_logTag, '构建简单请求', {'action': action});
    return request;
  }

  /// 构建带token的请求
  Map<String, dynamic> buildTokenRequest(String action, String token, [Map<String, dynamic>? additionalContent]) {
    final content = {
      'token': token,
      if (additionalContent != null) ...additionalContent,
    };

    final request = {
      'action': action,
      'content': content,
    };

    _logger.d(_logTag, '构建Token请求', {
      'action': action,
      'hasToken': token.isNotEmpty,
    });

    return request;
  }

  /// ========== 专用请求构建方法 ==========

  /// 构建读取请求
  Map<String, dynamic> buildReadRequest(String module) {
    final action = '${module}_read';
    return buildBaseRequest(action, {
      'createdAt': DateTime.now().toIso8601String(),
    });
  }

  /// 构建修改请求
  Map<String, dynamic> buildModifyRequest(String module, Map<String, dynamic> params) {
    final action = '${module}_modify';
    return buildBaseRequest(action, params);
  }

  /// 构建查询请求
  Map<String, dynamic> buildQueryRequest(String queryType, [Map<String, dynamic>? params]) {
    final action = '${queryType}_query';
    return buildBaseRequest(action, params ?? <String, dynamic>{});
  }

  /// ========== 认证相关请求 ==========

  /// 构建登录请求
  Map<String, dynamic> buildLoginRequest(String username, String password, String token) {
    final request = {
      'action': AppConstants.ACTION_LOGIN,
      'content': {
        'username': username,
        'password': password,
        'token': token,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      },
    };

    _logger.d(_logTag, '构建登录请求', {
      'username': username,
      'hasPassword': password.isNotEmpty,
      'hasToken': token.isNotEmpty,
    });

    return request;
  }

  /// 构建注册请求
  Map<String, dynamic> buildRegisterRequest(String username, String password, String email, [String? additionalInfo]) {
    final content = {
      'username': username,
      'password': password,
      'email': email,
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };

    if (additionalInfo != null && additionalInfo.isNotEmpty) {
      content['additionalInfo'] = additionalInfo;
    }

    final request = {
      'action': AppConstants.ACTION_REGISTER,
      'content': content,
    };

    _logger.d(_logTag, '构建注册请求', {
      'username': username,
      'email': email,
      'hasAdditionalInfo': additionalInfo != null,
    });

    return request;
  }

  /// ========== 配置相关请求 ==========

  /// 构建瞄准配置请求
  Map<String, dynamic> buildAimRequest(String actionType, [Map<String, dynamic>? aimParams]) {
    final action = actionType == 'read' ? AppConstants.ACTION_AIM_READ : AppConstants.ACTION_AIM_MODIFY;
    return buildBaseRequest(action, aimParams ?? <String, dynamic>{});
  }

  /// 构建开火配置请求
  Map<String, dynamic> buildFireRequest(String actionType, [Map<String, dynamic>? fireParams]) {
    final action = actionType == 'read' ? AppConstants.ACTION_FIRE_READ : AppConstants.ACTION_FIRE_MODIFY;
    return buildBaseRequest(action, fireParams ?? <String, dynamic>{});
  }

  /// 构建视野配置请求
  Map<String, dynamic> buildFovRequest(String actionType, [Map<String, dynamic>? fovParams]) {
    final action = actionType == 'read' ? AppConstants.ACTION_FOV_READ : AppConstants.ACTION_FOV_MODIFY;
    return buildBaseRequest(action, fovParams ?? <String, dynamic>{});
  }

  /// 构建PID配置请求
  Map<String, dynamic> buildPidRequest(String actionType, [Map<String, dynamic>? pidParams]) {
    final action = actionType == 'read' ? AppConstants.ACTION_PID_READ : AppConstants.ACTION_PID_MODIFY;
    return buildBaseRequest(action, pidParams ?? <String, dynamic>{});
  }

  /// 构建功能配置请求
  Map<String, dynamic> buildFunctionRequest(String actionType, [Map<String, dynamic>? functionData]) {
    final action = actionType == 'read' ? AppConstants.ACTION_FUNCTION_READ : AppConstants.ACTION_FUNCTION_MODIFY;

    final Map<String, dynamic> content = functionData != null ? {
      'functionConfigs': functionData,
    } : {};

    return buildBaseRequest(action, content);
  }

  /// 构建数据采集请求
  Map<String, dynamic> buildDataCollectionRequest(String actionType, [Map<String, dynamic>? dataParams]) {
    final action = actionType == 'read' ? AppConstants.ACTION_DATA_COLLECTION_READ : AppConstants.ACTION_DATA_COLLECTION_MODIFY;
    return buildBaseRequest(action, dataParams ?? <String, dynamic>{});
  }

  /// ========== 系统相关请求 ==========

  /// 构建状态栏查询请求
  Map<String, dynamic> buildStatusBarQueryRequest([String? token]) {
    if (token != null && token.isNotEmpty) {
      return buildTokenRequest(AppConstants.ACTION_STATUS_BAR_QUERY, token);
    } else {
      return buildSimpleRequest(AppConstants.ACTION_STATUS_BAR_QUERY);
    }
  }

  /// 构建游戏列表查询请求
  Map<String, dynamic> buildGameListQueryRequest() {
    return buildBaseRequest(AppConstants.ACTION_GAME_LIST_QUERY, {});
  }

  /// 构建心跳请求
  Map<String, dynamic> buildHeartbeatRequest() {
    return buildSimpleRequest('heartbeat');
  }

  /// ========== 首页相关请求 ==========

  /// 构建首页配置请求
  Map<String, dynamic> buildHomeRequest(String actionType, [Map<String, dynamic>? homeParams]) {
    final action = actionType == 'read' ? 'home_read' : 'home_modify';

    // 添加Pro状态信息
    final Map<String, dynamic> content = homeParams ?? <String, dynamic>{};
    if (_loginModel != null) {
      content['isPro'] = _loginModel!.isPro;
    }

    return buildBaseRequest(action, content);
  }

  /// 构建卡密验证请求
  Map<String, dynamic> buildCardKeyRequest(String cardKey) {
    return buildBaseRequest('card_key_verify', {
      'cardKey': cardKey,
      'isPro': _loginModel?.isPro ?? false,
    });
  }

  /// ========== 批量请求构建 ==========

  /// 构建批量配置请求
  List<Map<String, dynamic>> buildBatchConfigRequests(List<String> modules, String actionType) {
    final requests = <Map<String, dynamic>>[];
    
    for (final module in modules) {
      switch (module) {
        case 'aim':
          requests.add(buildAimRequest(actionType));
          break;
        case 'fire':
          requests.add(buildFireRequest(actionType));
          break;
        case 'fov':
          requests.add(buildFovRequest(actionType));
          break;
        case 'pid':
          requests.add(buildPidRequest(actionType));
          break;
        case 'function':
          requests.add(buildFunctionRequest(actionType));
          break;
        case 'data_collection':
          requests.add(buildDataCollectionRequest(actionType));
          break;
        default:
          _logger.w(_logTag, '未知的配置模块', {'module': module});
      }
    }

    _logger.i(_logTag, '构建批量配置请求', {
      'modules': modules,
      'actionType': actionType,
      'requestCount': requests.length,
    });

    return requests;
  }

  /// ========== 工具方法 ==========

  /// 验证请求参数
  bool validateRequest(Map<String, dynamic> request) {
    if (!request.containsKey('action') || request['action'] == null || request['action'].toString().isEmpty) {
      _logger.e(_logTag, '请求验证失败', {'reason': '缺少action字段'});
      return false;
    }

    return true;
  }

  /// 获取请求摘要（用于日志）
  Map<String, dynamic> getRequestSummary(Map<String, dynamic> request) {
    return {
      'action': request['action'],
      'hasContent': request.containsKey('content'),
      'contentKeys': request['content'] is Map ? (request['content'] as Map).keys.toList() : [],
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 添加请求ID（用于追踪）
  Map<String, dynamic> addRequestId(Map<String, dynamic> request) {
    final requestId = DateTime.now().millisecondsSinceEpoch.toString();
    request['requestId'] = requestId;
    
    _logger.d(_logTag, '添加请求ID', {
      'requestId': requestId,
      'action': request['action'],
    });
    
    return request;
  }

  /// 创建工厂方法
  static ApiRequestBuilder create(AuthModel authModel, GameModel gameModel, [LoginModel? loginModel]) {
    return ApiRequestBuilder(authModel, gameModel, loginModel);
  }
}
