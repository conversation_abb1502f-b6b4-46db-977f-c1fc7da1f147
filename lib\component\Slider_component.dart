// ignore_for_file: deprecated_member_use, use_super_parameters, unused_import, file_names, dead_code, sized_box_for_whitespace, unnecessary_import, no_leading_underscores_for_local_identifiers, unused_field

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:getwidget/getwidget.dart';
import 'dart:async';
import 'dart:math' as math;

/// 滑块配置类 - 定义单个滑块的所有参数
class SliderConfig {
  /// 滑块标题
  final String title;
  
  /// 当前值
  final double value;
  
  /// 最小值
  final double min;
  
  /// 最大值
  final double max;
  
  /// 每次滑块移动的步进值
  final double step;
  
  /// 数值变化回调
  final ValueChanged<double> onChanged;
  
  /// 滑块拖动结束回调
  final ValueChanged<double>? onChangeEnd;
  
  /// 小数位数
  final int decimalPlaces;
  
  /// 数值后缀（如：px, %, °等）
  final String? suffix;
  
  /// 是否启用输入框编辑
  final bool enableTextInput;
  
  /// 输入框宽度
  final double inputWidth;
  
  /// 滑块轨道高度
  final double trackHeight;
  
  /// 滑块主色调
  final Color? primaryColor;

  const SliderConfig({
    required this.title,
    required this.value,
    required this.min,
    required this.max,
    required this.step,
    required this.onChanged,
    this.onChangeEnd,
    this.decimalPlaces = 2,
    this.suffix,
    this.enableTextInput = true,
    this.inputWidth = 80.0,
    this.trackHeight = 2.0,
    this.primaryColor,
  });
}

/// 统一滑块创建函数 - 支持单个或多个滑块组件
/// 
/// 使用方式：
/// - 单个滑块：传入单个SliderConfig
/// - 多个滑块：传入SliderConfig列表 + 卡片标题
Widget createSlider({
  /// 滑块配置（单个滑块时使用）
  SliderConfig? config,
  
  /// 滑块配置列表（多个滑块时使用）
  List<SliderConfig>? configs,
  
  /// 卡片标题（多个滑块时使用）
  String? cardTitle,
  
  /// 卡片背景色
  Color? backgroundColor,
  
  /// 卡片阴影
  double elevation = 2.0,
}) {
  // 参数验证
  assert(
    (config != null && configs == null) || (config == null && configs != null),
    '必须提供config或configs中的一个，不能同时提供或都不提供'
  );
  
  if (configs != null) {
    assert(configs.isNotEmpty, 'configs不能为空');
  }
  
  // 单个滑块
  if (config != null) {
    return _SliderWidget(config: config);
  }
  
  // 多个滑块
  return GFCard(
    color: backgroundColor ?? Colors.white,
    elevation: elevation,
    margin: const EdgeInsets.symmetric(vertical: 2.0),
    title: cardTitle != null ? GFListTile(
      padding: const EdgeInsets.all(8.0),
      margin: EdgeInsets.zero,
      title: GFTypography(
        text: cardTitle,
        type: GFTypographyType.typo5,
        textColor: GFColors.DARK,
        showDivider: false,
      ),
    ) : null,
    content: Column(
      children: configs!.map((sliderConfig) => _SliderWidget(config: sliderConfig)).toList(),
    ),
  );
}

/// 内部滑块组件实现
class _SliderWidget extends StatefulWidget {
  final SliderConfig config;

  const _SliderWidget({required this.config});

  @override
  State<_SliderWidget> createState() => _SliderWidgetState();
}

class _SliderWidgetState extends State<_SliderWidget> {
  late TextEditingController _textController;
  late FocusNode _focusNode;
  late double _currentValue;
  bool _isSliderActive = false;
  bool _isTextEditing = false;
  
  @override
  void initState() {
    super.initState();
    _currentValue = _clampValue(widget.config.value);
    _textController = TextEditingController(text: _formatValue(_currentValue));
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void didUpdateWidget(_SliderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.config.value != widget.config.value && !_isTextEditing) {
      _currentValue = _clampValue(widget.config.value);
      _textController.text = _formatValue(_currentValue);
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 约束数值在有效范围内
  double _clampValue(double value) {
    return math.max(widget.config.min, math.min(widget.config.max, value));
  }

  /// 格式化数值显示
  String _formatValue(double value) {
    String formatted = value.toStringAsFixed(widget.config.decimalPlaces);
    if (widget.config.suffix != null && widget.config.suffix!.isNotEmpty) {
      formatted += ' ${widget.config.suffix}';
    }
    return formatted;
  }

  /// 解析输入文本为数值
  double? _parseValue(String text) {
    String cleanText = text;
    if (widget.config.suffix != null && widget.config.suffix!.isNotEmpty) {
      cleanText = text.replaceAll(widget.config.suffix!, '').trim();
    }
    return double.tryParse(cleanText);
  }

  /// 焦点变化处理
  void _onFocusChanged() {
    if (!_focusNode.hasFocus && _isTextEditing) {
      _handleTextSubmit();
    }
  }

  /// 处理文本输入提交
  void _handleTextSubmit() {
    _isTextEditing = false;
    final inputValue = _parseValue(_textController.text);
    if (inputValue != null) {
      final newValue = _clampValue(inputValue);
      if (newValue != _currentValue) {
        _updateValue(newValue, true);
      }
    } else {
      // 输入无效，恢复原值
      _textController.text = _formatValue(_currentValue);
    }
  }

  /// 更新数值
  void _updateValue(double newValue, bool isFromInput) {
    setState(() {
      _currentValue = newValue;
      if (!_isTextEditing) {
        _textController.text = _formatValue(newValue);
      }
    });
    
    widget.config.onChanged(newValue);
    
    if (isFromInput) {
      widget.config.onChangeEnd?.call(newValue);
    }
  }

  /// 滑块值变化
  void _onSliderChanged(double value) {
    if (_isTextEditing) return;
    
    _isSliderActive = true;
    final steps = ((value - widget.config.min) / widget.config.step).round();
    final newValue = widget.config.min + (steps * widget.config.step);
    final clampedValue = _clampValue(newValue);
    
    _updateValue(clampedValue, false);
  }

  /// 滑块拖动结束
  void _onSliderChangeEnd(double value) {
    _isSliderActive = false;
    widget.config.onChangeEnd?.call(_currentValue);
  }

  /// 按钮调节数值
  void _adjustValue(bool increase) {
    if (_isTextEditing) return;
    
    final adjustment = increase ? widget.config.step : -widget.config.step;
    final newValue = _clampValue(_currentValue + adjustment);
    _updateValue(newValue, true);
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = widget.config.primaryColor ?? theme.primaryColor;
    
    return GFCard(
      color: Colors.white,
      elevation: 1.0,
      margin: const EdgeInsets.symmetric(vertical: 1.0),
      content: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
            _buildHeader(primaryColor),
            const SizedBox(height: 8.0),
            _buildSliderSection(primaryColor),
          ],
        ),
      ),
    );
  }
  
  /// 构建头部区域（标题 + 控制按钮 + 输入框）
  Widget _buildHeader(Color primaryColor) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 检测可用宽度，判断是否为移动端
        final availableWidth = constraints.maxWidth;
        final isMobile = availableWidth < 400; // 400px作为移动端断点
        final isSmallMobile = availableWidth < 320; // 320px作为小屏移动端断点
        
        // 根据屏幕尺寸动态调整输入框宽度
        double inputWidth;
        if (isSmallMobile) {
          inputWidth = 50.0; // 小屏手机：进一步减小到50px
        } else if (isMobile) {
          inputWidth = 60.0; // 普通手机：减小到60px
        } else {
          inputWidth = widget.config.inputWidth; // 桌面：原始宽度
        }
        
        // 动态调整flex比例
        int titleFlex, controlFlex;
        if (isSmallMobile) {
          titleFlex = 1; // 标题占更少空间
          controlFlex = 2; // 控制区域占更多空间
        } else if (isMobile) {
          titleFlex = 3; // 移动端平衡布局
          controlFlex = 4;
        } else {
          titleFlex = 2; // 桌面端原始比例
          controlFlex = 3;
        }
        
    return Row(
      children: [
        // 标题
        Expanded(
              flex: titleFlex,
          child: GFTypography(
            text: widget.config.title,
            type: GFTypographyType.typo5,
            textColor: GFColors.DARK,
            showDivider: false,
          ),
        ),
        
        // 控制区域
        Expanded(
              flex: controlFlex,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.min, // 防止过度扩展
      children: [
              // 减少按钮
              _StepButton(
          icon: Icons.remove,
                color: primaryColor,
                onPressed: () => _adjustValue(false),
                enabled: _currentValue > widget.config.min,
                    size: isMobile ? 22.0 : 26.0, // 进一步减小移动端按钮尺寸
              ),
              
                  SizedBox(width: isMobile ? 3.0 : 5.0), // 进一步减小间距
              
              // 数值输入框
                  _buildInputField(primaryColor, inputWidth),
              
                  SizedBox(width: isMobile ? 3.0 : 5.0), // 进一步减小间距
              
              // 增加按钮
              _StepButton(
          icon: Icons.add,
                color: primaryColor,
                onPressed: () => _adjustValue(true),
                enabled: _currentValue < widget.config.max,
                    size: isMobile ? 22.0 : 26.0, // 进一步减小移动端按钮尺寸
              ),
            ],
          ),
        ),
      ],
        );
      },
    );
  }
  
  /// 构建数值输入框
  Widget _buildInputField(Color primaryColor, double inputWidth) {
    return Container(
      width: inputWidth,
      height: 28.0,
      decoration: BoxDecoration(
        border: Border.all(color: GFColors.LIGHT),
        borderRadius: BorderRadius.circular(4.0),
        color: widget.config.enableTextInput ? Colors.white : GFColors.LIGHT.withOpacity(0.3),
      ),
      child: widget.config.enableTextInput ? TextField(
        controller: _textController,
        focusNode: _focusNode,
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.center, // 添加垂直居中
        style: TextStyle(
          fontSize: 12.0,
          fontWeight: FontWeight.w600,
          color: primaryColor,
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero, // 移除内边距，让文字完全居中
          isDense: true, // 使用紧凑模式
        ),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.-]')),
        ],
        onChanged: (_) => _isTextEditing = true,
        onSubmitted: (_) => _handleTextSubmit(),
      ) : Center(
        child: Text(
          _formatValue(_currentValue),
          style: TextStyle(
            fontSize: 12.0,
            fontWeight: FontWeight.w600,
            color: primaryColor,
          ),
        ),
      ),
    );
  }
  
  /// 构建滑块区域
  Widget _buildSliderSection(Color primaryColor) {
    return Column(
      children: [
        // 范围标签
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.config.min.toStringAsFixed(widget.config.decimalPlaces),
              style: const TextStyle(
                fontSize: 10.0,
                color: GFColors.SECONDARY,
              ),
            ),
            Text(
              widget.config.max.toStringAsFixed(widget.config.decimalPlaces),
              style: const TextStyle(
                fontSize: 10.0,
                color: GFColors.SECONDARY,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 4.0),
        
        // 滑块
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: widget.config.trackHeight,
            activeTrackColor: primaryColor,
            inactiveTrackColor: GFColors.LIGHT,
            thumbColor: primaryColor,
            overlayColor: primaryColor.withOpacity(0.2),
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6.0),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 10.0),
          ),
        child: Slider(
            value: _currentValue,
            min: widget.config.min,
            max: widget.config.max,
            divisions: ((widget.config.max - widget.config.min) / widget.config.step).round(),
          onChanged: _onSliderChanged,
          onChangeEnd: _onSliderChangeEnd,
        ),
      ),
      ],
    );
  }
}

/// 步进按钮组件
class _StepButton extends StatefulWidget {
  final IconData icon;
  final Color color;
  final VoidCallback onPressed;
  final bool enabled;
  final double size;

  const _StepButton({
    required this.icon,
    required this.color,
    required this.onPressed,
    this.enabled = true,
    this.size = 28.0,
  });

  @override
  State<_StepButton> createState() => _StepButtonState();
}

class _StepButtonState extends State<_StepButton> {
  Timer? _timer;
  bool _isLongPressing = false;
  static const int _longPressDelay = 150;
  static const int _repeatDelay = 50;
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  
  void _startLongPress() {
    if (!widget.enabled) return;
    
    _isLongPressing = true;
    widget.onPressed(); // 立即执行一次
    
    _timer = Timer.periodic(
      const Duration(milliseconds: _longPressDelay),
      (timer) {
        if (!_isLongPressing) {
          timer.cancel();
          return;
        }
        
        widget.onPressed();
        
        // 加速重复
        timer.cancel();
        _timer = Timer.periodic(
          const Duration(milliseconds: _repeatDelay),
          (fastTimer) {
        if (!_isLongPressing) {
              fastTimer.cancel();
          return;
        }
        widget.onPressed();
          },
        );
      },
    );
  }
  
  void _stopLongPress() {
    _isLongPressing = false;
    _timer?.cancel();
    _timer = null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.enabled ? widget.onPressed : null,
      onLongPressStart: (_) => _startLongPress(),
      onLongPressEnd: (_) => _stopLongPress(),
      onLongPressCancel: _stopLongPress,
      child: Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          color: widget.enabled ? widget.color : GFColors.LIGHT,
          borderRadius: BorderRadius.circular(6.0),
          boxShadow: widget.enabled ? [
            BoxShadow(
              color: widget.color.withOpacity(0.3),
              blurRadius: 4.0,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Icon(
          widget.icon,
          size: widget.size * 0.6, // 图标尺寸为按钮尺寸的60%，确保比例协调
          color: widget.enabled ? Colors.white : GFColors.SECONDARY,
        ),
      ),
    );
  }
}

// =============================================================================
// 使用示例和API说明
// =============================================================================

/*
统一API使用示例：

1. 创建单个滑块：
```dart
createSlider(
  config: SliderConfig(
    title: '移动速度',
    value: controller.moveSpeed,
    min: 0.0,
    max: 10.0,
    step: 0.1,
    onChanged: (value) => controller.moveSpeed = value,
    onChangeEnd: (value) => controller.savePidConfig(),
    decimalPlaces: 1,
    suffix: 'm/s',
    enableTextInput: true,
  ),
)
```

2. 创建多个滑块卡片：
```dart
createSlider(
  cardTitle: '移动控制参数',
  configs: [
    SliderConfig(
      title: '近端移动速度',
      value: controller.nearMoveFactor,
      min: 0.0,
      max: 2.0,
      step: 0.01,
      onChanged: (value) => controller.nearMoveFactor = value,
      onChangeEnd: (value) => controller.handleSliderDragEnd(context),
      decimalPlaces: 2,
    ),
    SliderConfig(
      title: '近端跟踪速度',
      value: controller.nearStabilizer,
      min: 0.0,
      max: 5.0,
      step: 0.1,
      onChanged: (value) => controller.nearStabilizer = value,
      onChangeEnd: (value) => controller.handleSliderDragEnd(context),
      decimalPlaces: 1,
    ),
  ],
)
```

3. PID参数实际使用：
```dart
createSlider(
  cardTitle: '精度控制参数',
  configs: [
    SliderConfig(
      title: '近端死区大小',
      value: controller.nearAssistZone,
      min: 0.0,
      max: 10.0,
      step: 0.5,
      onChanged: (value) {
        controller.nearAssistZone = value;
        controller.handleButtonOperation();
      },
      onChangeEnd: (value) => controller.handleSliderDragEnd(context),
      decimalPlaces: 1,
      suffix: 'px',
    ),
    SliderConfig(
      title: '近端回弹速度',
      value: controller.nearResponseDelay,
      min: 0.0,
      max: 2.0,
      step: 0.1,
      onChanged: (value) {
        controller.nearResponseDelay = value;
        controller.handleButtonOperation();
      },
      onChangeEnd: (value) => controller.handleSliderDragEnd(context),
      decimalPlaces: 1,
    ),
  ],
)
```

关键特性：
- 统一的createSlider函数处理单个和多个滑块
- SliderConfig配置类包含所有必需参数
- 支持自定义标题、范围、步进值
- 支持可编辑输入框
- 长按加减按钮支持加速
- 基于GetWidget UI的现代化设计
*/
