// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import '../component/message_component.dart';
import 'logger.dart';
import 'app_constants.dart';

/// 统一错误处理器 - 标准化错误处理和用户反馈
/// 
/// 这个类提供了统一的错误处理机制，包括错误日志记录、用户友好的
/// 提示消息、异步操作包装等功能，确保整个应用的错误处理一致性。
class ErrorHandler {
  static final Logger _logger = Logger();

  /// ========== 基础错误处理方法 ==========

  /// 处理一般错误
  static void handleError({
    required String tag,
    required String operation,
    required dynamic error,
    BuildContext? context,
    String? userMessage,
    bool showToast = true,
    StackTrace? stackTrace,
  }) {
    // 记录详细错误日志
    _logger.e(tag, '$operation失败', {
      'error': error.toString(),
      'userMessage': userMessage,
      'hasContext': context != null,
    }, stackTrace);

    // 显示用户友好的错误提示
    if (showToast && context != null) {
      final message = userMessage ?? _getDefaultErrorMessage(operation);
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.error,
        duration: AppConstants.TOAST_DURATION,
      );
    }
  }

  /// 处理网络错误
  static void handleNetworkError({
    required String tag,
    required String operation,
    required dynamic error,
    BuildContext? context,
    bool showToast = true,
    String? customMessage,
  }) {
    _logger.e(tag, '网络请求失败', {
      'operation': operation,
      'error': error.toString(),
    });

    if (showToast && context != null) {
      final message = customMessage ?? AppConstants.ERROR_NETWORK;
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.error,
        duration: AppConstants.TOAST_DURATION,
      );
    }
  }

  /// 处理WebSocket错误
  static void handleWebSocketError({
    required String tag,
    required String operation,
    required dynamic error,
    BuildContext? context,
    bool showToast = true,
    String? customMessage,
  }) {
    _logger.e(tag, 'WebSocket错误', {
      'operation': operation,
      'error': error.toString(),
    });

    if (showToast && context != null) {
      final message = customMessage ?? AppConstants.ERROR_SERVER;
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.error,
        duration: AppConstants.TOAST_DURATION,
      );
    }
  }

  /// 处理数据解析错误
  static void handleParseError({
    required String tag,
    required String operation,
    required dynamic error,
    required String rawData,
    BuildContext? context,
    bool showToast = true,
    String? customMessage,
  }) {
    _logger.e(tag, '数据解析失败', {
      'operation': operation,
      'error': error.toString(),
      'rawData': rawData.length > 200 ? '${rawData.substring(0, 200)}...' : rawData,
    });

    if (showToast && context != null) {
      final message = customMessage ?? AppConstants.ERROR_PARSE;
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.error,
        duration: AppConstants.TOAST_DURATION,
      );
    }
  }

  /// 处理验证错误
  static void handleValidationError({
    required String tag,
    required String field,
    required String message,
    BuildContext? context,
    bool showToast = true,
  }) {
    _logger.w(tag, '数据验证失败', {
      'field': field,
      'message': message,
    });

    if (showToast && context != null) {
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.warning,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// 处理超时错误
  static void handleTimeoutError({
    required String tag,
    required String operation,
    BuildContext? context,
    bool showToast = true,
    String? customMessage,
  }) {
    _logger.w(tag, '操作超时', {
      'operation': operation,
    });

    if (showToast && context != null) {
      final message = customMessage ?? AppConstants.ERROR_TIMEOUT;
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.warning,
        duration: AppConstants.TOAST_DURATION,
      );
    }
  }

  /// 处理认证错误
  static void handleAuthError({
    required String tag,
    required String operation,
    BuildContext? context,
    bool showToast = true,
    String? customMessage,
  }) {
    _logger.w(tag, '认证失败', {
      'operation': operation,
    });

    if (showToast && context != null) {
      final message = customMessage ?? AppConstants.ERROR_UNAUTHORIZED;
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.error,
        duration: AppConstants.TOAST_DURATION,
      );
    }
  }

  /// ========== 成功消息处理 ==========

  /// 显示成功消息
  static void showSuccess({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 2),
    String? tag,
    String? operation,
  }) {
    if (tag != null && operation != null) {
      _logger.i(tag, '$operation成功', {'message': message});
    }
    
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.success,
      duration: duration,
    );
  }

  /// 显示信息提示
  static void showInfo({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 2),
    String? tag,
  }) {
    if (tag != null) {
      _logger.i(tag, '信息提示', {'message': message});
    }
    
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.info,
      duration: duration,
    );
  }

  /// 显示警告消息
  static void showWarning({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 2),
    String? tag,
  }) {
    if (tag != null) {
      _logger.w(tag, '警告提示', {'message': message});
    }
    
    MessageComponent.showIconToast(
      context: context,
      message: message,
      type: MessageType.warning,
      duration: duration,
    );
  }

  /// ========== 异步操作包装器 ==========

  /// 包装异步操作的错误处理
  static Future<T?> wrapAsyncOperation<T>({
    required String tag,
    required String operation,
    required Future<T> Function() asyncOperation,
    BuildContext? context,
    bool showToast = true,
    String? successMessage,
    String? errorMessage,
  }) async {
    try {
      final result = await asyncOperation();
      
      // 显示成功消息
      if (successMessage != null && context != null) {
        showSuccess(
          context: context, 
          message: successMessage,
          tag: tag,
          operation: operation,
        );
      }
      
      return result;
    } catch (error, stackTrace) {
      handleError(
        tag: tag,
        operation: operation,
        error: error,
        context: context,
        showToast: showToast,
        userMessage: errorMessage,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// 包装网络请求操作
  static Future<T?> wrapNetworkOperation<T>({
    required String tag,
    required String operation,
    required Future<T> Function() networkOperation,
    BuildContext? context,
    bool showToast = true,
    String? successMessage,
    String? errorMessage,
  }) async {
    try {
      final result = await networkOperation();
      
      if (successMessage != null && context != null) {
        showSuccess(
          context: context, 
          message: successMessage,
          tag: tag,
          operation: operation,
        );
      }
      
      return result;
    } catch (error, stackTrace) {
      handleNetworkError(
        tag: tag,
        operation: operation,
        error: error,
        context: context,
        showToast: showToast,
        customMessage: errorMessage,
      );
      return null;
    }
  }

  /// 包装WebSocket操作
  static Future<T?> wrapWebSocketOperation<T>({
    required String tag,
    required String operation,
    required Future<T> Function() webSocketOperation,
    BuildContext? context,
    bool showToast = true,
    String? successMessage,
    String? errorMessage,
  }) async {
    try {
      final result = await webSocketOperation();
      
      if (successMessage != null && context != null) {
        showSuccess(
          context: context, 
          message: successMessage,
          tag: tag,
          operation: operation,
        );
      }
      
      return result;
    } catch (error, stackTrace) {
      handleWebSocketError(
        tag: tag,
        operation: operation,
        error: error,
        context: context,
        showToast: showToast,
        customMessage: errorMessage,
      );
      return null;
    }
  }

  /// ========== 私有辅助方法 ==========

  /// 获取默认错误消息
  static String _getDefaultErrorMessage(String operation) {
    switch (operation) {
      case '登录':
        return '登录失败，请检查用户名和密码';
      case '注册':
        return '注册失败，请稍后重试';
      case '保存配置':
        return '保存配置失败，请重试';
      case '加载配置':
        return '加载配置失败，请重试';
      case '连接服务器':
        return AppConstants.ERROR_CONNECTION_FAILED;
      case '发送请求':
        return '发送请求失败，请重试';
      case '数据同步':
        return '数据同步失败，请重试';
      case '参数更新':
        return '参数更新失败，请重试';
      default:
        return '$operation失败，请重试';
    }
  }

  /// ========== 错误分类和统计 ==========

  /// 错误统计信息
  static final Map<String, int> _errorStats = {};

  /// 记录错误统计
  static void _recordErrorStats(String tag, String operation) {
    final key = '${tag}_$operation';
    _errorStats[key] = (_errorStats[key] ?? 0) + 1;
  }

  /// 获取错误统计
  static Map<String, int> getErrorStats() {
    return Map.from(_errorStats);
  }

  /// 清除错误统计
  static void clearErrorStats() {
    _errorStats.clear();
  }

  /// 获取最常见的错误
  static List<MapEntry<String, int>> getTopErrors({int limit = 10}) {
    final entries = _errorStats.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(limit).toList();
  }
}
