// ignore_for_file: avoid_print, unnecessary_brace_in_string_interps, unused_local_variable

import 'dart:convert';
import 'package:flutter/material.dart';
import '../views/function/card_manager_component.dart';
import '../utils/logger.dart';
import '../models/function_model.dart';
import '../services/server_service.dart';
import '../models/game_model.dart';
import '../models/auth_model.dart';
import '../models/login_model.dart';
import '../component/message_component.dart';

/// 事件处理器接口，用于定义卡片管理相关的事件处理方法
abstract class CardEventHandler {
  void onCardPropertyChanged(CardData card, String property, String newValue);
  void onCardBoolPropertyChanged(CardData card, String property, bool newValue);
  void onCardAdded(String presetName);
  void onGetAllCardsInfo();
  void onCardUpdated(CardData card);
}

/// 数据管理类，负责卡片数据的增删改查
class CardDataManager {
  // 卡片列表
  final List<CardData> cards = [];
  
  // 用于通知UI更新的回调
  final Function(VoidCallback) updateState;
  
  // FunctionModel
  final FunctionModel functionModel;
  
  // LoginModel
  final LoginModel? loginModel;
  
  CardDataManager({
    required this.updateState, 
    required this.functionModel, 
    this.loginModel
  });
  
  /// 从模型数据初始化默认卡片
  void initDefaultCards() {
    cards.clear();
    final configList = functionModel.configs;
    final bool isPro = loginModel?.isPro ?? false;
    
    // 添加调试日志
    log.i('CardDataManager', '初始化默认卡片，配置数量: ${configList.length}');
    log.i('CardDataManager', 'Pro状态: $isPro');
    
    for (int index = 0; index < configList.length; index++) {
      final config = configList[index];
      log.i('CardDataManager', '处理配置${index + 1}: ${jsonEncode(config)}');
      
      String aiMode = config['aiMode'] as String;
      bool triggerSwitch = config['triggerSwitch'] as bool;
      String hotkey = config['hotkey'] as String;
      
      log.i('CardDataManager', '原始数据 - presetName: ${config['presetName']}, hotkey: $hotkey, aiMode: $aiMode, triggerSwitch: $triggerSwitch');
      
      // 如果不是Pro用户且AI模式不是PID，则强制设置为PID（包括所有冲锋狙模式）
      if (!isPro && aiMode != 'PID') {
        log.i('CardDataManager', '非Pro用户的AI模式已强制改为PID: ${config['presetName']} (原模式: $aiMode)');
        aiMode = 'PID';
      }
      
      // 如果不是Pro用户且自动扳机为开启状态，则强制关闭
      if (!isPro && triggerSwitch) {
        triggerSwitch = false;
        log.i('CardDataManager', '非Pro用户的自动扳机已强制关闭: ${config['presetName']}');
      }
      
      // 处理阵营选择权限：非Pro用户强制设置为"无"
      String selectedFaction = config['selectedFaction'] as String? ?? '无';
      if (!isPro && selectedFaction != '无') {
        selectedFaction = '无';
        log.i('CardDataManager', '非Pro用户的阵营选择已强制设为"无": ${config['presetName']} (原阵营: ${config['selectedFaction']})');
      }
      
      // 处理切枪功能权限：非Pro用户强制关闭
      bool weaponSwitch = config['weaponSwitch'] as bool? ?? false;
      if (!isPro && weaponSwitch) {
        weaponSwitch = false;
        log.i('CardDataManager', '非Pro用户的切枪功能已强制关闭: ${config['presetName']}');
      }
      
      // 处理背闪防护权限：非Pro用户强制关闭
      bool flashShield = config['flashShield'] as bool? ?? false;
      if (!isPro && flashShield) {
        flashShield = false;
        log.i('CardDataManager', '非Pro用户的背闪防护已强制关闭: ${config['presetName']}');
      }
      
      // 添加调试日志
      log.i('CardDataManager', '创建卡片: ${config['presetName']} - hotkey: $hotkey, aiMode: $aiMode, triggerSwitch: $triggerSwitch, enabled: ${config['enabled']}');
      
      final card = CardData(
        presetName: config['presetName'] as String,
        hotkey: hotkey,
        aiMode: aiMode,
        lockPosition: config['lockPosition'] as String,
        selectedFaction: selectedFaction,  // 使用处理过的阵营选择
        triggerSwitch: triggerSwitch,
        weaponSwitch: weaponSwitch,  // 使用处理过的切枪功能状态
        flashShield: flashShield,    // 使用处理过的背闪防护状态
        enabled: config['enabled'] as bool,
      );
      
      cards.add(card);
      log.i('CardDataManager', '卡片${index + 1}已添加到列表: ${card.presetName} - hotkey: ${card.hotkey}');
    }
    
    log.i('CardDataManager', '所有卡片初始化完成，总数量: ${cards.length}');
  }
  
  /// 添加新卡片
  CardData addCard(String presetName) {
    final bool isPro = loginModel?.isPro ?? false;
    final String defaultAiMode = isPro ? 
        (functionModel.aiModes.isNotEmpty ? functionModel.aiModes[0] : 'PID') : 
        'PID';
    
    final newCard = CardData(
      presetName: presetName,
      hotkey: functionModel.hotkeys.isNotEmpty ? functionModel.hotkeys[0] : '',
      aiMode: defaultAiMode,
      lockPosition: functionModel.lockPositions.isNotEmpty ? functionModel.lockPositions[0] : '',
      selectedFaction: '无',
      triggerSwitch: false,
      weaponSwitch: false,  // 新增：切枪开关默认关闭
      flashShield: false,   // 新增：背闪开关默认关闭
      enabled: true,
    );
    
    updateState(() {
      cards.add(newCard);
      functionModel.addConfig(
        presetName: presetName,
        hotkey: newCard.hotkey,
        aiMode: newCard.aiMode,
        lockPosition: newCard.lockPosition,
        selectedFaction: newCard.selectedFaction,
        triggerSwitch: newCard.triggerSwitch,
        weaponSwitch: newCard.weaponSwitch,  // 新增：切枪开关
        flashShield: newCard.flashShield,    // 新增：背闪开关
        enabled: newCard.enabled,
      );
    });
    
    return newCard;
  }
  
  /// 更新卡片属性
  void updateCardProperty(CardData card, String property, String newValue) {
    updateState(() {
      switch (property) {
        case 'presetName':
          card.presetName = newValue;
          break;
        case 'hotkey':
          card.hotkey = newValue;
          break;
        case 'aiMode':
          card.aiMode = newValue;
          break;
        case 'lockPosition':
          card.lockPosition = newValue;
          break;
        case 'selectedFaction':
          card.selectedFaction = newValue;
          break;
        default:
          return;
      }

      functionModel.updateConfig(card.presetName, property, newValue);
    });
  }
  
  /// 更新卡片布尔属性
  void updateCardBoolProperty(CardData card, String property, bool newValue) {
    updateState(() {
      switch (property) {
        case 'enabled':
          card.enabled = newValue;
          break;
        case 'triggerSwitch':
          // 检查Pro权限：非Pro用户不能启用自动扳机
          final bool isPro = loginModel?.isPro ?? false;
          if (newValue && !isPro) {
            log.w('CardDataManager', '非Pro用户尝试启用自动扳机，操作被拒绝');
            return; // 阻止更新
          }
          card.triggerSwitch = newValue;
          break;
        case 'weaponSwitch':  // 新增：切枪开关处理
          // 检查Pro权限：非Pro用户不能启用切枪功能
          final bool isPro = loginModel?.isPro ?? false;
          if (newValue && !isPro) {
            log.w('CardDataManager', '非Pro用户尝试启用切枪功能，操作被拒绝');
            return; // 阻止更新
          }
          card.weaponSwitch = newValue;
          break;
        case 'flashShield':   // 新增：背闪开关处理
          // 检查Pro权限：非Pro用户不能启用背闪防护
          final bool isPro = loginModel?.isPro ?? false;
          if (newValue && !isPro) {
            log.w('CardDataManager', '非Pro用户尝试启用背闪防护，操作被拒绝');
            return; // 阻止更新
          }
          card.flashShield = newValue;
          break;
        default:
          return;
      }

      // 添加调试日志
      log.i('CardDataManager', '更新卡片布尔属性: ${card.presetName} - $property = $newValue');
      functionModel.updateConfig(card.presetName, property, newValue);
    });
  }
  
  /// 获取所有卡片信息并转换为JSON
  String getAllCardsInfo() => functionModel.toJson();
}

/// 事件处理器实现，处理卡片相关的所有事件
class CardEventHandlerImpl implements CardEventHandler {
  final CardDataManager dataManager;
  final FunctionModel functionModel;
  final ServerService? serverService;
  final GameModel? gameModel;
  final AuthModel? authModel;
  
  final log = Logger();
  final String _logTag = 'CardEvent';
  
  CardEventHandlerImpl({
    required this.dataManager, 
    required this.functionModel,
    this.serverService,
    this.gameModel,
    this.authModel,
  });
  
  @override
  void onCardPropertyChanged(CardData card, String property, String newValue) {
    dataManager.updateCardProperty(card, property, newValue);
    log.i(_logTag, '卡片属性已更新: ${card.presetName} - $property = $newValue');
  }
  
  @override
  void onCardBoolPropertyChanged(CardData card, String property, bool newValue) {
    dataManager.updateCardBoolProperty(card, property, newValue);
    log.i(_logTag, '卡片布尔属性已更新: ${card.presetName} - $property = $newValue');
  }
  
  @override
  void onCardAdded(String presetName) {
    dataManager.addCard(presetName);
    log.i(_logTag, '已添加新卡片: $presetName');
  }
  
  @override
  void onGetAllCardsInfo() {
    log.i(_logTag, '准备发送功能配置');
    _sendFunctionConfigToServer();
  }
  
  /// 向后端发送功能设置配置信息
  void _sendFunctionConfigToServer() {
    if (serverService == null || !serverService!.isConnected) {
      log.e(_logTag, '无法发送配置到服务器：WebSocket未连接');
      return;
    }

    if (gameModel == null || authModel == null) {
      log.e(_logTag, '无法发送配置到服务器：模型未初始化');
      return;
    }

    try {
      // 安全地获取值，使用空值合并操作符
      final String gameName = gameModel?.currentGame ?? '';
      final String cardKey = gameModel?.cardKey ?? '';
      final String username = authModel?.username ?? '';

      if (gameName.isEmpty || username.isEmpty) {
        log.e(_logTag, '无法发送配置到服务器：游戏名称或用户名为空');
        return;
      }
      
      // 添加调试日志，打印配置数据
      log.i(_logTag, '准备发送的配置数据:');
      for (int i = 0; i < functionModel.configs.length; i++) {
        final config = functionModel.configs[i];
        log.i(_logTag, '配置${i + 1}: ${config['presetName']} - triggerSwitch: ${config['triggerSwitch']}, enabled: ${config['enabled']}');
      }
      
      final payload = {
        'action': 'function_modify',
        'content': {
          'username': username,
          'gameName': gameName,
          'cardKey': cardKey,
          'configs': functionModel.configs,
          'createdAt': functionModel.createdAt,
          'updatedAt': functionModel.updatedAt
        }
      };
      
      serverService!.sendMessage(jsonEncode(payload));
      log.i(_logTag, '已发送功能配置到服务器：$gameName');
    } catch (e) {
      log.e(_logTag, '发送配置到服务器时出错：$e');
    }
  }
  
  @override
  void onCardUpdated(CardData card) {
    log.i(_logTag, '卡片已更新: ${card.presetName}');
  }
}

/// 功能控制器 - 负责连接UI和数据模型
class FunctionController extends ChangeNotifier {
  late CardDataManager _dataManager;
  late CardEventHandler _eventHandler;
  final FunctionModel _functionModel;
  final ServerService? _serverService;
  final GameModel? _gameModel;
  final AuthModel? _authModel;
  final LoginModel? _loginModel;
  
  final log = Logger();
  bool _isDisposed = false;
  final String _logTag = 'FunctionController';
  
  // 添加context管理
  BuildContext? _currentContext;
  
  // 添加加载提示管理
  VoidCallback? _currentLoadingDismiss;
  
  List<CardData> get cards => _dataManager.cards;
  
  // 防抖动控制
  DateTime _lastRequestTime = DateTime.now().subtract(const Duration(minutes: 1));
  String _lastRequestedGame = '';
  
  // 请求状态
  bool _isRequesting = false;
  
  FunctionController({
    required FunctionModel functionModel,
    ServerService? serverService,
    GameModel? gameModel,
    AuthModel? authModel,
    LoginModel? loginModel,
  }) : _functionModel = functionModel,
       _serverService = serverService,
       _gameModel = gameModel,
       _authModel = authModel,
       _loginModel = loginModel {
    _initController();
  }
  
  /// 设置当前上下文
  void setContext(BuildContext? context) {
    _currentContext = context;
  }
  
  /// 获取当前可用的BuildContext
  BuildContext? _getCurrentContext() {
    // 检查context是否仍然有效
    if (_currentContext != null && _currentContext!.mounted) {
      return _currentContext;
    }
    return null;
  }
  
  /// 显示持续的加载提示
  void _showPersistentLoadingMessage(String message) {
    final context = _getCurrentContext();
    if (context != null) {
      // 先关闭之前的提示
      _dismissCurrentLoadingMessage();
      
      // 显示新的持续提示
      _currentLoadingDismiss = MessageComponent.showPersistentLoading(
        context: context,
        message: message,
        position: MessagePosition.center,
      );
      
      log.i(_logTag, '显示持续加载提示: $message');
    }
  }
  
  /// 关闭当前的加载提示
  void _dismissCurrentLoadingMessage() {
    if (_currentLoadingDismiss != null) {
      _currentLoadingDismiss!();
      _currentLoadingDismiss = null;
      log.i(_logTag, '关闭当前加载提示');
    }
  }
  
  /// 显示成功消息并关闭加载提示
  void _showSuccessAndDismissLoading(String message) {
    _dismissCurrentLoadingMessage();
    
    final context = _getCurrentContext();
    if (context != null) {
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.success,
        duration: const Duration(seconds: 2),
      );
      log.i(_logTag, '显示成功消息: $message');
    }
  }
  
  /// 显示错误消息并关闭加载提示
  void _showErrorAndDismissLoading(String message) {
    _dismissCurrentLoadingMessage();
    
    final context = _getCurrentContext();
    if (context != null) {
      MessageComponent.showIconToast(
        context: context,
        message: message,
        type: MessageType.error,
        duration: const Duration(seconds: 3),
      );
      log.i(_logTag, '显示错误消息: $message');
    }
  }
  
  /// 初始化控制器
  void _initController() {
    log.i(_logTag, '开始初始化FunctionController');
    
    _dataManager = CardDataManager(
      updateState: (callback) {
        if (!_isDisposed) {
          callback();
          // 延迟通知监听器，避免在构建期间调用
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (!_isDisposed) {
              notifyListeners();
            }
          });
        }
      },
      functionModel: _functionModel,
      loginModel: _loginModel,
    );
    
    _eventHandler = CardEventHandlerImpl(
      dataManager: _dataManager,
      functionModel: _functionModel,
      serverService: _serverService,
      gameModel: _gameModel,
      authModel: _authModel,
    );
    
    if (_serverService != null) {
      log.i(_logTag, '🔗 注册WebSocket消息监听器');
      _serverService.addMessageListener(_handleServerMessage);
      log.i(_logTag, '✅ WebSocket消息监听器注册完成');
    } else {
      log.w(_logTag, '⚠️ ServerService为空，无法注册消息监听器');
    }
    
    // 监听FunctionModel的变化
    _functionModel.addListener(_onFunctionModelChanged);
    log.i(_logTag, '✅ FunctionModel监听器注册完成');

    if (_gameModel != null) {
      log.i(_logTag, '🔗 注册GameModel变更监听器');
      _gameModel.addListener(_handleGameModelChanged);
      log.i(_logTag, '✅ GameModel变更监听器注册完成');
    } else {
      log.w(_logTag, '⚠️ GameModel为空，无法注册变更监听器');
    }
    
    // 确保在下一帧初始化默认卡片，避免布局冲突
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isDisposed) {
        try {
          log.i(_logTag, '开始初始化默认卡片');
          _dataManager.initDefaultCards();
          
          // 如果卡片列表仍然为空，尝试从模型加载
          if (_dataManager.cards.isEmpty) {
            log.i(_logTag, '卡片列表为空，尝试从模型加载');
            _loadDefaultConfigsFromModel();
          }
          
          // 延迟请求服务器数据
          Future.delayed(const Duration(milliseconds: 500), () {
            if (!_isDisposed) {
              log.i(_logTag, '延迟500ms后请求服务器数据');
              _requestFunctionConfig();
            }
          });
        } catch (e) {
          log.e(_logTag, '初始化默认卡片失败: $e');
          // 如果初始化失败，创建基础配置
          _createFallbackConfigs();
        }
      }
    });
    
    log.i(_logTag, 'FunctionController初始化完成');
  }
  
  /// 从模型加载默认配置
  void _loadDefaultConfigsFromModel() {
    try {
      // 确保功能模型有默认配置
      if (_functionModel.configs.isEmpty) {
        log.i(_logTag, '功能模型配置为空，重新初始化默认配置');
        // 直接设置默认配置，而不是调用私有方法
        final defaultConfigs = [
          {
            'presetName': '配置1',
            'hotkey': '右键',
            'aiMode': 'FOV',
            'lockPosition': '头部',
            'selectedFaction': '无',
            'triggerSwitch': true,
            'weaponSwitch': false,  // 新增：切枪开关默认关闭
            'flashShield': false,   // 新增：背闪开关默认关闭
            'enabled': true,
          },
          {
            'presetName': '配置2',
            'hotkey': '右键',
            'aiMode': 'FOV',
            'lockPosition': '颈部',
            'selectedFaction': '无',
            'triggerSwitch': false,
            'weaponSwitch': false,  // 新增：切枪开关默认关闭
            'flashShield': false,   // 新增：背闪开关默认关闭
            'enabled': false,
          },
        ];
        _functionModel.setConfigs(defaultConfigs);
      }
      
      // 重新初始化卡片
      _dataManager.initDefaultCards();
      
      log.i(_logTag, '从模型加载了 ${_dataManager.cards.length} 个配置');
    } catch (e) {
      log.e(_logTag, '从模型加载配置失败: $e');
      _createFallbackConfigs();
    }
  }
  
  /// 创建备用配置（当所有其他方法都失败时）
  void _createFallbackConfigs() {
    try {
      log.i(_logTag, '创建备用配置');
      
      final bool isPro = _loginModel?.isPro ?? false;
      final fallbackConfigs = [
        {
          'presetName': '默认配置',
          'hotkey': '右键',
          'aiMode': 'PID',
          'lockPosition': '头部',
          'selectedFaction': '无',
          'triggerSwitch': false,
          'weaponSwitch': false,  // 新增：切枪开关默认关闭
          'flashShield': false,   // 新增：背闪开关默认关闭
          'enabled': true,
        },
      ];
      
      _functionModel.setConfigs(fallbackConfigs);
      _dataManager.initDefaultCards();
      
      log.i(_logTag, '备用配置创建完成，卡片数量: ${_dataManager.cards.length}');
    } catch (e) {
      log.e(_logTag, '创建备用配置失败: $e');
    }
  }
  
  /// 处理服务器消息
  void _handleServerMessage(dynamic message) {
    if (_isDisposed) return;
    
    // 添加测试日志
    log.i(_logTag, '🔥 FunctionController收到服务器消息: ${message.toString().substring(0, message.toString().length > 200 ? 200 : message.toString().length)}...');
    
    try {
      final Map<String, dynamic> responseData = jsonDecode(message.toString());
      final String action = responseData['action'] ?? '';
      
      log.i(_logTag, '解析消息action: $action');
      
      // 处理读取功能配置响应
      if (action == 'function_read_response') {
        log.i(_logTag, '🎯 检测到功能配置读取响应，开始处理');
        _handleFunctionReadResponse(responseData);
        _isRequesting = false;
        return;
      }
      
      // 处理修改功能配置响应
      if (action == 'function_modify_response') {
        log.i(_logTag, '🎯 检测到功能配置修改响应，开始处理');
        _handleFunctionModifyResponse(responseData);
        return;
      }
      
      log.i(_logTag, '消息不是功能相关，忽略处理');
    } catch (e) {
      log.e(_logTag, '处理服务器消息失败: $e');
      _isRequesting = false;
    }
  }
  
  /// 处理功能配置读取响应
  void _handleFunctionReadResponse(Map<String, dynamic> response) {
    try {
      log.i(_logTag, '收到功能配置读取响应: ${jsonEncode(response)}');
      
      final status = response['status'];
      if (status == 'success' || status == 'ok') {
        final data = response['data'];
        if (data != null && data['configs'] != null) {
          final configs = data['configs'] as List<dynamic>;
          
          // 阵营选择现在是每个配置独有的，不再有全局阵营选择
          
          log.i(_logTag, '解析到配置数量: ${configs.length}');
          for (int i = 0; i < configs.length; i++) {
            final config = configs[i];
            log.i(_logTag, '配置${i + 1}: ${jsonEncode(config)}');
          }
          
          // 处理非Pro用户的配置，确保AI模式、自动扳机、阵营选择和背闪防护权限正确
          if (_loginModel != null && !_loginModel.isPro) {
            log.i(_logTag, '检测到非Pro用户，开始处理配置限制');
            // 遍历配置列表，处理非Pro用户的限制
            for (var config in configs) {
              if (config is Map) {
                // 强制AI模式为PID（禁止FOV、FOVPID、所有冲锋狙等Pro模式）
                if (config['aiMode'] != null && config['aiMode'] != 'PID') {
                  log.i(_logTag, '非Pro用户的AI模式已强制改为PID: ${config['presetName']} (原模式: ${config['aiMode']})');
                  config['aiMode'] = 'PID';
                }
                // 强制关闭自动扳机
                if (config['triggerSwitch'] != null && config['triggerSwitch'] == true) {
                  config['triggerSwitch'] = false;
                  log.i(_logTag, '非Pro用户的自动扳机已强制关闭: ${config['presetName']}');
                }
                // 强制阵营选择为"无"
                if (config['selectedFaction'] != null && config['selectedFaction'] != '无') {
                  log.i(_logTag, '非Pro用户的阵营选择已强制设为"无": ${config['presetName']} (原阵营: ${config['selectedFaction']})');
                  config['selectedFaction'] = '无';
                }
                // 强制关闭切枪功能
                if (config['weaponSwitch'] != null && config['weaponSwitch'] == true) {
                  config['weaponSwitch'] = false;
                  log.i(_logTag, '非Pro用户的切枪功能已强制关闭: ${config['presetName']}');
                }
                // 强制关闭背闪防护
                if (config['flashShield'] != null && config['flashShield'] == true) {
                  config['flashShield'] = false;
                  log.i(_logTag, '非Pro用户的背闪防护已强制关闭: ${config['presetName']}');
                }
              }
            }
          } else {
            log.i(_logTag, '检测到Pro用户或登录模型为空，保持原始配置');
          }
          
          log.i(_logTag, '开始更新功能配置到模型');
          _updateFunctionConfigs(configs);
          
          log.i(_logTag, '通知UI更新');
          notifyListeners();
          
          log.i(_logTag, '功能配置处理完成，当前卡片数量: ${_dataManager.cards.length}');
          
          // 添加成功提示
          _showSuccessAndDismissLoading('功能配置加载成功！共加载 ${configs.length} 个配置');
        } else {
          log.w(_logTag, '响应数据为空或不包含configs字段');
          _showErrorAndDismissLoading('服务器返回的配置数据为空');
        }
      } else {
        final errorMessage = response['message'] ?? '未知错误';
        log.w(_logTag, '功能配置读取失败: $errorMessage');
        _showErrorAndDismissLoading('配置加载失败: $errorMessage');
      }
    } catch (e) {
      log.e(_logTag, '处理功能配置读取响应失败: $e');
      _showErrorAndDismissLoading('配置数据解析失败: $e');
    }
  }
  
  /// 处理功能配置修改响应
  void _handleFunctionModifyResponse(Map<String, dynamic> response) {
    try {
      final status = response['status'];
      if (status == 'success' || status == 'ok') {
        log.i(_logTag, '功能配置修改成功');
      } else {
        log.w(_logTag, '功能配置修改失败: ${response['message'] ?? '未知错误'}');
      }
    } catch (e) {
      log.e(_logTag, '处理功能配置修改响应失败: $e');
    }
  }
  
  /// 更新功能模型中的配置
  void _updateFunctionConfigs(List<dynamic> configs) {
    try {
      log.i(_logTag, '开始更新功能配置，输入配置数量: ${configs.length}');
      
      final updatedConfigs = configs
          .map((config) {
            final configMap = Map<String, dynamic>.from(config as Map);
            log.i(_logTag, '处理配置: ${configMap['presetName']} - hotkey: ${configMap['hotkey']}');
            
            // 过滤掉不应该在配置项中的字段，只保留配置相关的字段
            final filteredConfig = {
              'presetName': configMap['presetName'],
              'hotkey': configMap['hotkey'],
              'aiMode': configMap['aiMode'],
              'lockPosition': configMap['lockPosition'],
              'selectedFaction': configMap['selectedFaction'] ?? '无',  // 确保阵营选择字段存在
              'triggerSwitch': configMap['triggerSwitch'],
              'weaponSwitch': configMap['weaponSwitch'] ?? false,  // 新增：切枪开关，默认关闭
              'flashShield': configMap['flashShield'] ?? false,    // 新增：背闪开关，默认关闭
              'enabled': configMap['enabled'],
            };
            
            log.i(_logTag, '过滤后配置: ${jsonEncode(filteredConfig)}');
            return filteredConfig;
          })
          .toList();
      
      log.i(_logTag, '设置配置到FunctionModel，配置数量: ${updatedConfigs.length}');
      _functionModel.setConfigs(updatedConfigs);
      
      log.i(_logTag, 'FunctionModel配置设置完成，开始初始化卡片');
      _dataManager.initDefaultCards();
      
      log.i(_logTag, '卡片初始化完成，卡片数量: ${_dataManager.cards.length}');
      for (int i = 0; i < _dataManager.cards.length; i++) {
        final card = _dataManager.cards[i];
        log.i(_logTag, '卡片${i + 1}: ${card.presetName} - hotkey: ${card.hotkey}, aiMode: ${card.aiMode}');
      }
    } catch (e) {
      log.e(_logTag, '更新功能配置失败: $e');
    }
  }
  
  /// 添加新卡片
  void addCard(String presetName) {
    _eventHandler.onCardAdded(presetName);
    notifyListeners();
  }
  
  /// 更新卡片属性
  void updateCardProperty(CardData card, String property, String newValue) {
    // 如果是AI模式属性，并且用户是非Pro用户，强制设置为PID
    if (property == 'aiMode' && _loginModel != null && !_loginModel.isPro && newValue != 'PID') {
      log.w(_logTag, '非Pro用户尝试设置非PID模式（${newValue}），已强制改为PID模式');
      newValue = 'PID';
    }
    
    // 如果是阵营选择属性，并且用户是非Pro用户，强制设置为"无"
    if (property == 'selectedFaction' && _loginModel != null && !_loginModel.isPro && newValue != '无') {
      log.w(_logTag, '非Pro用户尝试设置阵营选择（${newValue}），已强制改为"无"');
      newValue = '无';
    }
    
    _eventHandler.onCardPropertyChanged(card, property, newValue);
  }
  
  /// 更新卡片布尔属性
  void updateCardBoolProperty(CardData card, String property, bool newValue) {
    // 检查自动扳机的Pro权限
    if (property == 'triggerSwitch' && newValue && _loginModel != null && !_loginModel.isPro) {
      log.w(_logTag, '非Pro用户尝试启用自动扳机，操作被拒绝');
      return; // 阻止更新
    }
    
    // 检查切枪功能的Pro权限
    if (property == 'weaponSwitch' && newValue && _loginModel != null && !_loginModel.isPro) {
      log.w(_logTag, '非Pro用户尝试启用切枪功能，操作被拒绝');
      return; // 阻止更新
    }
    
    // 检查背闪防护的Pro权限
    if (property == 'flashShield' && newValue && _loginModel != null && !_loginModel.isPro) {
      log.w(_logTag, '非Pro用户尝试启用背闪防护，操作被拒绝');
      return; // 阻止更新
    }
    
    _eventHandler.onCardBoolPropertyChanged(card, property, newValue);
  }
  
  /// 获取所有卡片信息并发送到服务器
  String getAllCardsInfo() {
    _eventHandler.onGetAllCardsInfo();
    return _dataManager.getAllCardsInfo();
  }
  
  /// 获取所有卡片信息并发送到服务器，带消息提示
  Future<void> saveAllCardsInfo(BuildContext context) async {
    try {
      _eventHandler.onGetAllCardsInfo();
      
      MessageComponent.showIconToast(
        context: context,
        message: '功能配置已保存',
        type: MessageType.success,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      log.e(_logTag, '保存功能配置失败: $e');
      
      MessageComponent.showIconToast(
        context: context,
        message: '保存失败: $e',
        type: MessageType.error,
        duration: const Duration(seconds: 2),
      );
    }
  }
  
  /// 主动刷新功能配置
  void refreshFunctionConfig() {
    if (_isRequesting) {
      log.i(_logTag, '已有请求正在进行中，忽略此次刷新');
      _showErrorAndDismissLoading('已有请求正在进行中，请稍候...');
      return;
    }
    
    if (_serverService == null || !_serverService.isConnected || 
        _authModel == null || _gameModel == null) {
      log.w(_logTag, '无法刷新配置：服务未连接或模型未初始化');
      _showErrorAndDismissLoading('无法刷新配置：服务未连接或模型未初始化');
      return;
    }
    
    _lastRequestTime = DateTime.now().subtract(const Duration(seconds: 5));
    _lastRequestedGame = '';
    _requestFunctionConfig();
  }
  
  /// 处理卡片更新
  void handleCardUpdated(CardData card) {
    _eventHandler.onCardUpdated(card);
    notifyListeners();
  }
  
  /// 处理游戏模型变更
  void _handleGameModelChanged() {
    if (_isDisposed || _gameModel == null) return;
    _requestFunctionConfig();
  }
  
  /// 请求最新的功能配置
  void _requestFunctionConfig() {
    log.i(_logTag, '🚀 开始请求功能配置');
    
    if (_isRequesting) {
      log.i(_logTag, '⏳ 已有请求正在进行中，跳过');
      return;
    }
    
    if (_serverService == null) {
      log.w(_logTag, '❌ ServerService为空，无法请求配置');
      return;
    }
    
    if (!_serverService.isConnected) {
      log.w(_logTag, '❌ WebSocket未连接，无法请求配置');
      _showErrorAndDismissLoading('WebSocket未连接，无法加载功能配置');
      return;
    }
    
    if (_authModel == null) {
      log.w(_logTag, '❌ AuthModel为空，无法请求配置');
      return;
    }
    
    if (_gameModel == null) {
      log.w(_logTag, '❌ GameModel为空，无法请求配置');
      return;
    }
    
    final now = DateTime.now();
    // 安全地获取值（前面已经检查过_gameModel和_authModel不为null）
    final currentGame = _gameModel.currentGame;
    final username = _authModel.username;
    final cardKey = _gameModel.cardKey;

    if (currentGame.isEmpty || username.isEmpty) {
      log.e(_logTag, '❌ 请求参数不完整 - username: $username, gameName: $currentGame');
      _showErrorAndDismissLoading('请求参数不完整，无法加载功能配置');
      return;
    }

    log.i(_logTag, '📋 请求参数 - username: $username, gameName: $currentGame, cardKey: $cardKey');
    
    // 防抖动控制
    if (_lastRequestedGame == currentGame && 
        now.difference(_lastRequestTime).inMilliseconds < 2000) {
      log.i(_logTag, '⏰ 防抖动控制，距离上次请求时间过短，跳过');
      return;
    }
    
    _lastRequestTime = now;
    _lastRequestedGame = currentGame;
    _isRequesting = true;
    
    // 显示正在请求的提示
    _showPersistentLoadingMessage('正在向服务器请求功能配置...');
    
    try {
      final request = {
        'action': 'function_read',
        'content': {
          'username': username,
          'gameName': currentGame,
          'cardKey': cardKey,
          'updatedAt': now.toIso8601String(),
        }
      };
      
      final requestJson = jsonEncode(request);
      log.i(_logTag, '📤 发送请求: $requestJson');
      
      _serverService.sendMessage(requestJson);
      log.i(_logTag, '✅ 功能配置请求已发送: $currentGame');
      
      // 设置超时处理
      Future.delayed(const Duration(seconds: 10), () {
        if (_isRequesting) {
          _isRequesting = false;
          log.w(_logTag, '⏰ 功能配置请求超时，重置请求状态');
          _showErrorAndDismissLoading('请求超时，服务器可能繁忙，请稍后重试');
        }
      });
    } catch (e) {
      _isRequesting = false;
      log.e(_logTag, '❌ 请求功能配置失败: $e');
      _showErrorAndDismissLoading('发送请求失败: $e');
    }
  }
  
  /// 处理FunctionModel变化
  void _onFunctionModelChanged() {
    if (_isDisposed) return;

    log.i(_logTag, '🔄 FunctionModel数据已更新，重新初始化卡片');

    try {
      // 重新从模型加载数据
      _dataManager.initDefaultCards();

      // 通知UI更新
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!_isDisposed) {
          notifyListeners();
        }
      });

      log.i(_logTag, '✅ 卡片数据已更新，当前卡片数量: ${_dataManager.cards.length}');
    } catch (e) {
      log.e(_logTag, '处理FunctionModel变化时出错: $e');
    }
  }

  @override
  void dispose() {
    _isDisposed = true;

    // 关闭任何正在显示的加载提示
    _dismissCurrentLoadingMessage();

    // 清理context引用，避免内存泄漏
    _currentContext = null;

    // 移除FunctionModel监听器
    _functionModel.removeListener(_onFunctionModelChanged);

    if (_serverService != null) {
      _serverService.removeMessageListener(_handleServerMessage);
    }

    if (_gameModel != null) {
      _gameModel.removeListener(_handleGameModelChanged);
    }

    super.dispose();
  }
}
