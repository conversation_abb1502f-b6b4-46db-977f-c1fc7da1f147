// ignore_for_file: file_names, library_private_types_in_public_api, unreachable_switch_default, use_super_parameters

import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';

/// 按钮类型枚举
enum ButtonType {
  primary,    // 主要按钮 - 蓝色
  secondary,  // 次要按钮 - 灰色
  success,    // 成功按钮 - 绿色
  warning,    // 警告按钮 - 橙色
  danger,     // 危险按钮 - 红色
  info,       // 信息按钮 - 青色
  outline,    // 轮廓按钮
}

/// 按钮尺寸枚举
enum ButtonSize { small, medium, large }

/// 按钮形状枚举
enum ButtonShape {
  standard,   // 标准矩形
  pill,       // 药丸形
  circle,     // 圆形（仅图标按钮）
}

/// 按钮排列方向枚举
enum ButtonAlignment { 
  horizontal,  // 水平排列
  vertical,    // 垂直排列
}

/// 按钮模式枚举
enum ButtonMode {
  single,     // 单个按钮
  group,      // 按钮组
  iconOnly,   // 纯图标按钮
}

/// 统一按钮配置类
class ButtonConfig {
  // 基础属性
  final String? text;
  final IconData? icon;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final ButtonShape shape;
  final bool disabled;
  
  // 样式属性
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;
  final bool fullWidth;
  
  // 组合属性
  final ButtonMode mode;
  final List<ButtonConfig>? children;
  final ButtonAlignment alignment;
  final double spacing;
  final EdgeInsets padding;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final bool expandToFill;

  const ButtonConfig({
    // 基础属性
    this.text,
    this.icon,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.shape = ButtonShape.standard,
    this.disabled = false,
    
    // 样式属性
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.fullWidth = false,
    
    // 组合属性
    this.mode = ButtonMode.single,
    this.children,
    this.alignment = ButtonAlignment.horizontal,
    this.spacing = 8.0,
    this.padding = const EdgeInsets.all(0),
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.expandToFill = false,
  });

  /// 快速创建单个文本按钮
  ButtonConfig.text(
    String text, {
    VoidCallback? onPressed,
    ButtonType type = ButtonType.primary,
    ButtonSize size = ButtonSize.medium,
    ButtonShape shape = ButtonShape.standard,
    bool disabled = false,
    Color? backgroundColor,
    Color? textColor,
    bool fullWidth = false,
  }) : this(
    text: text,
    onPressed: disabled ? null : onPressed,
    type: type,
    size: size,
    shape: shape,
    disabled: disabled,
    backgroundColor: backgroundColor,
    textColor: textColor,
    fullWidth: fullWidth,
    mode: ButtonMode.single,
  );

  /// 快速创建图标按钮
  ButtonConfig.icon(
    IconData icon, {
    VoidCallback? onPressed,
    ButtonType type = ButtonType.primary,
    ButtonSize size = ButtonSize.medium,
    ButtonShape shape = ButtonShape.circle,
    bool disabled = false,
    Color? backgroundColor,
    Color? iconColor,
  }) : this(
    icon: icon,
    onPressed: disabled ? null : onPressed,
    type: type,
    size: size,
    shape: shape,
    disabled: disabled,
    backgroundColor: backgroundColor,
    iconColor: iconColor,
    mode: ButtonMode.iconOnly,
  );

  /// 快速创建带图标的文本按钮
  ButtonConfig.textWithIcon(
    String text,
    IconData icon, {
    VoidCallback? onPressed,
    ButtonType type = ButtonType.primary,
    ButtonSize size = ButtonSize.medium,
    ButtonShape shape = ButtonShape.standard,
    bool disabled = false,
    Color? backgroundColor,
    Color? textColor,
    Color? iconColor,
    bool fullWidth = false,
  }) : this(
    text: text,
    icon: icon,
    onPressed: disabled ? null : onPressed,
    type: type,
    size: size,
    shape: shape,
    disabled: disabled,
    backgroundColor: backgroundColor,
    textColor: textColor,
    iconColor: iconColor,
    fullWidth: fullWidth,
    mode: ButtonMode.single,
  );

  /// 快速创建按钮组
  ButtonConfig.group(
    List<ButtonConfig> children, {
    ButtonAlignment alignment = ButtonAlignment.horizontal,
    double spacing = 8.0,
    EdgeInsets padding = const EdgeInsets.all(0),
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    bool expandToFill = false,
  }) : this(
    mode: ButtonMode.group,
    children: children,
    alignment: alignment,
    spacing: spacing,
    padding: padding,
    mainAxisAlignment: mainAxisAlignment,
    crossAxisAlignment: crossAxisAlignment,
    expandToFill: expandToFill,
  );

  /// 快速创建确认/取消按钮组
  ButtonConfig.actionGroup({
    required String confirmText,
    required String cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    ButtonType confirmType = ButtonType.primary,
    ButtonType cancelType = ButtonType.secondary,
    ButtonAlignment alignment = ButtonAlignment.horizontal,
    double spacing = 12.0,
    bool expandToFill = true,
  }) : this(
    mode: ButtonMode.group,
    children: [
      ButtonConfig.text(
        cancelText,
        onPressed: onCancel,
        type: cancelType,
        fullWidth: alignment == ButtonAlignment.vertical || expandToFill,
      ),
      ButtonConfig.text(
        confirmText,
        onPressed: onConfirm,
        type: confirmType,
        fullWidth: alignment == ButtonAlignment.vertical || expandToFill,
      ),
    ],
    alignment: alignment,
    spacing: spacing,
    expandToFill: expandToFill,
    mainAxisAlignment: expandToFill 
        ? MainAxisAlignment.spaceEvenly 
        : MainAxisAlignment.end,
  );
}

/// 按钮主题配置
class _ButtonTheme {
  final Color backgroundColor;
  final Color textColor;
  final Color iconColor;
  final GFButtonType gfType;
  
  const _ButtonTheme({
    required this.backgroundColor,
    required this.textColor,
    required this.iconColor,
    required this.gfType,
  });
  
  factory _ButtonTheme.fromType(ButtonType type) {
    switch (type) {
      case ButtonType.primary:
        return const _ButtonTheme(
          backgroundColor: Color(0xFF2196F3),
          textColor: Colors.white,
          iconColor: Colors.white,
          gfType: GFButtonType.solid,
        );
      case ButtonType.secondary:
        return const _ButtonTheme(
          backgroundColor: Color(0xFF9E9E9E),
          textColor: Colors.white,
          iconColor: Colors.white,
          gfType: GFButtonType.solid,
        );
      case ButtonType.success:
        return const _ButtonTheme(
          backgroundColor: Color(0xFF4CAF50),
          textColor: Colors.white,
          iconColor: Colors.white,
          gfType: GFButtonType.solid,
        );
      case ButtonType.warning:
        return const _ButtonTheme(
          backgroundColor: Color(0xFFFF9800),
          textColor: Colors.white,
          iconColor: Colors.white,
          gfType: GFButtonType.solid,
        );
      case ButtonType.danger:
        return const _ButtonTheme(
          backgroundColor: Color(0xFFF44336),
          textColor: Colors.white,
          iconColor: Colors.white,
          gfType: GFButtonType.solid,
        );
      case ButtonType.info:
        return const _ButtonTheme(
          backgroundColor: Color(0xFF00BCD4),
          textColor: Colors.white,
          iconColor: Colors.white,
          gfType: GFButtonType.solid,
        );
      case ButtonType.outline:
        return const _ButtonTheme(
          backgroundColor: Colors.transparent,
          textColor: Color(0xFF2196F3),
          iconColor: Color(0xFF2196F3),
          gfType: GFButtonType.outline,
        );
    }
  }
}

/// 统一按钮组件
class Button extends StatelessWidget {
  final ButtonConfig config;

  const Button(this.config, {Key? key}) : super(key: key);

  /// 通用的按钮创建方法 - 核心API
  static Widget create(ButtonConfig config) {
    return Button(config);
  }

  @override
  Widget build(BuildContext context) {
    switch (config.mode) {
      case ButtonMode.single:
        return _buildSingleButton();
      case ButtonMode.iconOnly:
        return _buildIconButton();
      case ButtonMode.group:
        return _buildButtonGroup();
    }
  }

  /// 构建单个按钮
  Widget _buildSingleButton() {
    final theme = _ButtonTheme.fromType(config.type);
    final gfSize = _convertSize(config.size);
    final gfShape = _convertShape(config.shape);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: GFButton(
        onPressed: config.disabled ? null : config.onPressed,
        text: config.text ?? '',
        icon: config.icon != null ? Icon(
          config.icon,
          color: config.iconColor ?? theme.iconColor,
        ) : null,
        size: gfSize,
        shape: gfShape,
        type: theme.gfType,
        fullWidthButton: config.fullWidth,
        textColor: config.textColor ?? theme.textColor,
        color: config.backgroundColor ?? theme.backgroundColor,
        borderSide: config.type == ButtonType.outline
            ? BorderSide(
                color: config.textColor ?? theme.textColor,
                width: 1.5,
              )
            : null,
        disabledColor: Colors.grey.shade300,
        disabledTextColor: Colors.grey.shade600,
        elevation: config.type == ButtonType.outline ? 0 : 2,
        focusElevation: config.type == ButtonType.outline ? 0 : 4,
        hoverElevation: config.type == ButtonType.outline ? 0 : 6,
      ),
    );
  }

  /// 构建图标按钮
  Widget _buildIconButton() {
    final theme = _ButtonTheme.fromType(config.type);
    final gfSize = _convertSize(config.size);
    final gfShape = _convertIconShape(config.shape);

    return GFIconButton(
      onPressed: config.disabled ? null : config.onPressed,
      icon: Icon(
        config.icon!,
        color: config.iconColor ?? (config.type == ButtonType.outline 
            ? theme.iconColor 
            : Colors.white),
      ),
      type: theme.gfType,
      shape: gfShape,
      size: gfSize,
      color: config.backgroundColor ?? theme.backgroundColor,
      disabledColor: Colors.grey.shade300,
    );
  }
  
  /// 构建按钮组
  Widget _buildButtonGroup() {
    if (config.children == null || config.children!.isEmpty) {
      return const SizedBox.shrink();
    }

    List<Widget> buttonWidgets = config.children!.map((buttonConfig) {
      Widget button = Button.create(buttonConfig);

      // 如果需要展开填充且是水平排列，则包裹在Expanded中
      if (config.expandToFill && config.alignment == ButtonAlignment.horizontal) {
        button = Expanded(child: button);
      }

      return button;
    }).toList();

    Widget content;
    if (config.alignment == ButtonAlignment.horizontal) {
      content = Row(
        mainAxisAlignment: config.mainAxisAlignment,
        crossAxisAlignment: config.crossAxisAlignment,
        children: _addSpacing(buttonWidgets, config.spacing, isHorizontal: true),
      );
    } else {
      content = Column(
        mainAxisAlignment: config.mainAxisAlignment,
        crossAxisAlignment: config.crossAxisAlignment,
        mainAxisSize: MainAxisSize.min,
        children: _addSpacing(buttonWidgets, config.spacing, isHorizontal: false),
      );
    }

    return Padding(
      padding: config.padding,
      child: content,
    );
  }

  /// 添加间距的辅助方法
  List<Widget> _addSpacing(List<Widget> children, double spacing, {required bool isHorizontal}) {
    if (children.isEmpty || spacing == 0) return children;
    
    List<Widget> result = [];
    for (int i = 0; i < children.length; i++) {
      result.add(children[i]);
      if (i < children.length - 1) {
        result.add(isHorizontal 
            ? SizedBox(width: spacing) 
            : SizedBox(height: spacing));
      }
    }
    return result;
  }
  
  /// 转换尺寸
  double _convertSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small: return GFSize.SMALL;
      case ButtonSize.large: return GFSize.LARGE;
      case ButtonSize.medium: default: return GFSize.MEDIUM;
    }
  }

  /// 转换按钮形状
  GFButtonShape _convertShape(ButtonShape shape) {
    switch (shape) {
      case ButtonShape.pill: return GFButtonShape.pills;
      case ButtonShape.standard: 
      case ButtonShape.circle: 
      default: return GFButtonShape.standard;
    }
  }

  /// 转换图标按钮形状
  GFIconButtonShape _convertIconShape(ButtonShape shape) {
    switch (shape) {
      case ButtonShape.circle: return GFIconButtonShape.circle;
      case ButtonShape.standard:
      case ButtonShape.pill:
      default: return GFIconButtonShape.standard;
    }
  }
}

/*
===========================================
新版统一API使用示例 - New Unified API Examples
===========================================

/// 1. 基础单个按钮创建
// 文本按钮
Button.create(ButtonConfig.text("保存", onPressed: () => print("保存")));

// 图标按钮
Button.create(ButtonConfig.icon(Icons.add, onPressed: () => print("添加")));

// 带图标的文本按钮
Button.create(ButtonConfig.textWithIcon(
  "保存", 
  Icons.save, 
  onPressed: () => print("保存"),
  type: ButtonType.primary,
));

/// 2. 不同类型的按钮
Button.create(ButtonConfig.text("主要", type: ButtonType.primary));
Button.create(ButtonConfig.text("次要", type: ButtonType.secondary));
Button.create(ButtonConfig.text("成功", type: ButtonType.success));
Button.create(ButtonConfig.text("警告", type: ButtonType.warning));
Button.create(ButtonConfig.text("危险", type: ButtonType.danger));
Button.create(ButtonConfig.text("信息", type: ButtonType.info));
Button.create(ButtonConfig.text("轮廓", type: ButtonType.outline));

/// 3. 不同尺寸和形状
Button.create(ButtonConfig.text("小按钮", size: ButtonSize.small));
Button.create(ButtonConfig.text("中按钮", size: ButtonSize.medium));
Button.create(ButtonConfig.text("大按钮", size: ButtonSize.large));

Button.create(ButtonConfig.text("标准", shape: ButtonShape.standard));
Button.create(ButtonConfig.text("药丸", shape: ButtonShape.pill));
Button.create(ButtonConfig.icon(Icons.star, shape: ButtonShape.circle));

/// 4. 自定义样式
Button.create(ButtonConfig.text(
  "自定义按钮",
  backgroundColor: Colors.purple,
  textColor: Colors.white,
  fullWidth: true,
));

/// 5. 按钮组 - 水平排列
Button.create(ButtonConfig.group([
  ButtonConfig.text("编辑", onPressed: () => print("编辑")),
  ButtonConfig.text("删除", type: ButtonType.danger, onPressed: () => print("删除")),
  ButtonConfig.text("分享", type: ButtonType.info, onPressed: () => print("分享")),
], alignment: ButtonAlignment.horizontal, spacing: 12.0, expandToFill: true));

/// 6. 按钮组 - 垂直排列
Button.create(ButtonConfig.group([
  ButtonConfig.textWithIcon("个人信息", Icons.person, type: ButtonType.outline),
  ButtonConfig.textWithIcon("隐私设置", Icons.privacy_tip, type: ButtonType.outline),
  ButtonConfig.textWithIcon("通知设置", Icons.notifications, type: ButtonType.outline),
], 
  alignment: ButtonAlignment.vertical, 
  spacing: 8.0,
  padding: EdgeInsets.all(16.0),
));

/// 7. 确认/取消按钮组
Button.create(ButtonConfig.actionGroup(
  confirmText: "确认",
  cancelText: "取消",
  onConfirm: () => print("确认"),
  onCancel: () => print("取消"),
  alignment: ButtonAlignment.horizontal,
));

/// 8. 混合类型按钮组
Button.create(ButtonConfig.group([
  ButtonConfig.icon(Icons.favorite, type: ButtonType.danger),
  ButtonConfig.text("收藏", type: ButtonType.primary),
  ButtonConfig.textWithIcon("分享", Icons.share, type: ButtonType.info),
], spacing: 16.0));

/// 9. 复杂配置示例
Button.create(ButtonConfig.group([
  ButtonConfig.text("选项1", 
    type: ButtonType.outline, 
    fullWidth: true,
    onPressed: () => print("选项1"),
  ),
  ButtonConfig.text("选项2", 
    type: ButtonType.outline, 
    fullWidth: true,
    onPressed: () => print("选项2"),
  ),
  ButtonConfig.text("选项3", 
    type: ButtonType.outline, 
    fullWidth: true,
    onPressed: () => print("选项3"),
  ),
], 
  alignment: ButtonAlignment.vertical,
  spacing: 12.0,
  mainAxisAlignment: MainAxisAlignment.center,
  crossAxisAlignment: CrossAxisAlignment.stretch,
));

/// 10. 业务场景示例
class NewFormPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(children: [
      // 表单操作按钮
      Button.create(ButtonConfig.actionGroup(
        confirmText: "保存",
        cancelText: "取消",
        onConfirm: () => print("保存表单"),
        onCancel: () => print("取消操作"),
      )),
      
      SizedBox(height: 16),
      
      // 功能按钮组
      Button.create(ButtonConfig.group([
        ButtonConfig.textWithIcon("编辑", Icons.edit, type: ButtonType.primary),
        ButtonConfig.textWithIcon("删除", Icons.delete, type: ButtonType.danger),
        ButtonConfig.textWithIcon("分享", Icons.share, type: ButtonType.info),
      ], expandToFill: true, spacing: 12.0)),
      
      SizedBox(height: 16),
      
      // 图标操作栏
      Button.create(ButtonConfig.group([
        ButtonConfig.icon(Icons.home),
        ButtonConfig.icon(Icons.search),
        ButtonConfig.icon(Icons.favorite),
        ButtonConfig.icon(Icons.person),
      ], spacing: 20.0, mainAxisAlignment: MainAxisAlignment.spaceEvenly)),
    ]);
  }
}

/// 11. 便捷函数封装示例
class QuickButtons {
  // 快速创建主要操作按钮
  static Widget primaryAction(String text, VoidCallback onPressed) {
    return Button.create(ButtonConfig.text(text, 
      onPressed: onPressed, 
      type: ButtonType.primary,
      fullWidth: true,
    ));
  }
  
  // 快速创建图标按钮栏
  static Widget iconBar(List<IconData> icons, List<VoidCallback> callbacks) {
    List<ButtonConfig> buttons = [];
    for (int i = 0; i < icons.length && i < callbacks.length; i++) {
      buttons.add(ButtonConfig.icon(icons[i], onPressed: callbacks[i]));
    }
    return Button.create(ButtonConfig.group(buttons, 
      mainAxisAlignment: MainAxisAlignment.spaceEvenly));
  }
  
  // 快速创建选项列表
  static Widget optionList(List<String> options, List<VoidCallback> callbacks) {
    List<ButtonConfig> buttons = [];
    for (int i = 0; i < options.length && i < callbacks.length; i++) {
      buttons.add(ButtonConfig.text(options[i], 
        onPressed: callbacks[i],
        type: ButtonType.outline,
        fullWidth: true,
      ));
    }
    return Button.create(ButtonConfig.group(buttons, 
      alignment: ButtonAlignment.vertical, 
      spacing: 8.0));
  }
}
*/
