// ignore_for_file: constant_identifier_names

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 登录模型 - 存储登录相关信息
class LoginModel extends ChangeNotifier {
  // 默认值配置
  static const _defaults = {
    'serverAddress': '************',
    'serverPort': '8080',
    'username': 'admin',
    'password': 'admin',
  };
  
  // 服务器信息
  String _serverAddress = _defaults['serverAddress']!;
  String _serverPort = _defaults['serverPort']!;
  
  // 登录信息
  String _username = _defaults['username']!;
  String _password = _defaults['password']!;
  String _token = '';
  bool _rememberPassword = true;
  
  // Pro登录状态
  bool _isPro = false;
  
  // 登录状态
  String _loginStatus = 'false';
  String _lastLoginTime = '';
  
  // 登录后是否需要刷新
  bool _refreshAfterLogin = false;
  
  // Getters
  String get serverAddress => _serverAddress;
  String get serverPort => _serverPort;
  String get username => _username;
  String get password => _password;
  String get token => _token;
  bool get rememberPassword => _rememberPassword;
  String get loginStatus => _loginStatus;
  String get lastLoginTime => _lastLoginTime;
  bool get refreshAfterLogin => _refreshAfterLogin;
  bool get isPro => _isPro;
  
  // 加载登录设置
  Future<void> loadLoginSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 使用默认值作为备选
    _serverAddress = prefs.getString('serverAddress') ?? _defaults['serverAddress']!;
    _serverPort = prefs.getString('serverPort') ?? _defaults['serverPort']!;
    _username = prefs.getString('username') ?? _defaults['username']!;
    _password = prefs.getString('password') ?? _defaults['password']!;
    _token = prefs.getString('token') ?? '';
    _rememberPassword = prefs.getBool('rememberPassword') ?? true;
    _loginStatus = prefs.getString('loginStatus') ?? 'false';
    _lastLoginTime = prefs.getString('lastLoginTime') ?? '';
    _isPro = prefs.getBool('isPro') ?? false;
    
    // 如果不记住密码，则清除密码
    if (!_rememberPassword) {
      _password = '';
    }
    
    notifyListeners();
  }
  
  // 更新服务器地址
  Future<void> updateServerAddress(String value) async {
    _serverAddress = value;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新服务器端口
  Future<void> updateServerPort(String value) async {
    _serverPort = value;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新用户名
  Future<void> updateUsername(String value) async {
    _username = value;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新密码
  Future<void> updatePassword(String value) async {
    _password = value;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新令牌
  Future<void> updateToken(String value) async {
    _token = value;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新登录状态
  Future<void> updateLoginStatus(String status) async {
    _loginStatus = status;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新Pro状态
  Future<void> updateProStatus(bool isPro) async {
    if (_isPro != isPro) {
      final oldStatus = _isPro;
    _isPro = isPro;
    await _saveSettings();
      
      // 添加详细的调试日志
      debugPrint('🔄 Pro状态变更: $oldStatus -> $isPro');
      debugPrint('📍 调用栈: ${StackTrace.current.toString().split('\n').take(3).join(' -> ')}');
      
    notifyListeners();
    }
  }
  
  // 更新最后登录时间
  Future<void> updateLastLoginTime(String time) async {
    _lastLoginTime = time;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新记住密码设置
  Future<void> updateRememberPassword(bool remember) async {
    _rememberPassword = remember;
    await _saveSettings();
    notifyListeners();
  }
  
  // 更新登录后刷新标志
  void updateRefreshAfterLogin(bool refresh) {
    _refreshAfterLogin = refresh;
    notifyListeners();
  }
  
  // 保存所有登录相关设置
  Future<void> saveLoginInfo({
    required String username,
    required String password,
    required String token,
    bool rememberPassword = false,
    String loginStatus = 'true',
    String? lastLoginTime,
    bool isPro = false,
  }) async {
    _username = username;
    _password = password;
    _token = token;
    _rememberPassword = rememberPassword;
    _loginStatus = loginStatus;
    _lastLoginTime = lastLoginTime ?? DateTime.now().toIso8601String();
    _isPro = isPro;
    
    await _saveSettings();
    notifyListeners();
  }
  
  // 清除登录信息
  Future<void> clearLoginInfo() async {
    _token = '';
    _loginStatus = 'false';
    
    // ⚠️ 重要：不要清除Pro状态！
    // Pro状态应该在用户重新登录时由服务器验证并设置
    // 如果在这里清除，会导致用户退出登录后Pro状态丢失
    // _isPro = false; // ❌ 禁止取消注释这行代码
    
    if (!_rememberPassword) {
      _username = '';
      _password = '';
    }
    
    await _saveSettings();
    notifyListeners();
  }
  
  // 保存设置
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    await Future.wait([
      prefs.setString('serverAddress', _serverAddress),
      prefs.setString('serverPort', _serverPort),
      prefs.setString('username', _username),
      prefs.setString('loginStatus', _loginStatus),
      prefs.setString('lastLoginTime', _lastLoginTime),
      prefs.setString('token', _token),
      prefs.setBool('rememberPassword', _rememberPassword),
      prefs.setBool('isPro', _isPro),
      prefs.setString('password', _rememberPassword ? _password : ''),
    ]);
  }
  
  /// 强制同步Pro状态 - 用于修复状态不一致问题
  /// 这个方法应该只在确认服务器返回正确状态时调用
  Future<void> forceUpdateProStatus(bool isPro, {String reason = '服务器同步'}) async {
    if (_isPro != isPro) {
      final oldStatus = _isPro;
      _isPro = isPro;
      await _saveSettings();
      notifyListeners();
      
      // 记录状态变更日志
      debugPrint('Pro状态已强制更新: $oldStatus -> $isPro (原因: $reason)');
    }
  }
  
  /// 检查Pro状态一致性
  bool checkProStatusConsistency() {
    // 这里可以添加更多的一致性检查逻辑
    return _isPro == _isPro; // 简单的自检
  }
} 