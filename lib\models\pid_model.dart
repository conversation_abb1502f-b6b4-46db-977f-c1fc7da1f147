// ignore_for_file: unused_import, unused_element

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// PID设置模型 - 管理近端瞄准控制相关的数据
class PidModel extends ChangeNotifier {
  // 默认值配置
  static const Map<String, dynamic> defaults = {
    'nearMoveFactor': 1.0,
    'nearStabilizer': 0.0,
    'nearResponseRate': 0.3,
    'nearAssistZone': 3.0,
    'nearResponseDelay': 1.0,
    'nearMaxAdjustment': 2.0,
    'farFactor': 1.0,
    'yAxisFactor': 1.0,
    'pidRandomFactor': 0.5,
  };
  
  // PID参数
  double _nearMoveFactor = 1.0;
  double _nearStabilizer = 0.0;
  double _nearResponseRate = 0.3;
  double _nearAssistZone = 3.0;
  double _nearResponseDelay = 1.0;
  double _nearMaxAdjustment = 2.0;
  double _farFactor = 1.0;
  double _yAxisFactor = 1.0;
  double _pidRandomFactor = 0.5;
  
  // 状态管理
  bool _hasUnsavedChanges = false;
  bool _saveButtonLocked = false;
  bool _isSliderDragging = false;
  
  // 元数据
  String _username = 'admin';
  String _gameName = 'csgo2';
  String _createdAt = '';
  String _updatedAt = '';
  
  // Getters
  double get nearMoveFactor => _nearMoveFactor;
  double get nearStabilizer => _nearStabilizer;
  double get nearResponseRate => _nearResponseRate;
  double get nearAssistZone => _nearAssistZone;
  double get nearResponseDelay => _nearResponseDelay;
  double get nearMaxAdjustment => _nearMaxAdjustment;
  double get farFactor => _farFactor;
  double get yAxisFactor => _yAxisFactor;
  double get pidRandomFactor => _pidRandomFactor;
  String get username => _username;
  String get gameName => _gameName;
  String get createdAt => _createdAt;
  String get updatedAt => _updatedAt;
  
  // 状态管理 Getters
  bool get hasUnsavedChanges => _hasUnsavedChanges;
  bool get saveButtonLocked => _saveButtonLocked;
  bool get isSliderDragging => _isSliderDragging;
  bool get saveButtonEnabled => !_saveButtonLocked;
  
  // Setters
  set nearMoveFactor(double value) {
    if (_nearMoveFactor != value) {
      _nearMoveFactor = value;
      _markAsChanged();
    }
  }
  
  set nearStabilizer(double value) {
    if (_nearStabilizer != value) {
      _nearStabilizer = value;
      _markAsChanged();
    }
  }
  
  set nearResponseRate(double value) {
    if (_nearResponseRate != value) {
      _nearResponseRate = value;
      _markAsChanged();
    }
  }
  
  set nearAssistZone(double value) {
    if (_nearAssistZone != value) {
      _nearAssistZone = value;
      _markAsChanged();
    }
  }
  
  set nearResponseDelay(double value) {
    if (_nearResponseDelay != value) {
      _nearResponseDelay = value;
      _markAsChanged();
    }
  }
  
  set nearMaxAdjustment(double value) {
    if (_nearMaxAdjustment != value) {
      _nearMaxAdjustment = value;
      _markAsChanged();
    }
  }
  
  set farFactor(double value) {
    if (_farFactor != value) {
      _farFactor = value;
      _markAsChanged();
    }
  }
  
  set yAxisFactor(double value) {
    if (_yAxisFactor != value) {
      _yAxisFactor = value;
      _markAsChanged();
    }
  }
  
  set pidRandomFactor(double value) {
    if (_pidRandomFactor != value) {
      _pidRandomFactor = value;
      _markAsChanged();
    }
  }
  
  set username(String value) {
    if (_username != value) {
      _username = value;
      // 延迟通知监听器，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
  
  set gameName(String value) {
    if (_gameName != value) {
      _gameName = value;
      // 延迟通知监听器，避免在构建期间调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }
  
  /// 构造函数
  PidModel() {
    // 移除直接调用loadSettings()，改为延迟初始化
    _initializeAsync();
  }
  
  /// 异步初始化
  void _initializeAsync() {
    // 使用microtask延迟执行，避免在构造函数中直接调用notifyListeners
    Future.microtask(() async {
      await loadSettings();
    });
  }
  
  /// 标记参数已更改
  void _markAsChanged() {
    _hasUnsavedChanges = true;
    _updateTimestamp();
    // 延迟通知监听器，避免在构建期间调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
  
  /// 更新时间戳
  void _updateTimestamp() {
    _updatedAt = DateTime.now().toIso8601String();
  }
  
  /// 设置滑块拖动状态
  void setSliderDragging(bool isDragging) {
    if (_isSliderDragging != isDragging) {
      _isSliderDragging = isDragging;
      notifyListeners();
    }
  }
  
  /// 锁定保存按钮（滚轮操作后）
  void lockSaveButton() {
    if (!_saveButtonLocked) {
      _saveButtonLocked = true;
      notifyListeners();
    }
  }
  
  /// 解锁保存按钮（加减按钮操作）
  void unlockSaveButton() {
    if (_saveButtonLocked) {
      _saveButtonLocked = false;
      notifyListeners();
    }
  }
  
  /// 清除未保存状态
  void clearUnsavedChanges() {
    _hasUnsavedChanges = false;
    notifyListeners();
  }
  
  /// 重置为默认值
  void resetToDefaults() {
    _nearMoveFactor = defaults['nearMoveFactor'];
    _nearStabilizer = defaults['nearStabilizer'];
    _nearResponseRate = defaults['nearResponseRate'];
    _nearAssistZone = defaults['nearAssistZone'];
    _nearResponseDelay = defaults['nearResponseDelay'];
    _nearMaxAdjustment = defaults['nearMaxAdjustment'];
    _farFactor = defaults['farFactor'];
    _yAxisFactor = defaults['yAxisFactor'];
    _pidRandomFactor = defaults['pidRandomFactor'];
    _updateTimestamp();
    _unlockSaveButtonAfterReset();
    notifyListeners();
  }
  
  /// 重置后解锁保存按钮
  void _unlockSaveButtonAfterReset() {
    _saveButtonLocked = false;
  }
  
  /// 从配置中加载设置
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    _nearMoveFactor = prefs.getDouble('pid_nearMoveFactor') ?? defaults['nearMoveFactor'];
    _nearStabilizer = prefs.getDouble('pid_nearStabilizer') ?? defaults['nearStabilizer'];
    _nearResponseRate = prefs.getDouble('pid_nearResponseRate') ?? defaults['nearResponseRate'];
    _nearAssistZone = prefs.getDouble('pid_nearAssistZone') ?? defaults['nearAssistZone'];
    _nearResponseDelay = prefs.getDouble('pid_nearResponseDelay') ?? defaults['nearResponseDelay'];
    _nearMaxAdjustment = prefs.getDouble('pid_nearMaxAdjustment') ?? defaults['nearMaxAdjustment'];
    _farFactor = prefs.getDouble('pid_farFactor') ?? defaults['farFactor'];
    _yAxisFactor = prefs.getDouble('pid_yAxisFactor') ?? defaults['yAxisFactor'];
    _pidRandomFactor = prefs.getDouble('pid_pidRandomFactor') ?? defaults['pidRandomFactor'];
    
    _username = prefs.getString('pid_username') ?? _username;
    _gameName = prefs.getString('pid_gameName') ?? _gameName;
    _createdAt = prefs.getString('pid_createdAt') ?? '';
    _updatedAt = prefs.getString('pid_updatedAt') ?? '';
    
    if (_createdAt.isEmpty) {
      _createdAt = DateTime.now().toIso8601String();
      _updatedAt = _createdAt;
    }
    
    // 延迟通知监听器，避免在构建期间调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
  
  /// 保存设置到持久化存储
  Future<void> saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setDouble('pid_nearMoveFactor', _nearMoveFactor);
    await prefs.setDouble('pid_nearStabilizer', _nearStabilizer);
    await prefs.setDouble('pid_nearResponseRate', _nearResponseRate);
    await prefs.setDouble('pid_nearAssistZone', _nearAssistZone);
    await prefs.setDouble('pid_nearResponseDelay', _nearResponseDelay);
    await prefs.setDouble('pid_nearMaxAdjustment', _nearMaxAdjustment);
    await prefs.setDouble('pid_farFactor', _farFactor);
    await prefs.setDouble('pid_yAxisFactor', _yAxisFactor);
    await prefs.setDouble('pid_pidRandomFactor', _pidRandomFactor);
    
    await prefs.setString('pid_username', _username);
    await prefs.setString('pid_gameName', _gameName);
    await prefs.setString('pid_createdAt', _createdAt);
    await prefs.setString('pid_updatedAt', _updatedAt);
  }
  
  /// 更新用户和游戏信息
  void updateUserGameInfo(String username, String gameName) {
    _username = username;
    _gameName = gameName;
    _updateTimestamp();
    // 延迟通知监听器，避免在构建期间调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
  
  /// 从JSON获取配置
  void fromJson(Map<String, dynamic> json) {
    final content = json['content'];
    if (content == null) return;
    
    if (content['nearMoveFactor'] != null) {
      _nearMoveFactor = _parseDoubleValue(content['nearMoveFactor']);
    }
    
    if (content['nearStabilizer'] != null) {
      _nearStabilizer = _parseDoubleValue(content['nearStabilizer']);
    }
    
    if (content['nearResponseRate'] != null) {
      _nearResponseRate = _parseDoubleValue(content['nearResponseRate']);
    }
    
    if (content['nearAssistZone'] != null) {
      _nearAssistZone = _parseDoubleValue(content['nearAssistZone']);
    }
    
    if (content['nearResponseDelay'] != null) {
      _nearResponseDelay = _parseDoubleValue(content['nearResponseDelay']);
    }
    
    if (content['nearMaxAdjustment'] != null) {
      _nearMaxAdjustment = _parseDoubleValue(content['nearMaxAdjustment']);
    }
    
    if (content['farFactor'] != null) {
      _farFactor = _parseDoubleValue(content['farFactor']);
    }
    
    if (content['yAxisFactor'] != null) {
      _yAxisFactor = _parseDoubleValue(content['yAxisFactor']);
    }
    
    if (content['pidRandomFactor'] != null) {
      _pidRandomFactor = _parseDoubleValue(content['pidRandomFactor']);
    }
    
    if (content['username'] != null) _username = content['username'].toString();
    if (content['gameName'] != null) _gameName = content['gameName'].toString();
    if (content['createdAt'] != null) _createdAt = content['createdAt'].toString();
    if (content['updatedAt'] != null) _updatedAt = content['updatedAt'].toString();
    
    // 延迟通知监听器，避免在构建期间调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
  
  /// 安全解析double值
  double _parseDoubleValue(dynamic value) {
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return 0.0;
      }
    }
    return 0.0;
  }

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'action': 'pid_modify',
      'content': {
        'username': _username,
        'gameName': _gameName,
        'nearMoveFactor': _nearMoveFactor,
        'nearStabilizer': _nearStabilizer,
        'nearResponseRate': _nearResponseRate,
        'nearAssistZone': _nearAssistZone,
        'nearResponseDelay': _nearResponseDelay,
        'nearMaxAdjustment': _nearMaxAdjustment,
        'farFactor': _farFactor,
        'yAxisFactor': _yAxisFactor,
        'pidRandomFactor': _pidRandomFactor,
        'updatedAt': _updatedAt,
      }
    };
  }
}